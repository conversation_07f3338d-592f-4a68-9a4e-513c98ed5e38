<?php

namespace App\Services;

use App\Models\StoreLeads\Domain;
use App\Models\StoreLeads\DomainCluster;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class DomainClusterService
{
    /**
     * Check if agency has any domains in the cluster
     */
    public function hasExistingAgencyDomainInCluster(int $domainId, int $agencyId): bool
    {
        return DB::table('sl_agency_domains')
            ->where('agency_id', $agencyId)
            ->whereIn('domain_id', function($query) use ($domainId) {
                $query->select('related_domain_id')
                    ->from('sl_domain_clusters')
                    ->where('domain_id', $domainId)
                    ->union(
                        DB::table('sl_domain_clusters')
                            ->select('domain_id')
                            ->where('related_domain_id', $domainId)
                    );
            })
            ->exists();
    }

    /**
     * Create cluster relationships for a domain
     */
    public function createClusterRelationships(Domain $domain): void
    {
        $clusterDomains = collect($domain->domainData->data['cluster_domains'] ?? [])
            ->map(fn($d) => strtolower(trim($d)))
            ->filter(fn($d) => !empty($d) && $d !== strtolower($domain->name));
// todo skip specific subdomains of domains like wpengine.com
        if ($clusterDomains->isEmpty()) {
            return;
        }

        $isOriginalDomainBestRanked = $this->isDomainBestRankedInCluster($domain);

        $relatedDomains = Domain::whereIn('name', $clusterDomains)
            ->where('id', '!=', $domain->id)
            ->dontCache()
            ->get();

        if ($relatedDomains->isEmpty()) {
            return;
        }

        DB::transaction(function () use ($domain, $relatedDomains, $isOriginalDomainBestRanked) {
            // Create forward relationships
            DomainCluster::insertOrIgnore(
                $relatedDomains->map(fn ($relatedDomain) => [
                    'domain_id' => $domain->id,
                    'related_domain_id' => $relatedDomain->id,
                    'is_best_ranked' => $isOriginalDomainBestRanked
                ])->toArray()
            );

            // Create reciprocal relationships
            DomainCluster::insertOrIgnore(
                $relatedDomains->map(fn ($relatedDomain) => [
                    'domain_id' => $relatedDomain->id,
                    'related_domain_id' => $domain->id,
                    'is_best_ranked' => $this->isDomainBestRankedInCluster($relatedDomain)
                ])->toArray()
            );
        });
    }

    /**
     * Check if a domain is marked as best ranked in its cluster
     */
    private function isDomainBestRankedInCluster(Domain $domain): bool
    {
        return !empty($domain->domainData->data['cluster_best_ranked']) &&
            strtolower($domain->domainData->data['cluster_best_ranked']) === strtolower($domain->name);
    }
}

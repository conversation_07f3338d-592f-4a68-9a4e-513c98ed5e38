<?php

namespace App\Services;

use App\Events\Journey\JourneyCreated;
use App\Events\Journey\StepCompleted;
use App\Models\Journey\Journey;
use App\Models\Journey\JourneyProgress;
use App\Models\Journey\JourneyStep;
use App\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class JourneyService
{
    public function createJourney(User $user, Journey $journey)
    {
        Log::info("Creating journey: {$journey->slug} user: {$user->email}");

        DB::connection('mysql::write')->beginTransaction();

        try {
            if ($user->journeys()->where('journey_id', $journey->id)->exists()) {
                throw new \Exception('User already has this journey');
            }
            $user->journeys()->attach($journey->id, [
                'started_at' => now(),
                'is_active' => true,
            ]);
            $user->journeys()->where('journey_id', '!=', $journey->id)->each(function ($journey) {
                $journey->pivot->is_active = false;
                $journey->pivot->save();
            });

            $journeySteps = $journey->journeySteps()->active()->orderBy('order_number')->get();
            foreach ($journeySteps as $step) {
                JourneyProgress::create([
                    'user_id' => $user->id,
                    'journey_id' => $journey->id,
                    'journey_step_id' => $step->id,
                    'order_number' => $step->order_number,
                ]);
            }

            DB::connection('mysql::write')->commit();

        } catch (\Throwable $e) {
            Log::error("Failed to create journey: {$journey->slug} user: {$user->email}");
            DB::connection('mysql::write')->rollBack();
            throw $e;
        }

        event(new JourneyCreated($user, $journey));
    }

    public function completeStep(User $user, $stepSlug)
    {
        info('Complete journey step: ' . $stepSlug .' for user: ' . $user->email);

        // Fetch the active journey for the user
        $journey = $this->getActiveJourney($user);

        if (empty($journey)) {
            info('No active journey found. Checking agency owner.');
            if ($user->agency->owner_id != $user->id) {
                $user = $user->agency->owner;
                $journey = $this->getActiveJourney($user);
            }
        }

        if (empty($journey)) {
            info('No active journey found');
            return; // No active journey
        }

        // Find the step within the journey by slug
        $journeyStep = JourneyStep::where('journey_id', $journey->id)
            ->where('slug', $stepSlug)
            ->first();

        if ($journeyStep) {
            // Update progress entry as completed
            $progress = JourneyProgress::where('user_id', $user->id)
                ->where('journey_id', $journey->id)
                ->where('journey_step_id', $journeyStep->id)
                ->first();

            if ($progress) {
                $progress->completed_at = now();
                $progress->save();
                info('Step completed: ' . $stepSlug .' for user: ' . $user->email);

                // Fire the event that a step was completed
                event(new StepCompleted($user, $journeyStep));
            }
        }
    }

    public function getActiveJourney(User $user)
    {
        return $user->journeys()->wherePivot('is_active', true)->first();
    }

    public function getCurrentStep(User $user)
    {
        // Fetch the active journey for the user
        $journey = $this->getActiveJourney($user);

        if (empty($journey)) {
            return null; // No active journey
        }

        // Fetch active journey progress
        $currentStep = JourneyProgress::where('user_id', $user->id)
            ->whereNull('completed_at')
            ->whereHas('journeyStep', function ($query) {
                $query->where('is_alert', true); // Only steps with alerts
            })
            ->orderBy('order_number')
            ->first();

        return $currentStep ? $currentStep->journeyStep : null;
    }

    public function completeJourney(User $user, Journey $journey)
    {
        if (!$user->journeys()->where('journey_id', $journey->id)->exists()) {
            return; // User does not have this journey
        }

        $user->journeys()->updateExistingPivot($journey->id, [
            'completed_at' => now(),
            'is_active' => false,
        ]);
    }
}

<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;
use App\Repositories\CacheableAgency;

class CheckLinkedinAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (Auth::user()->can('support')) {
            return $next($request);
        }
        
        $agencyCache = new CacheableAgency;
        $agency = $agencyCache->find(Auth::user()->agency_id);

        if (!$agency->is_linkedin_enabled) {
            return redirect(route('linkedin-access-request.create'));
        }

        return $next($request);
    }
}

<?php

namespace App\Http\Controllers;

use App\Campaign;
use App\Prospect;
use App\Services\JourneyService;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Jobs\UpdateDuplicateProspects;
use App\Jobs\ProcessProspectsFromImport;
use Illuminate\Support\Facades\Validator;

class ProspectImportController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware(['auth']);
    }

    /**
     * Saves the list of prospects to the database
     * once the user has re-mapped the "CSV headers" to "Database Fields"
     * We will save it to database.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Campaign  $campaign
     * @return \Illuminate\Http\Response
     */
    public function process(Request $request, Campaign $campaign)
    {
        DB::connection()->disableQueryLog();

        // get the campaign with its snippets
        $campaign->load('snippets');

        $arrSnippets = $campaign->snippets; // custom fields of campaign from "snippets" table
        $arrProspects = []; // prospect array from request
        $arrProspectEmails = [];
        $arrValidProspects = [];
        $arrInvalidProspects = [];
        $arrCampaignDuplicates = [];
        $arrOverrideDuplicates = [];
        $arrActiveDuplicates = [];
        $arrDoneDuplicates = [];
        $arrCsvDuplicates = [];
        $defaultFields = [
            'email', 'first_name', 'last_name', 'company', 'industry', 'website',
            'title', 'phone', 'address', 'city', 'state', 'country',
        ];  // this is the default fields from the "prospects" table

        // loop thru prospects to format each prospect
        foreach ($request->prospects as $prospect) {
            $objProspect = [];
            $mergeFields = [];

            // loop thru the fields to construct the prospect object
            foreach ($request->fields as $key => $value) {
                if($value){
                    if ($arrSnippets->where('name', $value)->first()) {
                        // if this field value exist in the snippets,
                        // then this value will be added to "mergeFields" array
                        $mergeFields[$value] = isset($prospect[$key]) ? $prospect[$key] : null;
                    } else {
                        // if not, this value goes to the default fields of prospect object
                        $objProspect[$value] = isset($prospect[$key]) ? $prospect[$key] : null;
                    }
                }
            }

            // immediately check the prospect's email
            $validateProspectFields = Validator::make($objProspect, [
                'email'      => 'bail|required|email:strict,filter|max:255',
                // 'first_name' => 'required',
                // 'last_name'  => 'required',
            ]);

            // Make email lowercase
            $objProspect['email'] = strtolower($objProspect['email']);

            // check if the prospect has an "email" field
            if ($validateProspectFields->fails()) {
                $prospect['error_message'] = implode(' ', $validateProspectFields->errors()->all());
                array_push($arrInvalidProspects, $prospect);
            } else {
                // test in CSV duplicates
                $listedProspect = Arr::where($arrProspects, function ($value, $key) use ($objProspect) {
                    return isset($value['email']) && $value['email'] == $objProspect['email'];
                });

                if (count($listedProspect)) {
                    array_push($arrCsvDuplicates, $prospect);
                } else {
                    // then assign the "mergeFields" array as value of 'merge_fields'
                    $objProspect['merge_fields'] = count($mergeFields) ? json_encode($mergeFields) : null;
                    $objProspect['campaign_id'] = $campaign->id;
                    $objProspect['team_id'] = $campaign->team_id;
                    // $objProspect['api_id'] = 0;
                    $objProspect['agency_id'] = $campaign->agency_id;
                    $objProspect['timezone'] = $campaign->timezone;
                    $objProspect['status'] = 'OK';
                    $objProspect['contact_id'] = 1; // preserve contact:1 to be dummy contact for new prospects
                    $objProspect['import_file'] = $request->file_name;
                    $objProspect['orig_data_from_csv'] = $prospect;
                    $objProspect['created_at'] = Carbon::now(); // bulk insert doesn't add timestamps
                    $objProspect['updated_at'] = Carbon::now(); // bulk insert doesn't add timestamps
                    $objProspect['domain'] = Str::after($objProspect['email'], '@'); // Build domain field

                    // format the "linkedin_slug" to get only the hash part
                    if(!empty($objProspect['linkedin_slug'])){
                        $objProspect['linkedin_hash'] = parseLinkedinProfileUrl($objProspect['linkedin_slug']);
                        unset($objProspect['linkedin_slug']);
                    }

                    if(!empty($objProspect['linkedin_profile_open'])
                        && in_array($objProspect['linkedin_profile_open'], ['yes', 'true', '1'])
                    ) {
                        $objProspect['profile_free_open'] = 1;
                    } else {
                        $objProspect['profile_free_open'] = 0;
                    }

                    unset($objProspect['linkedin_profile_open']);

                    // when fields are formatted, add this prospect object to the prospects array
                    array_push($arrProspects, $objProspect);
                    array_push($arrProspectEmails, $objProspect['email']);
                }
            }
        }

        // fetch client's active campaigns
        /*
        $activeCampaigns = $campaign->team->owner->teams
            ->map(function ($team) {
                return $team->campaigns()->get()->pluck('id');
            })->flatten()->toArray();
        */
        $activeCampaigns = $campaign->team->campaigns()->get()->pluck('id')->toArray();

        // include this campaign's id - probably Archived so it's not included in Active list
        // array_push($activeCampaigns, $campaign->id);

        $colProspects =  Prospect::selectRAW('id, team_id, campaign_id, LOWER(email) as email, import_file')
            ->whereIn('campaign_id', $activeCampaigns)
            ->whereIn('email', $arrProspectEmails)
            ->with(['campaign'])
            ->get();

        if (count($arrProspects)) {
            // loop thru the array of formatted prospects for duplicate checking
            foreach ($arrProspects as $formattedProspect) {
                // prospect could be duplicate in completed and this current campaign
                // needs to detect both and prioritize duplicate in this current campaign
                $exactDuplicate = $colProspects->where('email', $formattedProspect['email'])
                    ->where('campaign_id', $campaign->id)->first();
                $otherDuplicates = $colProspects->where('email', $formattedProspect['email'])
                    ->where('campaign_id', '!=', $campaign->id)->first();

                $origDataProspect = $formattedProspect['orig_data_from_csv'];
                unset($formattedProspect['orig_data_from_csv']);

                if ($exactDuplicate) {
                    if ($exactDuplicate->import_file != $request->file_name){
                        $newData = Arr::except($formattedProspect, [
                            'email', 'interested', 'status', 'contact_id', 'team_id', 'agency_id',
                            'created_at', 'updated_at', 'import_file', 'timezone'
                        ]);

                        $newData['prospect_id'] = $exactDuplicate->id;
                        array_push($arrOverrideDuplicates, $newData);
                        array_push($arrCampaignDuplicates, $formattedProspect);
                    } else {
                        array_push($arrCsvDuplicates, $formattedProspect);
                    }
                } elseif ($otherDuplicates) {
                    $isCampaignDone = in_array(
                        data_get($otherDuplicates, 'campaign.status'),
                        ['COMPLETED', 'ARCHIVED']
                    );

                    if($isCampaignDone) {
                        $origDataProspect['campaign_name'] = $otherDuplicates->campaign->name;
                        array_push($arrDoneDuplicates, $origDataProspect);

                        // if import prospects from "done" campaigins is "checked"
                        // just add the prospect in valid array
                        if($request->import_duplicate) {
                            array_push($arrValidProspects, $formattedProspect);
                        }

                    } else {
                        array_push($arrActiveDuplicates, $formattedProspect);
                    }
                } else {
                    array_push($arrValidProspects, $formattedProspect);
                }
            }
        }

        if(count($arrValidProspects)) {
            $prospectCollection = collect($arrValidProspects);

            // bulk insert by 2500 to avoid "1390 Prepared statement contains too many placeholders" error on mysql
            foreach ($prospectCollection->chunk(2500) as $prospectChunk) {
                Prospect::insert($prospectChunk->toArray());
            }

            // update campaign's status
            if (in_array($campaign->status, ['COMPLETED', 'ARCHIVED'])) {
                $campaign->status = 'DRAFT';
                $campaign->archived_at = null;
                $campaign->save();
            }

        }

        // run a job to override existing prospects
        if($request->override) {
            foreach (array_chunk($arrOverrideDuplicates, 2500) as $prospects) {
                DB::table('prospects_temp')->insert($prospects);
            }
        }

        // run a job to search those new prospects(contact_id=0) and fix contact_id
        if ($request->last_chunk) {
            if ($request->override) {
                Bus::chain([
                    new UpdateDuplicateProspects($campaign),
                    new ProcessProspectsFromImport($campaign),
                ])->onQueue('default')->dispatch();
            } else {
                ProcessProspectsFromImport::dispatch($campaign)->onQueue('default')->delay(10);
            }
        }

        if (count($arrValidProspects)) {
            $journeyService = new JourneyService();
            $journeyService->completeStep(Auth::user(), 'campaign_contacts_added');
        }

        return response()->json([
            'status'   => 'success',
            'message'  => 'Prospects has been added',
            'prospects' => count($arrValidProspects),
            'invalids' => $arrInvalidProspects,
            'campaignduplicates' => $arrCampaignDuplicates,
            'activeduplicates' => $arrActiveDuplicates,
            'doneduplicates' => $arrDoneDuplicates,
            'csvduplicates' => $arrCsvDuplicates
        ]);
    }
}

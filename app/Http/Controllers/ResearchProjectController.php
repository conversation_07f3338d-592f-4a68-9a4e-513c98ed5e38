<?php

namespace App\Http\Controllers;

use App\Agency;
use App\Exceptions\StripeException;
use App\Jobs\Research\NotifyOfResearchProject;
use App\ResearchMessage;
use App\ResearchProject;
use App\Spark;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class ResearchProjectController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware(['auth','can:research']);
    }

    /**
     * Display a list of research projects.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function index(Request $request)
    {
        $agencies = Auth::user()->getAccessibleAgencies();
        $agency = Auth::user()->agency->load(['owner']);

        // Find billable user.
        $subscriber = $agency->owner;
        // Check if subscriber has attached payment method
        if (!$subscriber->pm_type || !$subscriber->pm_last_four) {
            $needsPayment = true;
        } else {
            $needsPayment = false;
        }

        $researchPlan = Spark::researchPlans()->first();
        $subscriptionPrice = $researchPlan->price;

        $pendingInvoice = null;
        if (!is_null($subscriber->failed_research_charge)) {
            $invoices = $subscriber->invoicesIncludingPending();
            foreach ($invoices as $invoice) {
                if ($invoice->attempted && !$invoice->paid && $invoice->status == 'open') {
                    $pendingInvoice = $invoice->asStripeInvoice();
                    break;
                }
            }
        }

        return view('research-projects.index', compact([
            'agencies', 'agency', 'subscriptionPrice', 'needsPayment', 'pendingInvoice'
        ]));
    }

    public function list(Request $request)
    {
        $agencyId = decodeModelHashid(Agency::class, $request->input('agency_id', null));
        $keywords = $request->input('keywords', null);
        $status = $request->input('status', 'NOT_IS_ARCHIVED');
        $sorting = $request->input('sorting', 'NEWEST');

        $projects = ResearchProject::ofAgency($agencyId);

        $projects = ResearchProject::with([
            'researchMessages' => function ($query) {
                $query->orderBy('created_at', 'desc')
                    ->with('user:id,name,photo_url');
            },
            'agency:id,name',
            'researchProjectWeeks.researchProjectHours'
        ])
            //->with('agency:id,name','researchMessages.user:id,name,photo_url')
            ->ofAgency($agencyId)
            ->ofStatus($status)
            ->ofKeywords($keywords);

        switch ($sorting) {
            case 'STATUS':
                $projects = $projects->orderBy('is_archived') // archived last
                    ->orderBy('is_active', 'desc') // active first
                    ->orderBy('is_draft', 'desc') // draft first
                    ->orderByRaw('completed_at is not null') // cancelled before completed
                    ->orderBy('in_grace_period', 'desc') // show those in grace period before other stopped projects
                    ->orderBy('name', 'asc'); // alpha sort
                break;
            case 'AGENCY_MESSAGE':
                $projects = $projects->orderByDesc(
                    ResearchMessage::select('id')
                        ->whereColumn('research_project_id', 'research_projects.id')
                        ->where('is_report', false)
                        ->orderBy('id', 'desc')
                        ->limit(1)
                )->orderBy('name', 'asc');
                break;
            case 'STAFF_MESSAGE':
                $projects = $projects->orderByDesc(
                    ResearchMessage::select('id')
                        ->whereColumn('research_project_id', 'research_projects.id')
                        ->where('is_report', true)
                        ->orderBy('id', 'desc')
                        ->limit(1)
                )->orderBy('name', 'asc');
                break;
            case 'ALPHA':
                $projects = $projects->orderBy('name');
                break;
            case 'NEWEST':
                Log::info('sort newest');
                $projects = $projects->orderBy('id', 'desc');
                break;
            case 'OLDEST':
                Log::info('sort oldest');
                $projects = $projects->orderBy('id');
                break;
        }

        $projects = $projects->paginate(10);

        if (!is_null($agencyId)) {
            $researchWeek = Agency::find($agencyId)->researchWeeks()->latest()->first();
            $runningProjectCount = Agency::find($agencyId)->researchProjects()->running()->count();
            if ($researchWeek && now('US/Eastern')->greaterThan(Carbon::parse($researchWeek->week_end)->addDay())) {
                $researchWeek = null;
                $hasHistory = true;
            } elseif (Agency::find($agencyId)->researchWeeks()->count() > 1) {
                $hasHistory = true;
            } else {
                $hasHistory = false;
            }
        } else {
            $researchWeek = null;
            $runningProjectCount = null;
            $hasHistory = false;
        }

        return response()->json([
            'status' => 'success',
            'research_projects' => $projects,
            'research_week' => $researchWeek,
            'running_project_count' => $runningProjectCount,
            'has_history' => $hasHistory
        ]);
    }

    public function create()
    {

    }

    public function store(Request $request)
    {
        $this->authorize('create', ResearchProject::class);

        // First we need to increment the subscription or create a new one.
        $this->validate($request, [
            'name' => 'required|string',
            'description' => 'required|string'
        ]);

        if ($request['is_draft']) {
            $projectStart = null;
        } else {
            $projectStart = now();
        }

        $researchProject = ResearchProject::create([
            'name' => $request['name'],
            'description' => $request['description'],
            'agency_id' => Auth::user()->agency->id,
            'started_at' => $projectStart,
            'is_draft' => $request['is_draft']
        ]);

        NotifyOfResearchProject::dispatch($researchProject, 'project-created');

        // If it's a draft then we don't increment the subscription and set no initial hours.
        if ($researchProject->is_draft) {

            return response()->json([
                'research_project' => $researchProject->fresh()->toArray(),
            ]);
        }

        try {
            $subscription = $researchProject->incrementSubscription();
        } catch (\Exception $e) {
            Log::error('Got an error when incrementing a research subscription with the following message: '. $e->getMessage());
            Log::info('deleting project');
            $researchProject->delete();
            $user = Auth::user()->agency->owner;
            $researchSubscription = $user->subscription('research');
            if ($researchSubscription && !$researchSubscription->canceled()) {
                Log::info('updating research subscription quantity after failed activation');
                $researchSubscription->updateQuantity($user->agency->researchProjects()->active()->count());
            }

            if (is_a($e, StripeException::class)) {

                return response()->json([
                    'billing_error' => true,
                    'message' => $e->getMessage(),
                ], 422);
            }

            return response()->json([
                'subscription_error' => true,
                'message' => $e->getMessage()
            ], 422);
        }

        $researchProject->setInitialHours($subscription);

        return response()->json([
            'research_project' => $researchProject->fresh()->toArray(),
        ]);
    }

    public function update(ResearchProject $researchProject, Request $request)
    {
        $this->authorize('update', $researchProject);

        $this->validate($request, [
            'name' => 'required|string',
            'description' => 'required|string'
        ]);

        if ($researchProject->is_edit_locked && Auth::user()->cannot('updateResearchStatus', $researchProject)) {

            return response()->json([
                'lock_error' => true,
                'message' => 'Project Locked',
                'research_project' => $researchProject->fresh()->toArray(),
            ], 422);
        }

        $researchProject->update([
            'name' => $request['name'],
            'description' => $request['description'],
        ]);

        return response()->json([
            'research_project' => $researchProject->fresh()->toArray(),
        ]);
    }

    public function updateResearcherStatus(ResearchProject $researchProject, Request $request)
    {
        $this->authorize('updateResearchStatus', $researchProject);

        $this->validate($request, [
            'status' => [
                'required',
                Rule::in(['green', 'yellow', 'red']),
            ],
        ]);

        $researchProject->update([
            'researcher_status' => $request['status'],
        ]);

        return response()->json([
            'research_project' => $researchProject->fresh()->toArray(),
        ]);
    }

    public function lock(ResearchProject $researchProject, Request $request)
    {
        $this->authorize('updateResearchStatus', $researchProject);

        $researchProject->update(['is_edit_locked' => true]);

        return response()->json([
            'research_project' => $researchProject->fresh()->toArray(),
        ]);
    }

    public function unlock(ResearchProject $researchProject, Request $request)
    {
        $this->authorize('updateResearchStatus', $researchProject);

        $researchProject->update(['is_edit_locked' => false]);

        return response()->json([
            'research_project' => $researchProject->fresh()->toArray(),
        ]);
    }

    public function cancel(ResearchProject $researchProject, Request $request)
    {
        $this->authorize('updateStatus', $researchProject);

        $keepWorking = $request['keep_working'];

        if ($researchProject->cancel($keepWorking)) {
            return response()->json([
                'research_project' => $researchProject->fresh()->toArray(),
            ]);
        }

        return response()->json([
            'message' => 'There was an error when cancelling the lead research project.'
        ], 422);
    }

    public function activate(ResearchProject $researchProject, Request $request)
    {
        $this->authorize('updateStatus', $researchProject);

        if ($researchProject->activate()) {
            return response()->json([
                'research_project' => $researchProject->fresh()->toArray(),
            ]);
        }

        return response()->json([
            'message' => 'There was an error when re-activating the lead research project.'
        ], 422);
    }

    public function complete(ResearchProject $researchProject, Request $request)
    {
        $this->authorize('updateStatus', $researchProject);

        if ($researchProject->complete()) {

            return response()->json([
                'research_project' => $researchProject->fresh()->toArray(),
            ]);
        }

        return response()->json([
            'message' => 'There was an error when updating the lead research project status to completed.'
        ], 422);
    }

    public function archive(ResearchProject $researchProject, Request $request)
    {
        $this->authorize('updateStatus', $researchProject);

        if ($researchProject->archive()) {

            return response()->json([
                'research_project' => $researchProject->fresh()->toArray(),
            ]);
        }

        return response()->json([
            'message' => 'There was an error when updating the lead research project status to archived.'
        ], 422);
    }

    public function duplicate(ResearchProject $researchProject, Request $request, $action = null)
    {
        $this->authorize('updateStatus', $researchProject);

        $newProject = $researchProject->duplicate($action);

        if ($newProject) {

            return response()->json([
                'research_project' => $newProject->fresh()
                    ->load([
                        'researchMessages' => function ($query) {
                            $query->orderBy('created_at', 'desc');
                        },
                        'agency:id,name',
                        'researchMessages.user:id,name,photo_url'
                    ])->toArray(),
            ]);
        }

        return response()->json([
            'message' => 'There was an error when updating the research project.'
        ], 422);
    }
}

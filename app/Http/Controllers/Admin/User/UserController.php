<?php

namespace App\Http\Controllers\Admin\User;

use App\User;
use Illuminate\Http\Request;
use Achillesp\CrudForms\CrudForms;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class UserController extends Controller
{
    // TODO: Integrate with Kiosk!!

    use CrudForms;

    /**
     * UserController constructor.
     *
     * @param User $user
     * @param Request $request
     */
    public function __construct(User $user, Request $request)
    {
        $this->middleware(['auth', 'can:support']);

        $this->model = $user;

        $this->formFields = [
            ['name' => 'company', 'label' => 'Company Name', 'type' => 'text'],
            ['name' => 'name', 'label' => 'Name', 'type' => 'text'],
            ['name' => 'email', 'label' => 'E-mail', 'type' => 'email'],
            ['name' => 'password', 'label' => 'Password', 'type' => 'password'],
            ['name' => 'agency_id', 'label' => 'Agency', 'type' => 'select', 'relationship' => 'agency'],
            ['name' => 'roles', 'label' => 'Roles', 'type' => 'checkbox_multiple', 'relationship' => 'roles'],
        ];

        $this->indexFields = ['name', 'email', 'agency_id', 'roles'];

        $this->validationRules = [
            'name'          => 'required|string|max:255',
            'email'         => 'required|email|unique:users,email,'.$request->user,
            'password'      => 'required|string|max:255',
            'agency_id'     => 'required|integer',
        ];
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if ($this->withTrashed) {
            $entities = $this->model->withTrashed()->get();
        } else {
            $entities = $this->model->all();
        }

        $this->loadModelRelationships($entities);

        $fields = $this->getIndexFields();
        $title = $this->getFormTitle();
        $route = $this->getRoute();
        $withTrashed = $this->withTrashed;
        $bladeLayout = $this->bladeLayout;

        return view('admin.user.index',
            compact(
                'entities',
                'fields',
                'title',
                'route',
                'withTrashed',
                'bladeLayout'
            )
        );
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(),
            $this->getValidationRules(),
            $this->getValidationMessages(),
            $this->getValidationAttributes()
        )->validate();

//        $entity = $this->model->create($request->all());

        $entity = new User();

        $entity->forceFill([
            'company'   => $request['company'],
            'name'      => $request['name'],
            'email'     => $request['email'],
            'password'  => bcrypt($request['password']),
            'agency_id' => $request['agency_id'],
        ])->save();

        // Only admins should be able to change roles/permissions.
        if (Auth::user()->can('admin')) {
            $this->syncModelRelationships($entity, $request);
        }

        $request->session()->flash('status', 'Data saved successfully!');

        return redirect(route($this->getRoute().'.index'));
    }

    /**
     * Show a user's details.
     *
     * @param User $user
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function show(User $user)
    {
        $user->load(['agency', 'roles', 'teams']);

        return view('admin.user.show', compact('user'));
    }

    public function edit(User $user)
    {
        $entity = $user;

        if (Auth::user()->cannot('admin')) {
            $this->formFields = [
                ['name' => 'company', 'label' => 'Company Name', 'type' => 'text'],
                ['name' => 'name', 'label' => 'Name', 'type' => 'text'],
                ['name' => 'email', 'label' => 'E-mail', 'type' => 'email'],
                ['name' => 'password', 'label' => 'Password', 'type' => 'password'],
                ['name' => 'agency_id', 'label' => 'Agency', 'type' => 'select', 'relationship' => 'agency'],
            ];
        }

        $this->loadModelRelationships($entity);

        $relationshipOptions = $this->getModelRelationshipData();

        $fields = $this->getFormFields();
        $title = $this->getFormTitle();
        $route = $this->getRoute();
        $bladeLayout = $this->bladeLayout;

        return view('crud-forms::edit',
            compact(
                'entity',
                'fields',
                'title',
                'route',
                'bladeLayout',
                'relationshipOptions'
            )
        );
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param $id
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        Validator::make($request->all(),
            $this->getValidationRules(),
            $this->getValidationMessages(),
            $this->getValidationAttributes()
        )->validate();

        $entity = $this->model->findOrFail($id);

        // Handle checkboxes
        foreach ($this->getFormFields() as $field) {
            if ('checkbox' == $field['type']) {
                $request["{$field['name']}"] = ($request["{$field['name']}"]) ? true : false;
            }
        }

        $entity->update($request->all());

        // Only admins should be able to change roles/permissions.
        if (Auth::user()->can('admin')) {
            $this->syncModelRelationships($entity, $request);
        }

        $request->session()->flash('status', 'Data saved.');

        return redirect(route($this->getRoute().'.show', $id));
    }
}

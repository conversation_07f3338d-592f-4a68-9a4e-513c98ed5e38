<?php

namespace App\Policies;

use App\User;
use App\Campaign;
use Illuminate\Auth\Access\HandlesAuthorization;

class CampaignPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can create prospects.
     *
     * @param  \App\User $user
     * @return mixed
     */
    public function create(User $user)
    {
        // Only agency admins and huron simple clients can create Campaigns.
        if ($user->hasRole('agency-admin') || $user->agency->bills_customers) {
            return true;
        }

        if ($user->can('campaign.admin') || $user->can('campaign.create')) {
            return true;
        }

        if ($user->can('support')) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can view the prospect.
     *
     * @param  \App\User $user
     * @param  \App\Campaign $campaign
     * @return mixed
     */
    public function read(User $user, Campaign $campaign)
    {
        return ($user->can('campaign.admin') && $user->onTeam($campaign->team))
            || ($user->can('campaign.read') && $user->onTeam($campaign->team))
            || ($user->hasRole('agency-admin') && $user->agency_id == $campaign->agency_id)
            || $user->can('support');
    }

    /**
     * Determine whether the user can update the prospect.
     *
     * @param  \App\User $user
     * @param  \App\Campaign $campaign
     * @return mixed
     */
    public function update(User $user, Campaign $campaign)
    {
        return ($user->can('campaign.admin') && $user->onTeam($campaign->team))
            || ($user->can('campaign.update') && $user->onTeam($campaign->team))
            || ($user->hasRole('agency-admin') && $user->agency_id == $campaign->agency_id)
            || $user->can('support');
    }

    /**
     * Determine whether the user can delete the prospect.
     *
     * @param  \App\User $user
     * @param  \App\Campaign $campaign
     * @return mixed
     */
    public function delete(User $user, Campaign $campaign)
    {
        return ($user->can('campaign.admin') && $user->onTeam($campaign->team))
            || ($user->can('campaign.delete') && $user->onTeam($campaign->team))
            || ($user->hasRole('agency-admin') && $user->agency_id == $campaign->agency_id)
            || $user->can('support');
    }
}


<?php

namespace App;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class Schedule extends Model
{
    protected $guarded = ['id'];

    protected $casts = [
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
    ];

    /**
     * Find active schedules for a specific date and time.
     *
     * @param $query
     * @param null $time
     * @return mixed
     */
    public function scopeActive($query, $time = null)
    {
        if (null === $time) {
            $time = Carbon::now();
        }

        return $query->where('starts_at', '<=', $time)->where('ends_at', '>=', $time);
    }

    /**
     * Find schedules that their running time has passed.
     *
     * @param $query
     * @param null $time
     * @return mixed
     */
    public function scopePassed($query, $time = null)
    {
        if (null === $time) {
            $time = Carbon::now();
        }

        return $query->where('ends_at', '<', $time);
    }

    /**
     * Find schedules of a specific day of week.
     * Use shorthand for day (e.g. mon, tue, wed, thu, fri, sat, sun).
     *
     * @param $query
     * @param string $day
     * @return mixed
     */
    public function scopeOfDay($query, $day)
    {
        return $query->where('day', $day);
    }

    /*
     * Find schedules that have not reached the daily send limit.
     *
     * @param $query
     * @return mixed
     */
    public function scopeIncomplete($query)
    {
        return $query->whereRaw('amount_sent < amount_to_send');
    }

    /*
     * Find schedules that have reached the daily send limit.
     *
     * @param $query
     * @return mixed
     */
    public function scopeComplete($query)
    {
        return $query->whereRaw('amount_sent >= amount_to_send');
    }

    /**
     * Find schedules that have not been put into dormant state.
     * Schedules can become dormant if they cannot find any prospects.
     *
     * @param $query
     * @return mixed
     */
    public function scopeAwake($query, $time = null)
    {
        if (null === $time) {
            $time = Carbon::now();
        }

        return $query->where('is_dormant', false)
            ->orWhere(function($query) use ($time) {
                $query->whereNotNull('dormant_until')
                    ->where('dormant_until', '<', $time);
            });
    }

    /**
     * Schedules that meet all conditions to send messages.
     *
     * @param $query
     * @param null $time
     * @return mixed
     */
    public function scopeCanSend($query, $time = null)
    {
        return $query->active($time)->incomplete()->awake($time);
    }

    /**
     * Filter schedules by campaigns.
     *
     * @param mixed $query
     * @param mixed $campaignIds
     * @return mixed $query
     */
    public function scopeOfCampaigns($query, $campaignIds)
    {
        if ($campaignIds !== null) {
            return $query->whereIn('campaign_id', $campaignIds);
        }

        return $query;
    }

    /**
     * Filter schedules by email_account_id.
     *
     * @param mixed $query
     * @param int $emailAccountId
     * @return mixed $query
     */
    public function scopeOfEmailAccount($query, $emailAccountId)
    {
        if ($emailAccountId) {
            return $query->where('email_account_id', $emailAccountId);
        }

        return $query;
    }

    /**
     * Filter schedules by linkedin_account_id.
     *
     * @param mixed $query
     * @param int $linkedinAccountId
     * @return mixed $query
     */
    public function scopeOfLinkedinAccount($query, $linkedinAccountId)
    {
        if ($linkedinAccountId) {
            return $query->where('linkedin_account_id', $linkedinAccountId);
        }

        return $query;
    }

    public function emailTemplate()
    {
        return $this->belongsTo(EmailTemplate::class);
    }

    public function linkedinMessageTemplate()
    {
        return $this->belongsTo(LinkedinMessageTemplate::class);
    }

    public function campaignStage()
    {
        return $this->belongsTo(CampaignStage::class);
    }

    public function emailAccount()
    {
        return $this->belongsTo(EmailAccount::class);
    }

    public function linkedinAccount()
    {
        return $this->belongsTo(LinkedinAccount::class);
    }

    public function campaign()
    {
        return $this->belongsTo(Campaign::class);
    }

    public function scheduledProspects()
    {
        return $this->hasMany(ScheduledProspect::class);
    }

    /**
     * Check if the schedule has completed today's emails.
     *
     * @return bool
     */
    public function isComplete()
    {
        return $this->amount_sent >= $this->amount_to_send;
    }

    /**
     * Check if this schedule is ready to send based on schedule wait time.
     * We also check queued status in case an email is queued but sending is delayed.
     *
     * @param Carbon/Carbon or String $time
     * @return bool
     */
    public function isReadyToSend($time = null)
    {
        if (null === $time) {
            $time = Carbon::now();
        } elseif (is_string($time)) {
            $time = Carbon::parse($time);
        }

        // Check if it is time to send and we have not already pushed(queued) this for sending.
        // We need to check queued status in case the email sending is delayed.
        if (!$this->queued && (null === $this->last_sent_at || $time->gte($this->nextSendTime()))) {
            return true;
        }

        return false;
    }

    /**
     * Get the optimal next send time for this schedule, based on wait time and last send time.
     *
     * @return Carbon
     */
    public function nextSendTime()
    {
        return Carbon::parse($this->starts_at)->addSeconds($this->wait_time * $this->amount_sent);
//        return Carbon::parse($this->last_sent_at)->addSeconds($this->wait_time);
    }
}

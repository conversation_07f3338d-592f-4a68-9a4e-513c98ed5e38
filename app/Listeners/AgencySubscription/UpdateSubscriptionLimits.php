<?php

namespace App\Listeners\AgencySubscription;

use App\Events\AgencySubscription\AgencySubscribed;
use App\Events\AgencySubscription\AgencySubscriptionCancelled;
use App\Events\AgencySubscription\AgencySubscriptionRenewed;
use App\Events\AgencySubscription\AgencySubscriptionUpdated;
use App\Services\Wavo3CreditsBillingService;
use App\Spark;
use Carbon\Carbon;

class UpdateSubscriptionLimits
{
    /**
     * Handle the event.
     *
     * @param  mixed  $event
     * @return void
     */
    public function handle($event)
    {
        // Only applicable to wavo version 3 agencies
        if ($event->agency->wavo_version != 3) {
            return;
        }

        $creditService = new Wavo3CreditsBillingService($event->agency);

        if ($event instanceof AgencySubscribed) {
            if ($event->plan->hasContactsLimit()) {
                $contactsLimit = $event->plan->attributes['es_contacts_limit'];
            } else {
                $contactsLimit = null;
            }
            if ($event->plan->hasAccountsLimit()) {
                $accountsLimit = $event->plan->attributes['email_account_limit'];
            } else {
                $accountsLimit = null;
            }

            $event->agency->update([
                'es_contacts_limit' => $contactsLimit,
                'email_account_limit' => $accountsLimit,
                'uses_fresh_es_data' => $event->plan->attributes['uses_fresh_es_data'] ?? false,
                'ai_fields' => $event->plan->attributes['ai_fields'] ?? 0,
                'billing_next_date' => Carbon::parse($event->agency->subscription()->asStripeSubscription()->current_period_end),
            ]);

            if (!empty($contactsLimit)) {
                $creditService->incrementCredits($contactsLimit, 'Subscription Created (' . $event->plan->name . ' Plan)');
            }

        } elseif ($event instanceof AgencySubscriptionCancelled) {
            if ($event->agency->subscription()->ended()) {
                // end of subscription. reset any remaining credits
                $event->agency->update([
                    'es_contacts_limit' => 0,
                    'email_account_limit' => 0,
                    'uses_fresh_es_data' => 0,
                    'ai_fields' => 0,
                    'billing_next_date' => null,
                ]);
            }

        } elseif ($event instanceof AgencySubscriptionUpdated) {
            // Only need to change limits on switch. On resume do nothing
            if ($event->oldPlanId != $event->newPlanId) {
                $oldPlan = Spark::agencyPlans()->where('id', $event->oldPlanId)->first();
                $newPlan = Spark::agencyPlans()->where('id', $event->newPlanId)->first();

                if (!$newPlan->hasContactsLimit()) {
                    $contactsLimit = null;
                } else {
                    $contactsLimit = $newPlan->attributes['es_contacts_limit'];

                    if ($oldPlan->id == 'free-ec' || $oldPlan->id == 'free' || $oldPlan->id == 'free-ec2') {
                        // Free plan to paid plan. Add the new plan credits
                        $creditService->incrementCredits($contactsLimit, 'Subscription Plan upgrade to ' . $newPlan->name);
                    } elseif ($newPlan->attributes['es_contacts_limit'] > $oldPlan->attributes['es_contacts_limit']) {
                        // New plan has higher limit. Add the difference from the old plan to current credits
                        $newCredits = $newPlan->attributes['es_contacts_limit'] - $oldPlan->attributes['es_contacts_limit'];
                        $creditService->incrementCredits($newCredits, 'Subscription Plan upgrade to ' . $newPlan->name);
                    }
                }

                if ($newPlan->hasAccountsLimit()) {
                    $accountsLimit = $newPlan->attributes['email_account_limit'];
                } else {
                    $accountsLimit = null;
                }

                $event->agency->update([
                    'es_contacts_limit' => $contactsLimit,
                    'email_account_limit' => $accountsLimit,
                    'uses_fresh_es_data' => $newPlan->attributes['uses_fresh_es_data'] ?? false,
                    'ai_fields' => $newPlan->attributes['ai_fields'] ?? 0,
                    'billing_next_date' => Carbon::parse($event->agency->subscription()->asStripeSubscription()->current_period_end),
                ]);
            }
        } elseif ($event instanceof AgencySubscriptionRenewed) {
            $plan = Spark::agencyPlans()->where('id', $event->planId)->first();
            $contactsLimit = $plan->attributes['es_contacts_limit'];
            if (!empty($event->agency->es_contacts_limit)) {
                $newCredits = $contactsLimit;
                $creditService->incrementCredits($newCredits, 'Subscription Renewed (' . $plan->name . ' Plan)');
            }
            $event->agency->update([
                'es_contacts_limit' => $contactsLimit,
                'billing_next_date' => Carbon::parse($event->agency->subscription()->asStripeSubscription()->current_period_end),
            ]);
        }
    }
}

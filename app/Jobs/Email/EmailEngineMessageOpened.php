<?php

namespace App\Jobs\Email;

use App\EmailMessage;
use App\Events\EmailMessage\ProspectOpenedEmail;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class EmailEngineMessageOpened implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The message id that we get from the Nylas webhook.
     *
     * @var string
     */
    protected $messageId;

    protected const DB_READ_CONNECTION = 'mysql::read';

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($messageId)
    {
        $this->messageId = $messageId;
    }

    /**
     * Get the tags that should be assigned to the job.
     *
     * @return array
     */
    public function tags()
    {
        return ['email-api', 'message-opened', 'email-message-'.$this->messageId];
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::channel('emailengine')->info('EmailEngine message.opened webhook:'.$this->messageId);

        try {
            $emailMessage = EmailMessage::on(self::DB_READ_CONNECTION)
                ->where('id', $this->messageId)->first();

            if ($emailMessage && null === $emailMessage->opened_at) {
                $emailMessage->opened_at = Carbon::now();
                $emailMessage->is_opened = true;
                $emailMessage->save();

                event(new ProspectOpenedEmail($emailMessage));
            }
        } catch (\Exception $e) {
            Log::info('EmailEngine error in message.opened of messageId:' . $this->messageId);
            Log::error($e->getMessage());
        }
    }
}

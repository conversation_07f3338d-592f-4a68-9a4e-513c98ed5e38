<?php

namespace App\Jobs\Email;

use App\EmailMessage;
use App\Services\EmailIntegrationServices\EmailIntegrationFactory;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ArchiveMessage implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;

    public $timeout = 300;
    public $backoff = 30;

    protected $messageId;
    protected $emailAccount;
    protected $api;

    protected const DB_READ_CONNECTION = 'mysql::read';

    /**
     * Create a new job instance.
     *
     * @param $messageId
     */
    public function __construct($messageId)
    {
        $this->messageId = $messageId;
    }

    /**
     * Get the tags that should be assigned to the job.
     *
     * @return array
     */
    public function tags()
    {
        return ['email-api', 'archive-message', 'message-'.$this->messageId];
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $emailMessage = EmailMessage::on(self::DB_READ_CONNECTION)
            ->where('nylas_message_id', $this->messageId)
            ->orWhere('message_id', $this->messageId)
            ->with('emailAccount')
            ->first();

        if (is_null($emailMessage)) {
            Log::error('Cannot archive message. Not found message id: '.$this->messageId);

            return;
        }

        $factory = new EmailIntegrationFactory;
        $this->emailAccount = $emailMessage->emailAccount;
        $this->api = $factory->make($this->emailAccount);
        $messageId = $this->emailAccount->integration_type == 'ee' ? $emailMessage->ee_id : $this->messageId;


        $folder = $this->getArchiveFolder();

        if (!isset($folder) || is_null($folder)) {
            // No archive folder exists in db. Update folders and find/create archive folder.
            $folder = $this->findUpdatedArchiveFolder();
            $this->api->moveMessage($messageId, $folder->folder_id);
        } else {
            // There's an archive folder in db.
            // Try to move message and if it fails update folder info and retry.
            try {
                $this->api->moveMessage($messageId, $folder->folder_id);
            } catch (\Exception $e) {
                $folder = $this->findUpdatedArchiveFolder();
                $this->api->moveMessage($messageId, $folder->folder_id);
            }
        }

        Log::channel('nylas')->info('Archived message id: '.$this->messageId);
    }

    /**
     * Get email folder info and update db for this email account.
     */
    protected function getEmailFolders()
    {
        $folders = $this->api->getFolders();
        if ($folders !== false && !is_null($folders)) {
            $folders = $folders->map(function($folder) {
                return [
                    'folder_id' => $folder['id'],
                    'name' => $folder['name'],
                    'display_name' => $folder['display_name'],
                    'type' => $folder['object']
                ];
            });
            $this->emailAccount->emailFolders()->delete();
            $this->emailAccount->emailFolders()->createMany($folders);
        }
    }

    /**
     * Get this account's archive folder from our local DB.
     *
     * @return mixed
     */
    protected function getArchiveFolder()
    {
        return $this->emailAccount->emailFolders()
            ->where(function ($query) {
                $query->whereIn('name', ['archive', 'Archive'])
                    ->orWhereIn('display_name', [
                        'archive',
                        'Archive',
                        'auto archive',
                        'Auto Archive',
                        '[Imap]/archive',
                        '[Imap]/Archive'
                    ]);
            })
            ->first();
    }

    /**
     * Update this account's folder data and find or create the archive folder.
     *
     * @return bool|mixed
     */
    protected function findUpdatedArchiveFolder()
    {
        $this->getEmailFolders(); // Resync folder info in case the archive folder is not existing on our records.
        $folder = $this->getArchiveFolder();

        if (!isset($folder) || is_null($folder)) {
            // If we still can't find it, try to create it
            $folder = $this->api->createFolder('Archive');

            if ($folder == false || is_null($folder)) {
                Log::error('Cannot archive message. Archive folder not found and cannot be created. Message id: '.$this->messageId);
                $this->fail(new \Exception('Unable to create archive folder'));

                return false;
            }
        }

        return $folder;
    }
}

<?php

namespace App\Jobs\Linkedin;

use App\Campaign;
use App\CampaignStage;
use App\Exceptions\LinkedinPuppeteerBrowser\ElementNotFoundException;
use App\Exceptions\LinkedinPuppeteerBrowser\LinkedinAccountException;
use App\Exceptions\LinkedinPuppeteerBrowser\ProxyException;
use App\LinkedinMessage;
use App\LinkedinMessageTemplate;
use App\LinkedinThread;
use App\Prospect;
use App\ProspectActivity;
use App\Schedule;
use App\Services\CampaignSchedulerService;
use App\Services\LinkedinPuppeteerBrowserService;
use App\Services\LinkedinThreadService;
use App\Traits\LogsMessages;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;

/**
 * Send a LinkedIn connect request to a prospect based on a schedule
 */
class SendLinkedinConnectRequest implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, LogsMessages;

    public $timeout = 0;
    public $tries = 5;

    private $prospect;
    private $schedule;
    private $linkedinAccount;
    private $linkedinMessageTemplate;
    private $messageText;
    private $campaign;
    private $campaignStage;
    private $page;
    private $browserService;
    private $proxy = 'datacenter';
    private $testMode = false;
    private $linkedinThreadService;
    private $skipConnectionMsg = false;

    protected const DB_READ_CONNECTION = 'mysql::read';

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Prospect $prospect, Schedule $schedule)
    {
        $this->prospect = $prospect;
        $this->schedule = $schedule;
        $this->linkedinAccount = $this->schedule->linkedinAccount;
        $this->logChannel = 'linkedin';
        $this->logPrefix = "SendLinkedinConnectRequest-{$this->prospect->id}. ";
    }

    public function tags()
    {
        return [
            'linkedin',
            'linkedin-send-connect-request',
            'linkedin-send-connect-request-'.$this->schedule->linkedin_message_template_id,
            'linkedin-account-'.$this->schedule->linkedin_account_id
        ];
    }

    public function middleware()
    {
        return [
            (new WithoutOverlapping("linkedin-account-id:{$this->linkedinAccount->id}"))
                ->shared()
                ->expireAfter(180)
                ->releaseAfter(185),
        ];
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws \Throwable
     */
    public function handle()
    {
        if (! $this->validatePrerequisites()) {

            return;
        }

        $this->linkedinMessageTemplate = LinkedinMessageTemplate::on(static::DB_READ_CONNECTION)
            ->find($this->schedule->linkedin_message_template_id);
        $this->campaignStage = CampaignStage::on(static::DB_READ_CONNECTION)->find($this->schedule->campaign_stage_id);
        $this->campaign = Campaign::on(static::DB_READ_CONNECTION)->find($this->schedule->campaign_id);
        $this->messageText = $this->linkedinMessageTemplate->parseMessageContent($this->prospect, $this->linkedinAccount);
        $this->linkedinThreadService = new LinkedinThreadService($this->linkedinAccount);
        $this->skipConnectionMsg = $this->campaignStage->skip_connection_msg;

        $this->logInfo('Starting Linkedin Connect Request');
        $connectStatus = $this->sendConnectRequest();
        $this->logInfo('sendConnectRequest DONE. Status: '.$connectStatus);

        if ($connectStatus) {
            if($connectStatus === 'PENDING' || $connectStatus === 'CONNECT-DEMO') {
                $this->logInfo('Connect request sent. Updating prospect');
                $this->sendingCompleted($this->saveConnectRequest()); // This also releases locks.
            } else {
                $this->logInfo('Connect request skipped. Status: '.$connectStatus);
                $this->sendingCompleted(null, $connectStatus);
            }


        } else {
            // No message sent. Release locks and exit.
            $this->releaseLocks();
        }

        if (config('app.linkedin.send')) {
            $this->browserService->closeBrowser();
            sleep(2);
        }
    }

    public function failed(\Throwable $exception)
    {
        $this->logError('Connect request failed. '.$exception->getMessage());
        $this->releaseLocks();
    }

    protected function validatePrerequisites()
    {
        if (is_null($this->prospect->linkedin_slug)) {
            $this->logInfo('No valid linkedin slug for this prospect.');
            $this->releaseLocks();

            return false;
        }

        if (! $this->linkedinAccount || $this->linkedinAccount->status != 'ACTIVE') {
            $this->logInfo('No active linkedin account. Stopping message send.');
            $this->releaseLocks();

            return false;
        }

        // Check if the contact is connected // TODO: Do we really need this?
        if ($this->prospect->linkedin_connect_status == 'ACCEPTED' || ! $this->prospect->status == 'OK') {
            $this->logInfo('Prospect is not ready to receive any connect request.');
            $this->releaseLocks();

            return false;
        }

        $previousMessages = $this->prospect->linkedinMessages()->exists();
        if ($previousMessages) {
            $this->logError('Contact is already contacted on LinkedIn. Cannot send connect request');
            $this->releaseLocks();

            return false;
        }

        return true;
    }

    protected function sendConnectRequest()
    {
        if (! config('app.linkedin.send')) {
            return 'CONNECT-DEMO';
        }

        if($this->prospect->profile_free_open) {
            return 'OPEN';
        }

        $connectStatus = '';

        try {
            $this->browserService = new LinkedinPuppeteerBrowserService($this->linkedinAccount, $this->proxy, $this->testMode);
            $this->page = $this->browserService->launchBrowser();
            // First visit profile to get public url if not done already
            if (is_null($this->prospect->linkedin_public_url) || is_null($this->prospect->linkedin_hash_miniprofile)) {
                $this->logInfo('Visit prospect LI profile');
                $this->page = $this->browserService->visitProfile($this->prospect);
            }

            /*
             * no need to check message because connection request with note
             * doesnt create a thread until prospect accept
             *
            // Get this prospect's thread in case we have old messages
            $thread = $this->browserService->getProspectThread($this->prospect);
            if ($thread) {
                $this->logInfo('Parse thread: '.$thread['id']);

                // LINKEDIN OUTREACH TODO:
                // can assign prospect with "forProspect"?
                $this->linkedinThreadService->forSync()->parseRawThread($thread);
            }
            */

            $connectStatus = $this->browserService->sendConnectRequest($this->prospect, $this->messageText, $this->skipConnectionMsg);

        } catch (ProxyException | ElementNotFoundException $e) {
            $this->logError($e->getMessage());

        } catch (LinkedinAccountException $e) {
            $this->logError("Connect request stopped. ".$e->getMessage());

        } catch (\Throwable $e) {
            // Propagate unhandled error to retry job.
            $this->logError('Unhandled exception. '.$e->getMessage());
            $this->browserService->closeBrowser();
            sleep(2);

            throw($e);
        }

        return $connectStatus;
    }

    protected function saveConnectRequest()
    {
        if (! config('app.linkedin.send')) {
            return $this->saveDemoRequest();
        }

        $this->logInfo('Could not find any threads. Save thread and message with placeholder IDs');

        $msgText = $this->skipConnectionMsg ? 'Connection request sent without a note' : $this->messageText;

        // Create placeholder thread and message until the connect request is accepted
        $linkedinThread = LinkedinThread::create([
            'linkedin_thread_hash' => 'connect-pending',
            'agency_id' => $this->linkedinAccount->agency_id,
            'linkedin_account_id' => $this->linkedinAccount->id,
            'campaign_id' => $this->campaignStage->campaign_id,
            'prospect_id' => $this->prospect->id,
            'thread_date' => now(),
        ]);
        $linkedinMessage = LinkedinMessage::create([
            'agency_id' => $this->linkedinAccount->agency_id,
            'linkedin_account_id' => $this->linkedinAccount->id,
            'campaign_id' => $this->prospect->campaign_id,
            'prospect_id' => $this->prospect->id,
            'linkedin_thread_id' => $linkedinThread->id,
            'linkedin_message_template_id' => $this->linkedinMessageTemplate->id,
            'is_connect_request' => true,
            'linkedin_message_hash' => 'connect-pending',
            'origin' => 'self',
            'status' => 'OK',
            'message' => $msgText,
            'submitted_at' => now(),
        ]);

        return $linkedinMessage ?? null;
    }

    protected function saveConnectRequestWithThread()
    {
        if (! config('app.linkedin.send')) {
            return $this->saveDemoRequest();
        }

        try {
            // Find message in messaging tab
            $this->page->waitForTimeout(3000);
            $this->page = $this->browserService->goHome();
            $this->page->waitForTimeout(3000);

            // LINKEDIN OUTREACH TODO:
            // we should not get message/thread after sending connection requests
            // because connection message will not appear in the threads until accepted
            $thread = $this->browserService->getProspectThread($this->prospect);
        } catch (ProxyException | ElementNotFoundException $e) {
            $this->logError($e->getMessage());
        } catch (LinkedinAccountException $e) {
            $this->logError("Error fetching prospect thread. ".$e->getMessage());
        } catch (\Throwable $e) {
            // Propagate unhandled error to retry job.
            $this->logError('Unhandled exception. '.$e->getMessage());
        }

        if (! empty($thread)) {
            $this->linkedinThreadService->forProspect($this->prospect)->saveConnectRequest($thread);
            $linkedinMessage = $this->prospect->linkedinMessages()->where('is_connect_request', true)->whereNull('linkedin_message_template_id')->orderByDesc('id')->first();
        } else {
            $this->logInfo('Could not find any threads. Save thread and message with placeholder IDs');

            /*
             * TODO:
             * message for already [Pending] connection, a request outside wavo platform
             * message for [Accept] where profile was the one requested for connection
            */

            // Create placeholder thread and message until the connect request is accepted
            $linkedinThread = LinkedinThread::create([
                'linkedin_thread_hash' => 'connect-pending',
                'agency_id' => $this->linkedinAccount->agency_id,
                'linkedin_account_id' => $this->linkedinAccount->id,
                'campaign_id' => $this->campaignStage->campaign_id,
                'prospect_id' => $this->prospect->id,
                'thread_date' => now(),
            ]);
            $linkedinMessage = LinkedinMessage::create([
                'agency_id' => $this->linkedinAccount->agency_id,
                'linkedin_account_id' => $this->linkedinAccount->id,
                'campaign_id' => $this->prospect->campaign_id,
                'prospect_id' => $this->prospect->id,
                'linkedin_thread_id' => $linkedinThread->id,
                'linkedin_message_template_id' => $this->linkedinMessageTemplate->id,
                'is_connect_request' => true,
                'linkedin_message_hash' => 'connect-pending',
                'origin' => 'self',
                'status' => 'OK',
                'message' => $this->messageText,
                'submitted_at' => now(),
            ]);
        }

        return $linkedinMessage ?? null;
    }

    protected function saveDemoRequest()
    {
        $this->logInfo('Save demo connect request');

        // Create placeholder thread and message until the connect request is accepted
        $linkedinThread = LinkedinThread::create([
            'linkedin_thread_hash' => 'demo-'.$this->prospect->id.'-'.rand(0,10000),
            'agency_id' => $this->linkedinAccount->agency_id,
            'linkedin_account_id' => $this->linkedinAccount->id,
            'campaign_id' => $this->campaignStage->campaign_id,
            'prospect_id' => $this->prospect->id,
            'thread_date' => now(),
        ]);
        $linkedinMessage = LinkedinMessage::create([
            'agency_id' => $this->linkedinAccount->agency_id,
            'linkedin_account_id' => $this->linkedinAccount->id,
            'campaign_id' => $this->prospect->campaign_id,
            'prospect_id' => $this->prospect->id,
            'linkedin_thread_id' => $linkedinThread->id,
            'linkedin_message_template_id' => $this->linkedinMessageTemplate->id,
            'is_connect_request' => true,
            'linkedin_message_hash' => 'demo-'.$this->prospect->id.'-'.rand(0,10000),
            'from_name' => $this->linkedinAccount->name,
            'to_name' => $this->prospect->first_name . ' ' . $this->prospect->last_name,
            'origin' => 'self',
            'status' => 'OK',
            'message' => $this->messageText,
            'submitted_at' => now(),
        ]);

        return $linkedinMessage;
    }

    protected function sendingCompleted($linkedinMessage, $liConnectStatus='PENDING')
    {
        $this->linkedinAccount->incrementInvitesSent();

        app('App\Services\CampaignStatsManager')
            ->handleCompletedOutreach($this->linkedinMessageTemplate->fresh(), $this->prospect);

        // Release locks while updating schedule and prospect
        $this->schedule->last_sent_at = now();
        $this->schedule->amount_sent++;
        $this->schedule->queued = false;
        $this->schedule->save();

        $this->prospect->update([
            'scheduled_linkedin_message_template_id' => null,
            'schedule_id' => null,
            'last_contacted' => $linkedinMessage ? $linkedinMessage->submitted_at : now(),
            'timezone' => $this->campaign->timezone,
            'linkedin_messages_sent' => $this->prospect->linkedin_messages_sent + 1,
            'completed_steps' => $this->campaignStage->number,
            'next_step' => $this->campaignStage->number + 1,
            'last_linkedin_step' => $this->campaignStage->number,
            'linkedin_connect_status' => $liConnectStatus,
            'wait_until' => now()->addSeconds($this->campaignStage->wait_time),
        ]);

        // Log Prospect activity
        ProspectActivity::create([
            'prospect_id' => $this->prospect->id,
            'linkedin_message_id' => $linkedinMessage ? $linkedinMessage->id : null,
            'activity' => 'LinkedinConnectRequestSent',
        ]);

        // Wake up next step schedule if available
        $campaignScheduler = new CampaignSchedulerService($this->campaign);
        $campaignScheduler->wakeNextSchedules($this->schedule);
    }
    protected function releaseLocks()
    {
        if (! is_null($this->schedule)) {
            $this->schedule->update(['queued' => false]);
        }

        if (! is_null($this->prospect)) {
            if ($this->prospect->linkedinMessages()->count() == 0) {
                $this->prospect->update([
                    'linkedin_account_id' => null,
                    'scheduled_linkedin_message_template_id' => null,
                    'schedule_id' => null,
                ]);
            } else {
                $this->prospect->update([
                    'scheduled_linkedin_message_template_id' => null,
                    'schedule_id' => null,
                ]);
            }
        }
    }
}

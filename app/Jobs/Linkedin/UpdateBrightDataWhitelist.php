<?php

namespace App\Jobs\Linkedin;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use GuzzleHttp\Client as HttpClient;
use Illuminate\Support\Facades\Log;


class UpdateBrightDataWhitelist implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 180;
    public $backoff = 30;

    protected $http;

    /**
     * Create a new job instance.
     *
     */
    public function __construct()
    {
        //
    }

    public function tags()  {
        return ['linkedin-api', 'update-brightdata-whitelist'];
    }

    /**
     * Execute the job thru puphpeteer.
     *
     * @return void
     */
    public function handle()
    {
        $this->http = new HttpClient();
        $testServers = [
            'http://ifconfig.so',
            'http://ifconfig.me',
            'http://ipecho.net/plain'
        ];
        foreach($testServers as $testServer) {
            $response = $this->http->get('ifconfig.so');
            $ip = $response->getBody()->getContents();
            if (!is_null($ip)) {
                break;
            }
        }

        if (is_null($ip)) {

            throw(new \Exception('BrightData-API: Could not detect chrome-worker pod IP!'));
        }

        // Update BrightData whitelist
        Log::info('BrightData-API: Updating BrightData whitelist with chrome-worker pod IP:'.$ip);

        $response = $this->http->post(
            'https://brightdata.com/api/zone/whitelist',
            [
                \GuzzleHttp\RequestOptions::JSON => ['ip' => $ip],
                'headers' => [
                    'Authorization' => 'Bearer '.config('app.luminati.token')
                ]
            ]
        );

        if ($response->getStatusCode() == 200 || $response->getStatusCode() == 204) {
            Log::info('BrightData-API: Whitelisted IP:'.$ip);
        } else {
            Log::error('BrightData-API: Could not whitelist IP:'.$ip);
            Log::error('BrightData-API: Got response: '.$response->getStatusCode().' '.$response->getReasonPhrase());

            throw(new \Exception('BrightData-API: Could not whitelist IP on BrightData. Got response '.$response->getStatusCode()));
        }
    }
}

<?php

namespace App\Models\Journey;

use App\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class JourneyProgress extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'completed_at' => 'datetime',
    ];

    public function journeyStep()
    {
        return $this->belongsTo(JourneyStep::class, 'journey_step_id');
    }

    public function journey()
    {
        return $this->belongsTo(Journey::class, 'journey_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}

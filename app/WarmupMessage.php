<?php

namespace App;

use App\Traits\Hashidable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WarmupMessage extends Model
{
    use Hashidable, HasFactory;

    const STATUSES = [
        'PENDING',      // wavo process the schedule
        'SENT',         // received messageNew with account is sender
        'COMPLETED',    // received messageNew with account is receiver and processed
    ];

    protected $guarded = ['id'];

    protected $appends = ['hashid'];

    /**
     * Sender account of a message.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function emailAccount()
    {
        return $this->belongsTo(EmailAccount::class);
    }

    public function warmupThread()
    {
        return $this->belongsTo(WarmupThread::class);
    }

    public function recipientAccount()
    {
        return $this->belongsTo(EmailAccount::class, 'recipient_account_id', 'id');
    }

    public function agency()
    {
        return $this->belongsTo(Agency::class);
    }

    public function scopeOfEmailAccount($query, $emailAccountId)
    {
        if ($emailAccountId) {
            return $query->where('email_account_id', $emailAccountId);
        }

        return $query;
    }

    public function scopeOfRecipientAccount($query, $recipientAccountId)
    {
        if ($recipientAccountId) {
            return $query->where('recipient_account_id', $recipientAccountId);
        }

        return $query;
    }
}

<?php

namespace App\Console\Commands\Jobs;

use App\EmailAccount;
use App\Jobs\Email\CheckEmailSchedules;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CheckSchedulesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'jobs:check-schedules';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Dispatch jobs to run schedules of active email accounts if not already running.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $accountsQueued = getPendingJobTagIds('schedule', 'App\Jobs\Email\CheckEmailSchedules', 'email-account-');

        EmailAccount::ofStatus('active')->notFrozen()->withRunningCampaigns()
            ->whereHas('schedules', function ($query) {
                $query->active();
            })
            ->get()
            ->each(function ($emailAccount) use ($accountsQueued) {
                if (! $accountsQueued->contains($emailAccount->id)) {
                    Log::channel('emailengine')->info('Dispatch schedule checking for email account: '.$emailAccount->id);
                    CheckEmailSchedules::dispatch($emailAccount->id)->onQueue('schedule');
                }
            });
    }
}

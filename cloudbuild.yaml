steps:
  # Pull the latest build to make building faster
#  - name: 'gcr.io/cloud-builders/docker'
#    entrypoint: 'bash'
#    args:
#      - '-c'
#      - 'docker pull gcr.io/$PROJECT_ID/wavo-app:v3-$BRANCH_NAME-latest || exit 0'
  # Build new docker image
  - name: gcr.io/cloud-builders/docker
    args:
      - 'build'
      - '-t'
      - 'gcr.io/$PROJECT_ID/wavo-app:v3-$BRANCH_NAME-$COMMIT_SHA'
      - '-t'
      - 'gcr.io/$PROJECT_ID/wavo-app:v3-$BRANCH_NAME-latest'
      - '--cache-from'
      - 'gcr.io/$PROJECT_ID/wavo-app:v3-$BRANCH_NAME-latest'
      - '-f'
      - './docker/app/Dockerfile'
      - '.'
    env:
      - 'CLOUDSDK_COMPUTE_REGION=us-east1'
      - 'CLOUDSDK_COMPUTE_ZONE=us-east1-c'
  # Push image to make it available immediately
  - name: gcr.io/cloud-builders/docker
    args:
    - 'push'
    - 'gcr.io/$PROJECT_ID/wavo-app:v3-$BRANCH_NAME-$COMMIT_SHA'
    env:
      - 'CLOUDSDK_COMPUTE_REGION=us-east1'
      - 'CLOUDSDK_COMPUTE_ZONE=us-east1-c'
  - name: gcr.io/cloud-builders/docker
    args:
    - 'push'
    - 'gcr.io/$PROJECT_ID/wavo-app:v3-$BRANCH_NAME-latest'
    env:
      - 'CLOUDSDK_COMPUTE_REGION=us-east1'
      - 'CLOUDSDK_COMPUTE_ZONE=us-east1-c'
  # Apply k8s configMap
  - name: 'gcr.io/cloud-builders/kubectl'
    args:
      - 'apply'
      - '-f'
      - 'k8s/${_K8S_ENV}/${_K8S_ENV}-env-config.yaml'
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=us-east1-c'
      - 'CLOUDSDK_CONTAINER_CLUSTER=wavo-cluster-1'
  # Delete migration job to re-run it on next step
#  - name: 'gcr.io/cloud-builders/kubectl'
#    args:
#      - 'delete'
#      - '-f'
#      - 'k8s/${_K8S_ENV}/build/app/db-migrate-job.yaml'
#    env:
#      - 'CLOUDSDK_COMPUTE_ZONE=us-east1-c'
#      - 'CLOUDSDK_CONTAINER_CLUSTER=wavo-cluster-1'
  # Apply k8s deployments
  - name: 'gcr.io/cloud-builders/kubectl'
    args:
      - 'apply'
      - '-f'
      - 'k8s/${_K8S_ENV}/build'
      - '--recursive=true'
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=us-east1-c'
      - 'CLOUDSDK_CONTAINER_CLUSTER=wavo-cluster-1'
  # Update docker image used by frontend deployments
  - name: 'gcr.io/cloud-builders/kubectl'
    args:
      - '--namespace=wavo-${_K8S_ENV}'
      - 'set'
      - 'image'
      - 'deployment'
      - '${_K8S_ENV}-frontend-deployment'
      - '${_K8S_ENV}-frontend=gcr.io/$PROJECT_ID/wavo-app:v3-$BRANCH_NAME-$COMMIT_SHA'
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=us-east1-c'
      - 'CLOUDSDK_CONTAINER_CLUSTER=wavo-cluster-1'
  # Update docker image used by worker deployments
  - name: 'gcr.io/cloud-builders/kubectl'
    args:
      - '--namespace=wavo-${_K8S_ENV}'
      - 'set'
      - 'image'
      - 'deployment'
      - '${_K8S_ENV}-worker-deployment'
      - '${_K8S_ENV}-worker=gcr.io/$PROJECT_ID/wavo-app:v3-$BRANCH_NAME-$COMMIT_SHA'
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=us-east1-c'
      - 'CLOUDSDK_CONTAINER_CLUSTER=wavo-cluster-1'
  # Update docker image used by cron scheduler
#  - name: 'gcr.io/cloud-builders/kubectl'
#    args:
#      - '--namespace=wavo-${_K8S_ENV}'
#      - 'set'
#      - 'image'
#      - 'cronjob'
#      - '${_K8S_ENV}-schedule-cron-job'
#      - '${_K8S_ENV}-schedule-cron-job=gcr.io/$PROJECT_ID/wavo-app:v3-$BRANCH_NAME-$COMMIT_SHA'
#    env:
#      - 'CLOUDSDK_COMPUTE_ZONE=us-east1-c'
#      - 'CLOUDSDK_CONTAINER_CLUSTER=wavo-cluster-1'
# Push images to Google Container Registry with tags
#images:
#  - 'gcr.io/$PROJECT_ID/wavo-app:v3-$BRANCH_NAME-$COMMIT_SHA'
#  - 'gcr.io/$PROJECT_ID/wavo-app:v3-$BRANCH_NAME-latest'
substitutions:
  _K8S_ENV: production
options:
  pool:
    name: 'projects/$PROJECT_ID/locations/us-east1/workerPools/build-pool-e2s4'
  logging: CLOUD_LOGGING_ONLY

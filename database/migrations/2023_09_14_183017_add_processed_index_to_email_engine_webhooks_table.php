<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('email_engine_webhooks', function (Blueprint $table) {
            $table->index('processed', 'idx_eew_processed');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('email_engine_webhooks', function (Blueprint $table) {
            $table->dropIndex('idx_eew_processed');
        });
    }
};

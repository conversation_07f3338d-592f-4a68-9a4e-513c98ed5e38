<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDailyStatsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('daily_stats', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedInteger('agency_id');
            $table->unsignedInteger('team_id');
            $table->unsignedInteger('campaign_id');
            $table->unsignedInteger('email_account_id');
            $table->date('day');
            $table->unsignedInteger('sent')->default(0);
            $table->unsignedInteger('autoreplied')->default(0);
            $table->unsignedInteger('unsubscribed')->default(0);
            $table->unsignedInteger('replied')->default(0);
            $table->unsignedInteger('bounced')->default(0);
            $table->timestamps();

            $table->foreign('agency_id')
                ->references('id')
                ->on('agencies')
                ->onDelete('cascade');

            $table->foreign('team_id')
                ->references('id')
                ->on('teams')
                ->onDelete('cascade');

            $table->foreign('campaign_id')
                ->references('id')
                ->on('campaigns')
                ->onDelete('cascade');

            $table->foreign('email_account_id')
                ->references('id')
                ->on('email_accounts')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('daily_stats');
    }
}

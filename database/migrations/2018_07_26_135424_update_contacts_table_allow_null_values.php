<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class UpdateContactsTableAllowNullValues extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('contacts', function (Blueprint $table) {
            $table->string('company')->nullable()->change();
            $table->string('industry')->nullable()->change();
            $table->string('website')->nullable()->change();
            $table->string('tags')->nullable()->change();
            $table->string('title')->nullable()->change();
            $table->string('phone')->nullable()->change();
            $table->string('address')->nullable()->change();
            $table->string('city')->nullable()->change();
            $table->string('state')->nullable()->change();
            $table->string('country')->nullable()->change();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('contacts', function (Blueprint $table) {
            $table->string('company')->change();
            $table->string('industry')->change();
            $table->string('website')->change();
            $table->string('tags')->change();
            $table->string('title')->change();
            $table->string('phone')->change();
            $table->string('address')->change();
            $table->string('city')->change();
            $table->string('state')->change();
            $table->string('country')->change();
        });
    }
}

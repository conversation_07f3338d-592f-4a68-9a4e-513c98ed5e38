<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddEmailApiKeyToTeamsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('email_api_key');
        });
        Schema::table('teams', function (Blueprint $table) {
            $table->string('email_api_key')->nullable()->after('photo_url')->index();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('teams', function (Blueprint $table) {
            $table->dropColumn('email_api_key');
        });
        Schema::table('users', function (Blueprint $table) {
            $table->string('email_api_key')->nullable()->after('stripe_id');
        });

    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddLinkedinAccountIdToLinkedinSearchesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('linkedin_searches', function (Blueprint $table) {
            $table->integer('linkedin_account_id')->unsigned()->nullable();
            $table->foreign('linkedin_account_id')->references('id')->on('linkedin_accounts');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('linkedin_searches', function (Blueprint $table) {
            $table->dropForeign(['linkedin_account_id']);
            $table->dropColumn(['linkedin_account_id']);
        });
    }
}

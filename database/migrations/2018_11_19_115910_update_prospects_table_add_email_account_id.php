<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class UpdateProspectsTableAddEmailAccountId extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('prospects', function (Blueprint $table) {
            $table->unsignedInteger('email_account_id')->nullable()->index()->after('campaign_id');
            $table->dropIndex(['api_id']);
            $table->dropColumn(['api_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('prospects', function (Blueprint $table) {
            $table->dropIndex(['email_account_id']);
            $table->dropColumn(['email_account_id']);
            $table->unsignedInteger('api_id')->default(0)->index()->after('campaign_id');
        });
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('warmup_daily_stats', function (Blueprint $table) {
            $table->unsignedInteger('server_gsuite_inbox')->default(0);
            $table->unsignedInteger('server_gsuite_categories')->default(0);
            $table->unsignedInteger('server_gsuite_spam')->default(0);

            $table->unsignedInteger('server_office365_inbox')->default(0);
            $table->unsignedInteger('server_office365_categories')->default(0);
            $table->unsignedInteger('server_office365_spam')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('warmup_daily_stats', function (Blueprint $table) {
            $table->dropColumn([
                'server_gsuite_inbox',
                'server_gsuite_categories',
                'server_gsuite_spam',
                'server_office365_inbox',
                'server_office365_categories',
                'server_office365_spam'
            ]);
        });
    }
};

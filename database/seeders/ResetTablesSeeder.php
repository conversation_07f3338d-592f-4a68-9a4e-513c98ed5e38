<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class ResetTablesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Remove foreign key checks.
        DB::statement("SET foreign_key_checks = 0");

        // Truncate all tables, except migrations
        $tables = Schema::getConnection()->getDoctrineSchemaManager()->listTableNames();
        foreach ($tables as $table) {
            if ($table !== 'migrations')
                DB::table($table)->truncate();
        }

        DB::statement("SET foreign_key_checks = 1");
    }
}

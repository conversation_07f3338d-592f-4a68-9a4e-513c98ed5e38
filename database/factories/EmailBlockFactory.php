<?php

namespace Database\Factories;

use App\Campaign;
use Illuminate\Database\Eloquent\Factories\Factory;

class EmailBlockFactory extends Factory
{
    public function definition(): array
    {
        $type = $this->faker->randomElement($array = ['email','email','email','domain']);

        return [
            'team_id' => function (array $attributes) {
                return Campaign::find($attributes['campaign_id'])->team_id;
            },
            'address' => $type == 'email' ? $this->faker->unique()->safeEmail : $this->faker->domainName,
            'type' => $type
        ];
    }
}

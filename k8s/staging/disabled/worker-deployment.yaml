apiVersion: apps/v1
kind: Deployment
metadata:
  name: staging-worker-deployment
  namespace: wavo-staging
spec:
  replicas: 0
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 2
      maxSurge: 1
  selector:
    matchLabels:
      component: staging-worker
  template:
    metadata:
      labels:
        component: staging-worker
    spec:
      serviceAccountName: staging-ingress-sa
      restartPolicy: Always
      tolerations:
        - key: cloud.google.com/gke-preemptible
          operator: Equal
          value: "true"
          effect: NoSchedule
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                - key: node-group
                  operator: In
                  values:
                    - "cheap"
                - key: node-group
                  operator: NotIn
                  values:
                    - "default"
                    - "redis"
                    - "browser"
                    - "elastic"
                    - "emailengine"
      volumes:
        - name: google-cloud-storage-key
          secret:
            secretName: staging-storage
        - name: stackdriver-key
          secret:
            secretName: staging-stackdriver-sa
      containers:
        - name: staging-worker
          image: gcr.io/wavo-225922/wavo-app:v3-dev-latest
          imagePullPolicy: Always
          resources:
            limits:
              cpu: 450m
              memory: 1.3G
            requests:
              cpu: 100m
              memory: 500M
          ports:
            - containerPort: 8080
          lifecycle:
            preStop:
              exec:
                # Gracefully terminate horizon
                command: [ "supervisorctl", "stop", "horizon" ]
          envFrom:
            - configMapRef:
                name: staging-env-config
            - secretRef:
                name: staging-app-secret
          volumeMounts:
            - name: google-cloud-storage-key
              mountPath: /var/secrets/google
            - name: stackdriver-key
              mountPath: /var/secrets/stackdriver
          env:
            - name: GOOGLE_CLOUD_KEY_FILE
              value: /var/secrets/google/key.json
            - name: STACKDRIVER_KEY_FILE
              value: /var/secrets/stackdriver/key.json
            - name: START_FRONTEND
              value: "false"
            - name: START_HORIZON
              value: "true"
      terminationGracePeriodSeconds: 320

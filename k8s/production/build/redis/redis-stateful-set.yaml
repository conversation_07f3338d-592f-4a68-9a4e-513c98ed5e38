apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: production-redis-stateful-set
  namespace: wavo-production
spec:
  replicas: 1
  updateStrategy:
    type: RollingUpdate
  selector:
    matchLabels:
      component: production-redis
  serviceName: "production-redis-cluster-ip-service"
  template:
    metadata:
      labels:
        component: production-redis
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9121"
    spec:
      terminationGracePeriodSeconds: 10
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: node-group
                    operator: In
                    values:
                      - "default"
                  - key: node-group
                    operator: NotIn
                    values:
                      - "redis"
                      - "cheap"
                      - "browser"
                      - "elastic"
                      - "emailengine"
      containers:
        - name: production-redis
          image: redis
          args: ["--save", "3600", "1", "--save", "300", "100", "--save", "60", "10000", "--maxmemory", "5900M"]
          resources:
            limits:
              memory: 6200M
            requests:
              memory: 6000M
              cpu: 200m
          ports:
            - containerPort: 6379
              name: prod-redis
          volumeMounts:
            - name: redis-persistent-storage
              mountPath: "/data"
          livenessProbe:
            tcpSocket:
              port: 6379
            initialDelaySeconds: 15
            periodSeconds: 15
          readinessProbe:
            tcpSocket:
              port: 6379
            initialDelaySeconds: 30
            periodSeconds: 10
        - name: redis-exporter
          image: oliver006/redis_exporter:latest
          resources:
            requests:
              cpu: 100m
              memory: 100M
          ports:
            - containerPort: 9121
      volumes:
        - name: redis-persistent-storage
          persistentVolumeClaim:
            claimName: production-redis-persistent-volume-claim

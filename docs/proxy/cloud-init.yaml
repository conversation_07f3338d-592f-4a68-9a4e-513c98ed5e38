#cloud-config
users:
  - name: ach22p77
    plain_text_passwd: "byEz2Rvi2S2i5d4s"
    lock_passwd: false
    shell: /bin/bash

packages:
  - dante-server
  - ufw

write_files:
  - path: /etc/danted.conf
    content: |
      logoutput: syslog

      internal: eth0 port = 4481
      external: eth0

      method: username
      user.notprivileged: nobody

      client pass {
          from: 0.0.0.0/0 to: 0.0.0.0/0
          log: connect disconnect error
      }

      pass {
          from: 0.0.0.0/0 to: 0.0.0.0/0
          protocol: tcp
          method: username
          log: connect disconnect error
      }

runcmd:
  - systemctl enable danted
  - systemctl restart danted
  - ufw allow 22/tcp
  - ufw allow 4481/tcp
  - ufw --force enable

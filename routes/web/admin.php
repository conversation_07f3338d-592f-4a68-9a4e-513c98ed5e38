<?php
/*
 * Administration routes (for top level admins)
 */

// Impersonation
Route::get('/spark/kiosk', 'Admin\DashboardController@show');
Route::get('/spark/kiosk/users/impersonate/{id}', 'Admin\ImpersonationController@impersonate');
Route::get('/spark/kiosk/users/{id}/profile', 'Admin\ProfileController@show');
Route::post('/spark/kiosk/users/search', 'Admin\SearchController@performBasicSearch');

// Admin routes
Route::prefix('/admin')->name('admin.')->middleware(['auth', 'can:support'])->group(function () use ($pluralTeamString) {
    Route::view('', 'admin.home');

    Route::get('management-report/monthly-trend/{months?}', 'Admin\ManagementReportController@monthlyTrend')->name('management-report.monthly-trend');
    Route::get('management-report/bimonthly-trend/{months?}', 'Admin\ManagementReportController@biMonthlyTrend')->name('management-report.bimonthly-trend');
    Route::get('management-report/current', 'Admin\ManagementReportController@current')->name('management-report.current');

    Route::get('subscription-report/user-dash', 'Admin\SubscriptionReportController@userDashboardSubscriptions')->name('subscription-report.user-dash');
    Route::get('subscription-report/agency-dash', 'Admin\SubscriptionReportController@agencyDashboardSubscriptions')->name('subscription-report.agency-dash');
    Route::get('subscription-report/wavo3', 'Admin\SubscriptionReportController@wavo3Subscriptions')->name('subscription-report.wavo3');
    Route::get('subscription-report/wavo3e', 'Admin\SubscriptionReportController@wavo3eSubscriptions')->name('subscription-report.wavo3e');


    Route::get('users/{user}/'.$pluralTeamString.'/create', ['as' => 'users.'.$pluralTeamString.'.create', 'uses' => 'Admin\TeamController@createForUser']);
    Route::post('users/{user}/'.$pluralTeamString,  ['as' => 'users.'.$pluralTeamString.'.store', 'uses' => 'Admin\TeamController@storeForUser']);

    Route::resource('users', 'Admin\User\UserController');
    Route::put('users/{user}/restore', ['as' => 'users.restore', 'uses' => 'Admin\User\UserController@restore']);

    Route::resource('roles', 'Admin\User\RoleController')->middleware('can:admin');
    Route::put('roles/{role}/restore', ['as' => 'roles.restore', 'uses' => 'Admin\User\RoleController@restore'])->middleware('can:admin');;

    Route::resource('permissions', 'Admin\User\PermissionController')->middleware('can:admin');
    Route::put('permissions/{permission}/restore', ['as' => 'permissions.restore', 'uses' => 'Admin\User\PermissionController@restore'])->middleware('can:admin');

    Route::resource('agencies', 'Admin\Agency\AgencyController');
    Route::put('agencies/{agency}/restore', ['as' => 'agencies.restore', 'uses' => 'Admin\Agency\AgencyController@restore']);

    Route::get('agencies/{agency}/agency-invitations', 'Admin\Agency\MailedInvitationController@all')->name('agencies.invitations.index');
    Route::post('agencies/{agency}/agency-invitations', 'Admin\Agency\MailedInvitationController@store')->name('agencies.invitations.store');
    Route::delete('agency-invitations/{agency_invitation}', 'Admin\Agency\MailedInvitationController@destroy')->name('agencies.invitations.destroy');

    Route::resource('campaign/geographies', 'Admin\Campaign\GeographyController');
    Route::resource('campaign/industries', 'Admin\Campaign\IndustryController');
    Route::resource('campaign/company_sizes', 'Admin\Campaign\CompanySizeController');
    Route::resource('campaign/job_functions', 'Admin\Campaign\JobFunctionController');
    Route::resource('campaign/seniorities', 'Admin\Campaign\SeniorityController');

    Route::get('wavo2-user-registration/create', 'Admin\User\Wavo2UserController@create')->name('wavo2-user-registration.create');
    Route::post('wavo2-user-registration', 'Admin\User\Wavo2UserController@store')->name('wavo2-user-registration.store');
    Route::get('wavo2-user-registration/{user}', 'Admin\User\Wavo2UserController@show')->name('wavo2-user-registration.show');

    Route::get('wavo3-user-registration/create', 'Admin\User\Wavo3UserController@create')->name('wavo3-user-registration.create');
    Route::post('wavo3-user-registration', 'Admin\User\Wavo3UserController@store')->name('wavo3-user-registration.store');
    Route::get('wavo3-user-registration/{user}', 'Admin\User\Wavo3UserController@show')->name('wavo3-user-registration.show');

    Route::get('wavo3e-user-registration/create', 'Admin\User\Wavo3eUserController@create')->name('wavo3e-user-registration.create');
    Route::post('wavo3e-user-registration', 'Admin\User\Wavo3eUserController@store')->name('wavo3e-user-registration.store');
    Route::get('wavo3e-user-registration/{user}', 'Admin\User\Wavo3eUserController@show')->name('wavo3e-user-registration.show');

    Route::get('failed-email-messages', [\App\Http\Controllers\Admin\FailedEmailMessageController::class, 'index'])
        ->name('failed-email-messages.index');
    Route::get('email-message-tests', [\App\Http\Controllers\Admin\EmailMessageTestController::class, 'index'])
        ->name('email-message-tests.index');
    Route::get('email-notifications', [\App\Http\Controllers\Admin\EmailNotificationController::class, 'index'])
        ->name('email-notifications.index');

    Route::prefix('/csv-upload')->name('csv-upload.')->middleware(['auth', 'can:support'])->group(function () {
        Route::get('/', 'App\Http\Controllers\Admin\CsvUploadController@index')
            ->name('index');
        Route::get('/{csvUpload}', 'App\Http\Controllers\Admin\CsvUploadController@show')
            ->name('show');
        Route::get('/{csvUpload}/download', 'App\Http\Controllers\Admin\CsvUploadController@download')
            ->name('download');
        Route::post('/csv-upload', 'App\Http\Controllers\Admin\CsvUploadController@store')
            ->name('store');
        Route::delete('/{csvUpload}', 'App\Http\Controllers\Admin\CsvUploadController@destroy')
            ->name('destroy');
        Route::put('/{csvUpload}', 'App\Http\Controllers\Admin\CsvUploadController@update')
            ->name('update');
    });

    Route::resource('journeys', 'Admin\Journey\JourneyController');
    Route::resource('journey-steps', 'Admin\Journey\JourneyStepController');

    Route::get('search-stats', 'App\Http\Controllers\Admin\SearchStatsController@index')->name('search-stats.index');
    Route::post('search-stats', 'App\Http\Controllers\Admin\SearchStatsController@show')->name('search-stats.show');
    Route::get('api-usage', 'App\Http\Controllers\Admin\ApiUsageController@index')->name('api-usage.index');
    Route::post('api-usage', 'App\Http\Controllers\Admin\ApiUsageController@show')->name('api-usage.show');
    Route::get('api-usage/download', 'App\Http\Controllers\Admin\ApiUsageController@download')->name('api-usage.download');
    Route::get('domain-clusters', 'App\Http\Controllers\Admin\DomainClustersController@index')->name('domain-clusters.index');
    Route::post('domain-clusters', 'App\Http\Controllers\Admin\DomainClustersController@show')->name('domain-clusters.show');
    Route::get('domain-clusters/download', 'App\Http\Controllers\Admin\DomainClustersController@download')->name('domain-clusters.download');

    Route::get('platform-hosts', 'App\Http\Controllers\Admin\PlatformHostController@index')->name('platform-hosts.index');
    Route::post('platform-hosts/update-status', 'App\Http\Controllers\Admin\PlatformHostController@updatePlatformStatus')->name('platform-hosts.update-status');
    Route::post('platform-hosts/load-more', 'App\Http\Controllers\Admin\PlatformHostController@loadMore')->name('platform-hosts.load-more');
});

// Unsubscribe domains (admin access)
Route::prefix('/domains')->name('admin.unsubscribe-domains.')->middleware(['auth', 'can:admin'])->group(function () {
    Route::get('/', 'CustomDomainController@adminIndex')->name('index');
    Route::post('/', 'CustomDomainController@store')->name('store');
    Route::get('/list', 'CustomDomainController@list')->name('list');
    Route::delete('/{domain}', 'CustomDomainController@destroy')->name('destroy');
});

// Blocked Subdomains (used on agency registration)
Route::prefix('/subdomain-blocks')->name('admin.subdomain-blocks.')->middleware(['auth', 'can:admin'])->group(function () {
    Route::get('/', 'SubdomainBlockController@index')->name('index');
    Route::post('/', 'SubdomainBlockController@store')->name('store');
    Route::get('/list', 'SubdomainBlockController@list')->name('list');
    Route::delete('/{subdomain}', 'SubdomainBlockController@destroy')->name('destroy');
});

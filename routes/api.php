<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register the API routes for your application as
| the routes are automatically authenticated using the API guard and
| loaded automatically by this application's RouteServiceProvider.
|
*/

Route::group([
    'middleware' => 'auth:api'
], function () {
    //
});

//Route::get('/allow-domain', function() {
//    App\Agency::where('domain', '=', request()->getHost())->firstOrFail();
//    return response('success', 200);
//})->name('allow-domain');

Route::prefix('email-whs')->name('email-webhooks.')->middleware('throttle:nylas')->group(function () {
    Route::name('verify.')->group(function () {
        Route::get('message/created', 'Nylas\Webhooks\ChallengeController@verify')->name('message.created');
        Route::get('message/opened', 'Nylas\Webhooks\ChallengeController@verify')->name('message.opened');
        Route::get('message/clicked', 'Nylas\Webhooks\ChallengeController@verify')->name('message.clicked');
        Route::get('account/invalid', 'Nylas\Webhooks\ChallengeController@verify')->name('account.invalid');
        Route::get('account/error', 'Nylas\Webhooks\ChallengeController@verify')->name('account.error');
        Route::get('account/stopped', 'Nylas\Webhooks\ChallengeController@verify')->name('account.stopped');
        Route::get('account/running', 'Nylas\Webhooks\ChallengeController@verify')->name('account.running');
    });

    Route::post('message/created', 'Nylas\Webhooks\MessageController@create')->name('messsage.created');
    Route::post('message/opened', 'Nylas\Webhooks\MessageController@open')->name('message.opened');
    Route::post('message/clicked', 'Nylas\Webhooks\MessageController@click')->name('message.clicked');
    Route::post('account/invalid', 'Nylas\Webhooks\AccountController@invalid')->name('account.invalid');
    Route::post('account/error', 'Nylas\Webhooks\AccountController@error')->name('account.error');
    Route::post('account/stopped', 'Nylas\Webhooks\AccountController@stopped')->name('account.stopped');
    Route::post('account/running', 'Nylas\Webhooks\AccountController@running')->name('account.running');
});

Route::prefix('emailengine-whs')->name('emailengine-webhooks.')->middleware('throttle:emailengine')->group(function () {
    Route::post('webhook', 'EmailEngine\Webhooks\NotificationController@index')->name('index');
});

// t=track, o=opened, c=clicked
Route::prefix('t')->name('message-track.')->middleware('throttle:emailengine')->group(function () {
    Route::get('/{tracker_id}/o', 'EmailEngine\Webhooks\NotificationController@opened')->name('opened');
    Route::get('/{tracker_id}/c', 'EmailEngine\Webhooks\NotificationController@clicked')->name('clicked');
});

Route::post('spiffy/webhooks/checkout', 'Spiffy\Webhooks\CheckoutWebhookController@handle')
    ->name('spiffy.webhooks.checkout');

Route::prefix('zapier')->name('zapier.')->middleware(['zapier.apikey', 'throttle:zapier'])->group(function () {
    Route::get('me', 'Zapier\AuthController@me')->name('me');

    // data source triggers
    Route::get('clients', 'Zapier\TriggerController@clients')->name('triggers.clients');
    Route::get('campaigns', 'Zapier\TriggerController@campaigns')->name('triggers.campaigns');

    // triggers
    Route::get('replied', 'Zapier\TriggerController@replied')->name('triggers.replied');
    Route::get('autoreplied', 'Zapier\TriggerController@autoreplied')->name('triggers.autoreplied');
    Route::get('bounced', 'Zapier\TriggerController@bounced')->name('triggers.bounced');
    Route::get('unsubscribed', 'Zapier\TriggerController@unsubscribed')->name('triggers.unsubscribed');
    Route::get('positive', 'Zapier\TriggerController@positive')->name('triggers.positive');
    Route::get('negative', 'Zapier\TriggerController@negative')->name('triggers.negative');
    Route::get('neutral', 'Zapier\TriggerController@neutral')->name('triggers.neutral');
    Route::get('opened', 'Zapier\TriggerController@opened')->name('triggers.opened');
    Route::get('clicked', 'Zapier\TriggerController@clicked')->name('triggers.clicked');

    Route::get('started', 'Zapier\TriggerController@started')->name('triggers.started');
    Route::get('paused', 'Zapier\TriggerController@paused')->name('triggers.paused');
    Route::get('completed', 'Zapier\TriggerController@completed')->name('triggers.completed');

    // actions
    Route::post('add-contact', 'Zapier\ActionController@add_contact')->name('actions.contacts.store');
    Route::post('stop-contact', 'Zapier\ActionController@stop_contact')->name('actions.contacts.update.stop');
    Route::post('add-suppression', 'Zapier\ActionController@add_suppression')->name('actions.suppressions.store');
    Route::post('add-client', 'Zapier\TeamActionController@add_client')->name('actions.clients.store');
    Route::post('add-client-user', 'Zapier\TeamActionController@add_client_user')->name('actions.clients.users.store');
});

Route::prefix('pipedream')->name('pipedream.')->middleware(['pipedream.apikey', 'throttle:pipedream'])->group(function () {
    Route::get('me', 'Zapier\AuthController@me')->name('me');

    // data source triggers
    Route::get('clients', 'Zapier\TriggerController@clients')->name('triggers.clients');
    Route::get('campaigns', 'Zapier\TriggerController@campaigns')->name('triggers.campaigns');

    // triggers
    Route::get('replied', 'Zapier\TriggerController@replied')->name('triggers.replied');
    Route::get('autoreplied', 'Zapier\TriggerController@autoreplied')->name('triggers.autoreplied');
    Route::get('bounced', 'Zapier\TriggerController@bounced')->name('triggers.bounced');
    Route::get('unsubscribed', 'Zapier\TriggerController@unsubscribed')->name('triggers.unsubscribed');
    Route::get('positive', 'Zapier\TriggerController@positive')->name('triggers.positive');
    Route::get('negative', 'Zapier\TriggerController@negative')->name('triggers.negative');
    Route::get('neutral', 'Zapier\TriggerController@neutral')->name('triggers.neutral');
    Route::get('opened', 'Zapier\TriggerController@opened')->name('triggers.opened');
    Route::get('clicked', 'Zapier\TriggerController@clicked')->name('triggers.clicked');

    Route::get('started', 'Zapier\TriggerController@started')->name('triggers.started');
    Route::get('paused', 'Zapier\TriggerController@paused')->name('triggers.paused');
    Route::get('completed', 'Zapier\TriggerController@completed')->name('triggers.completed');

    // actions
    Route::post('add-contact', 'Zapier\ActionController@add_contact')->name('actions.contacts.store');
    Route::post('stop-contact', 'Zapier\ActionController@stop_contact')->name('actions.contacts.update.stop');
    Route::post('add-suppression', 'Zapier\ActionController@add_suppression')->name('actions.suppressions.store');
    Route::post('add-client', 'Zapier\TeamActionController@add_client')->name('actions.clients.store');
    Route::post('add-client-user', 'Zapier\TeamActionController@add_client_user')->name('actions.clients.users.store');
});

Route::prefix('v1')->name('api.v1.')->middleware(['auth:api', 'verified', 'can:agency-admin', 'verifySubscription'])->group(function () {
    Route::middleware('throttle:api-fast')->group(function () {
        Route::get('clients', 'Api\TeamController@index')->name('clients.index');
        Route::post('clients', 'Api\TeamController@store')->name('clients.store');
        Route::get('clients/{client_id}/users', 'Api\TeamUserController@index')->name('clients.users.index');
        Route::post('clients/{client_id}/users', 'Api\TeamUserController@store')->name('clients.users.store');
        Route::put('clients/{client_id}/users', 'Api\TeamUserController@update')->name('clients.users.update');
        Route::delete('clients/{client_id}/users', 'Api\TeamUserController@destroy')->name('clients.users.destroy');
        Route::apiResource('campaigns', 'Api\CampaignController')->except([
            'show', 'update', 'destroy'
        ]);
        Route::post('contacts', 'Api\ProspectController@store')->name('prospects.store');
        Route::get('contacts/{contact_id}', 'Api\ProspectController@show')->name('prospects.show');
        Route::get('campaigns/{campaign_id}/replies', 'Api\EmailMessageReplyController@campaignReplyIndex')
            ->name('campaigns.replies.index');
        Route::get('campaigns/{campaign_id}/daily-stats', 'Api\DailyCampaignStatsController@index')
            ->name('campaigns.daily-stats.index');
        Route::get('campaigns/{campaign_id}/stats', 'Api\StatsController@index')
            ->name('campaigns.stats.index');
        Route::get('contacts/{contact_id}/replies', 'Api\EmailMessageReplyController@prospectReplyIndex')
            ->name('prospects.replies.index');
        Route::get('email-threads/{email_thread_id}', 'Api\EmailThreadController@show')->name('email-threads.show');
        Route::put('contact-interest', 'Api\ProspectInterestController@update')->name('contact-interest.update');
        Route::delete('contact-interest', 'Api\ProspectInterestController@destroy')->name('contact-interest.destroy');
    });
    Route::middleware('throttle:api-medium')->group(function () {
        Route::get('campaigns/{campaign_id}/contacts',
            'Api\ProspectController@campaignIndex')->name('campaigns.prospects.index');
        Route::get('contacts', 'Api\ProspectController@index')->name('prospects.index');
    });
    Route::middleware('throttle:api-slow')->group(function () {
        Route::post('suppressions/emails', 'Api\EmailBlockController@store')->name('suppressions.emails.store');
        Route::post('suppressions/domains', 'Api\DomainBlockController@store')->name('suppressions.domains.store');
    });
});

Vue.component('register-agency', {
  /**
   * Load mixins for the component.
   */
  mixins: [
    require('./../../../../../spark/resources/assets/js/mixins/register'),
    require('./../../../../../spark/resources/assets/js/mixins/plans'),
    require('./../../../../../spark/resources/assets/js/mixins/vat'),
  ],

  props: ['domain', 'app_env', 'mailboxlayer_key', 'allow_free_email'],

  /**
   * The component's data.
   */
  data() {
    return {
      stripeInline: true,
      query: null,
      agency: {},

      clientSecret: '',
      redirectUrl: '',

      coupon: null,
      invalidCoupon: false,

      country: null,
      taxRate: 0,

      plans: [],
      selectedPlan: null,
      validPlanFromUrl: null,

      agencyInvitation: null,
      invalidAgencyInvitation: false,
      isValidatingEmail: false,
      isSubscribing: false,
      isCardNameErr: false,

      default_trial: 1,
      default_plan: 'tsunami',

      formLabel: {
        title: 'Start Your 7 Day Trial',
        button_busy: 'Starting Your Trial Subscription',
        button: 'Start Trial'
      },

      registerForm: new SparkForm({
        payment_method: '',
        plan: '',
        coupon: null,
        address: '',
        address_line_2: '',
        city: '',
        state: '',
        zip: '',
        country: 'US',
        vat_id: '',

        team: '',
        team_slug: '',
        name: '',
        email: '',
        password: '',
        password_confirmation: '',
        terms: true,
        agency_invitation: null,
        agency_id: null,
        agency_name: null,
        agency_email: null,
        agency_domain: null,
        agency_subdomain: null,
        agency_hashid: null,
        selected_plan: '',
        trial: 0
      }),

      subscribeForm: new SparkForm({
        payment_method: '',
        plan: '',
        coupon: null,
        address: '',
        address_line_2: '',
        city: '',
        state: '',
        zip: '',
        country: 'US',
        vat_id: ''
      }),

      stripeError: '',

      stripeElementStyles: {
        base: {
          color: '#76838f',
          fontSize: '14px',
          fontWeight: '100',
          fontFamily: '"Roboto", sans-serif',
          height: '36.0167px',
          lineHeight: '22px',
          '::placeholder': {
            color: '#a3afb7',
          },
        },
        invalid: {
          color: 'red',
          ':focus': {
            color: 'red',
          },
          '::placeholder': {
            color: 'red',
          },
        },
      },
      stripeElementClasses: {
        focus: 'focus',
        empty: 'empty',
        invalid: 'invalid',
      },
      cardElement: {},
      cardNumber: {},
      cardExpiry: {},
      cardCvc: {},
      cardName: '',
    };
  },


  watch: {
    /**
     * Watch for changes on the entire billing address.
     */
    'currentBillingAddress': function (value) {
      if (!Spark.collectsEuropeanVat) {
        return;
      }

      this.refreshTaxRate(this.registerForm);
    },


    /**
     * Watch the team name for changes.
     */
    'registerForm.team': function (val, oldVal) {
      if (this.registerForm.team_slug == '' ||
        this.registerForm.team_slug == oldVal.toLowerCase().replace(/[\s\W-]+/g, '-')
      ) {
        this.registerForm.team_slug = val.toLowerCase().replace(/[\s\W-]+/g, '-');
      }
    },

    /**
     * Watch the agency subdomain for changes and update the agency domain accordingly.
     */
    'registerForm.agency_subdomain': function (val, oldVal) {
      if (val) {
        this.registerForm.agency_domain = val + '.' + this.domain;
      } else {
        this.registerForm.agency_domain = '';
      }
    },
  },


  /**
   * The component has been created by Vue.
   */
  created() {
    this.query = URI(document.URL).query(true);

    this.getPlans();

    this.guessCountry();

    if (this.query.coupon) {
      this.getCoupon();

      this.registerForm.coupon = this.query.coupon;
    }

    if (this.query.agency_invitation) {
      this.getAgencyInvitation();

      this.registerForm.agency_invitation = this.query.agency_invitation;
    }
  },


  /**
   * Prepare the component.
   */
  mounted() {
    if (this.stripeInline) {
      this.initializeStripeCustomElementInline()
    } else {
      this.initializeStripeCustomElement();
    }
  },


  methods: {
    /**
     * Get the invitation specified in the query string.
     */
    getAgencyInvitation() {
      axios.get(`/agency_invitations/${this.query.agency_invitation}`)
        .then(response => {
          this.agencyInvitation = response.data;
          this.registerForm.agency_id = this.agencyInvitation.agency.id;
          this.registerForm.agency_hashid = this.agencyInvitation.agency.hashid;
          this.registerForm.agency_name = this.agencyInvitation.agency.name;
          this.registerForm.agency_subdomain = this.agencyInvitation.agency.domain.replace('.' + this.domain, '');
          this.registerForm.agency_email = this.agencyInvitation.agency.email;
        })
        .catch(response => {
          this.invalidAgencyInvitation = true;
        });
    },

    /**
     * Attempt to guess the user's country.
     */
    guessCountry() {
      axios.get('/geocode/country')
        .then(response => {
          if (response.data != 'ZZ') {
            this.registerForm.country = response.data;
          }
        })
        .catch(response => {
          //
        })
        .finally(function () {
          this.refreshStatesAndProvinces();
        });
    },


    /**
     * Get the coupon specified in the query string.
     */
    getCoupon() {
      axios.get('/coupon/' + this.query.coupon)
        .then(response => {
          this.coupon = response.data;
        })
        .catch(response => {
          this.invalidCoupon = true;
        });
    },

    /**
     * Connect to mailboxlayer to validate if email is working before registering to platform
     */
    prepareRegister() {

      // if (this.app_env == 'production') {
      //   this.isValidatingEmail = true;
      //   let checkUrl = 'https://apilayer.net/api/check?access_key=' + this.mailboxlayer_key + '&email=' + this.registerForm.email + '&format=1'
      //
      //   $.ajax({
      //     url: checkUrl,
      //     dataType: 'json',
      //     success: (json) => {
      //
      //       // Access and use your preferred validation result objects
      //       // {
      //       //     "email": "<EMAIL>",
      //       //     "did_you_mean": "",
      //       //     "user": "nanjiro.ko",
      //       //     "domain": "gmail.com",
      //       //     "format_valid": true,
      //       //     "mx_found": true,
      //       //     "smtp_check": true,
      //       //     "catch_all": null,
      //       //     "role": false,
      //       //     "disposable": false,
      //       //     "free": true,
      //       //     "score": 0.8
      //       // }
      //       this.isValidatingEmail = false;
      //
      //       // if email address is valid, proceed to register
      //       if (!json.format_valid || !json.mx_found || json.disposable
      //         || (!this.allow_free_email && json.free)
      //       ) {
      //         this.registerForm.errors.set({'email': ['Please use a valid business email address']});
      //       } else {
      //         this.register();
      //       }
      //     }
      //   });
      // } else {
      this.register();
      // }
    },


    /**
     * Attempt to register with the application.
     */
    register() {
      this.registerForm.busy = true;
      this.registerForm.errors.forget();

      this.sendRegistration();
    },


    /*
     * After obtaining the Stripe token, send the registration to Spark.
     */
    sendRegistration() {
      this.registerForm.plan = '';
      this.registerForm.name = this.cardName;

      Spark.post('/register', this.registerForm)
        .then(response => {
          // google tag manager
          if (this.app_env == 'production') {
            let eventName = this.selectedPlan.id == 'agency-dashboard'
              ? 'new_registration' : 'new_teamaccount';

            if (this.selectedPlan.slug == 'ripple' || this.selectedPlan.slug == 'crest' || this.selectedPlan.slug == 'tsunami') {
              eventName = 'new_wavo3_registration';
            }

            // dataLayer.push({
            //   'name': this.registerForm.name,
            //   'email': this.registerForm.email,
            //   'event': eventName
            // });
          }

          this.redirectUrl = response.redirect;
          this.clientSecret = response.client_secret;
          this.agency = response.agency;
          this.subscribe();
        });
    },

    /**
     * Get the active plans for the application.
     */
    getPlans() {
      // if ( ! Spark.cardUpFront) {
      //     return;
      // }

      axios.get('/spark/plans/agency')
        .then(response => {
          let plans = response.data;

          this.plans = _.where(plans, {type: "agency"});

          if ('plan' in this.query) {
            this.validPlanFromUrl = _.head(_.where(
              plans,
              {type: "agency", slug: this.query.plan}
            ));
          }

          // spark select default plan
          this.selectAppropriateDefaultPlan();

          // use the selected default plan
          this.registerForm.selected_plan = this.selectedPlan.id;


        });
    },

    /**
     * Select the appropriate default plan for registration.
     */
    selectAppropriateDefaultPlan() {
      // make sure the query.plan value is valid plan
      if (this.query.plan && this.validPlanFromUrl) {
        this.selectPlanById(this.query.plan) || this.selectPlanBySlug(this.query.plan);
        this.registerForm.trial = this.query.trial;
      } else if (this.query.invitation) {
        this.selectFreePlan();
      } else if (this.paidPlansForActiveInterval.length > 0) {
        // this.selectPlan(this.paidPlansForActiveInterval[0]);
        // No plan from url. Use default
        this.selectPlanBySlug(this.default_plan);
        this.registerForm.trial = this.default_trial;
      } else {
        this.selectFreePlan();
      }

      if (this.shouldShowYearlyPlans()) {
        this.showYearlyPlans();
      }
    },

    selectThisPlan(plan) {
      this.registerForm.selected_plan = plan.id;
    },

    updateBilling() {
      if (!this.cardName) {
        this.isCardNameErr = true
        return false
      }

      this.isCardNameErr = false

      if(!this.registerForm.busy){
        this.subscribe();
      }
    },

    initializeStripeCustomElementInline() {
      if (!this.$root.stripeElementMounted) {
        this.cardElement = elements.create('card', {
          hidePostalCode: true,
          style: this.stripeElementStyles,
          classes: this.stripeElementClasses,
          showIcon: true,
        });
        this.cardElement.mount('#stripe-card');

        let self = this;
        this.cardElement.on('change', function(event) {
          self.stripeError = '';
        });

        this.$root.stripeElementMounted = true;
      }
    },

    initializeStripeCustomElement() {
      if (!this.$root.stripeElementMounted) {
        this.cardNumber = elements.create('cardNumber', {
          style: this.stripeElementStyles,
          classes: this.stripeElementClasses,
          placeholder: 'Card number',
          showIcon: true,
        });
        this.cardNumber.mount('#stripe-card-number');

        this.cardExpiry = elements.create('cardExpiry', {
          style: this.stripeElementStyles,
          classes: this.stripeElementClasses,
        });
        this.cardExpiry.mount('#stripe-card-expiry');

        this.cardCvc = elements.create('cardCvc', {
          style: this.stripeElementStyles,
          classes: this.stripeElementClasses,
        });
        this.cardCvc.mount('#stripe-card-cvc');

        let self = this;
        this.cardNumber.on('change', function(event) {
          self.stripeError = '';
        });
        this.cardExpiry.on('change', function(event) {
          self.stripeError = '';
        });
        this.cardCvc.on('change', function(event) {
          self.stripeError = '';
        });

        this.$root.stripeElementMounted = true;
      }
    },

    /**
     * Subscribe to the specified plan.
     */
    subscribe() {
      // this.registerForm.startProcessing();
      this.stripeError = '';
      this.isSubscribing = true;

      let cardEmail = this.registerForm.email;

      stripe.confirmCardSetup ( this.clientSecret, {
          payment_method: {
            card: this.cardElement, // this.cardNumber if not stripe inline
            billing_details: {
              name: this.cardName,
              email: cardEmail
            }
          }
        }
      )
        .then((response) => {
          if (response.error) {
            // console.log('error on stripe check');
            // this.stripeError = response.error.message;
            this.stripeError = "There has been an error validating your card.";
            this.clearAgency();
            // this.registerForm.busy = false;
            // this.isSubscribing = false;
          } else {
            // this.registerForm.successful = true;
            // this.registerForm.busy = true;
            this.createSubscription(response.setupIntent.payment_method);
          }
        });
    },

    /*
    * After obtaining the Stripe payment method, create subscription on the server.
    */
    createSubscription(paymentMethod) {
      this.subscribeForm.payment_method = paymentMethod;
      this.subscribeForm.plan = this.selectedPlan.id;
      this.subscribeForm.from_setup = true;

      Spark.post('/settings/agency-subscription', this.subscribeForm)
        .then(response => {
          Bus.$emit('updateUser');
          Bus.$emit('updateTeam');
          Bus.$emit('updateAgency');

          // if (response.subscription && response.subscription === 'new' && this.app_env == 'production') {
          //   window.customerly('event', 'agency_started_trial');
          // }

          // sent the affiliate tracking
          // let strTID = this.getCookie('_fprom_track');
          // axios.post('https://firstpromoter.com/api/v1/track/signup', {
          //     "wid": "79f7bc01ac5382121ab3",
          //     "email": this.user.email,
          //     // "uid": this.user.stripe_id,
          //     "tid": strTID
          // }, {
          //     headers: {
          //         "x-api-key": 'b3fdd951ba28645d8a455626c6fdfcb5'
          //     }
          // }).then(track_response => {
          //     console.log(track_response)
          // });

          // google tag manager
          if (this.app_env == 'production') {
            // dataLayer.push({
            //   'name': this.registerForm.name,
            //   'email': this.registerForm.email,
            //   'event': 'new_trial'
            // });
          }

          // console.log('finish setup...')
          axios.put(`/agency/setup/${this.agency.hashid}/finish`)
            .then((response) => {
              swal({
                title: 'Registration Success!',
                text: 'You have activated your Wavo subscription. You can now login to your dashboard',
                icon: "success",
              }).then(() => {
                window.location = this.redirectUrl;
              });
            }).catch((error) => {
          });
        })
        .catch((error) => {
          // console.log('error on registration last step');
          // console.log(response);
          // console.log(response.error);
          this.stripeError = 'Billing Error. We had trouble processing your payment. Please validate your payment details.';
          this.clearAgency();
        });
    },

    clearAgency() {
      // console.log('clear agency temp data');
      axios.delete(`/register/${this.agency.hashid}`)
        .then(response => {
          swal({
            title: 'Registration Error',
            text: this.stripeError,
            icon: "error",
          }).then(() => {
            this.registerForm.busy = false;
            this.isSubscribing = false;
            this.subscribeForm.busy = false;
          });
        }).catch((error) => {
          swal({
            title: 'Registration Error',
            text: 'There has been an error when creating your subscription. Please contact support',
            icon: "error",
          }).then(() => {
            this.registerForm.busy = false;
            this.subscribeForm.busy = false;
            this.isSubscribing = false;
          });
      });
    }
  },


  computed: {
    formLabelTitle() {
      return this.registerForm.trial ? 'Start your 7-day trial' : 'Start your subscription';
    },

    formLabelButton() {
      return this.registerForm.trial ? 'Start Trial' : 'Register';
    },

    formLabelButtonBusy() {
      return 'Creating your account';
    },


    /**
     * Determine if the selected country collects European VAT.
     */
    countryCollectsVat() {
      return this.collectsVat(this.registerForm.country);
    },


    /**
     * Get the displayable discount for the coupon.
     */
    discount() {
      if (this.coupon) {
        if (this.coupon.percent_off) {
          return this.coupon.percent_off + '%';
        } else {
          return Vue.filter('currency')(this.coupon.amount_off / 100);
        }
      }
    },


    /**
     * Get the current billing address from the register form.
     *
     * This used primarily for wathcing.
     */
    currentBillingAddress() {
      return this.registerForm.address +
        this.registerForm.address_line_2 +
        this.registerForm.city +
        this.registerForm.state +
        this.registerForm.zip +
        this.registerForm.country +
        this.registerForm.vat_id;
    }
  }
});

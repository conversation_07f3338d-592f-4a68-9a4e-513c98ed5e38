/**
* jQuery asPaginator v0.3.3
* https://github.com/amazingSurge/jquery-asPaginator
*
* Copyright (c) amazingSurge
* Released under the LGPL-3.0 license
*/
!function(t,e){if("function"==typeof define&&define.amd)define(["jquery"],e);else if("undefined"!=typeof exports)e(require("jquery"));else{var i={exports:{}};e(t.jQuery),t.jqueryAsPaginatorEs=i.exports}}(this,function(t){"use strict";function e(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function i(t,e){var i=this,s=Date.now||function(){return(new Date).getTime()},n=void 0,a=void 0,o=void 0,r=void 0,l=0,u=function(){l=s(),n=null,r=t.apply(a,o),n||(a=o=null)};return function(){for(var c=arguments.length,f=Array(c),h=0;h<c;h++)f[h]=arguments[h];var d=s(),p=e-(d-l);return a=i,o=f,p<=0||p>e?(n&&(clearTimeout(n),n=null),l=d,r=t.apply(a,o),n||(a=o=null)):n||(n=setTimeout(u,p)),r}}var s=function(t){return t&&t.__esModule?t:{default:t}}(t),n=function(){function t(t,e){for(var i=0;i<e.length;i++){var s=e[i];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(t,s.key,s)}}return function(e,i,s){return i&&t(e.prototype,i),s&&t(e,s),e}}(),a={namespace:"asPaginator",currentPage:1,itemsPerPage:10,visibleNum:5,resizeThrottle:250,disabledClass:"asPaginator_disable",activeClass:"asPaginator_active",tpl:function(){return"<ul>{{first}}{{prev}}{{lists}}{{next}}{{last}}</ul>"},skin:null,components:{first:!0,prev:!0,next:!0,last:!0,lists:!0},onInit:null,onReady:null,onChange:null},o={},r=function(){function t(i,n,r){e(this,t),this.element=i,this.$element=(0,s.default)(i).empty(),this.options=s.default.extend({},a,r),this.namespace=this.options.namespace,this.currentPage=this.options.currentPage||1,this.itemsPerPage=this.options.itemsPerPage,this.totalItems=n,this.totalPages=this.getTotalPages(),this.isOutOfBounds()&&(this.currentPage=this.totalPages),this.initialized=!1,this.components=o,this.$element.addClass(this.namespace),this.options.skin&&this.$element.addClass(this.options.skin),this.classes={disabled:this.options.disabledClass,active:this.options.activeClass},this.disabled=!1,this._trigger("init"),this.init()}return n(t,[{key:"init",value:function(){var t=this;t.visible=t.getVisible(),s.default.each(this.options.components,function(e,i){if(null===i||!1===i)return!1;t.components[e].init(t)}),t.createHtml(),t.bindEvents(),t.goTo(t.currentPage),t.initialized=!0,"number"!=typeof this.options.visibleNum&&(0,s.default)(window).on("resize",i(function(){t.resize()},this.options.resizeTime)),this._trigger("ready")}},{key:"createHtml",value:function(){var t=this,e=void 0;t.contents=t.options.tpl();for(var i=t.contents.match(/\{\{([^\}]+)\}\}/g).length,n=void 0,a=0;a<i;a++)"namespace"!==(n=t.contents.match(/\{\{([^\}]+)\}\}/))[1]?this.options.components[n[1]]&&(e=t.components[n[1]].opts.tpl.call(t),t.contents=t.contents.replace(n[0],e)):t.contents=t.contents.replace(n[0],t.namespace);t.$element.append((0,s.default)(t.contents))}},{key:"bindEvents",value:function(){var t=this;s.default.each(this.options.components,function(e,i){if(null===i||!1===i)return!1;t.components[e].bindEvents(t)})}},{key:"unbindEvents",value:function(){var t=this;s.default.each(this.options.components,function(e,i){if(null===i||!1===i)return!1;t.components[e].unbindEvents(t)})}},{key:"resize",value:function(){var t=this;t._trigger("resize"),t.goTo(t.currentPage),t.visible=t.getVisible(),s.default.each(this.options.components,function(e,i){if(null===i||!1===i)return!1;void 0!==t.components[e].resize&&t.components[e].resize(t)})}},{key:"getVisible",value:function(){var t=(0,s.default)("body, html").width(),e=0;return"number"!=typeof this.options.visibleNum?s.default.each(this.options.visibleNum,function(i,s){t>i&&(e=s)}):e=this.options.visibleNum,e}},{key:"calculate",value:function(t,e,i){var s=1,n=1;return t<=i+2&&(s=0),t+i+1>=e&&(n=0),{left:s,right:n}}},{key:"goTo",value:function(t){if((t=Math.max(1,Math.min(t,this.totalPages)))===this.currentPage&&!0===this.initialized)return!1;this.$element.find("."+this.classes.disabled).removeClass(this.classes.disabled),t===this.totalPages&&(this.$element.find("."+this.namespace+"-next").addClass(this.classes.disabled),this.$element.find("."+this.namespace+"-last").addClass(this.classes.disabled)),1===t&&(this.$element.find("."+this.namespace+"-prev").addClass(this.classes.disabled),this.$element.find("."+this.namespace+"-first").addClass(this.classes.disabled)),this.currentPage=t,this.initialized&&this._trigger("change",t)}},{key:"prev",value:function(){return!!this.hasPreviousPage()&&(this.goTo(this.getPreviousPage()),!0)}},{key:"next",value:function(){return!!this.hasNextPage()&&(this.goTo(this.getNextPage()),!0)}},{key:"goFirst",value:function(){return this.goTo(1)}},{key:"goLast",value:function(){return this.goTo(this.totalPages)}},{key:"update",value:function(t,e){var i={};"string"==typeof t?i[t]=e:i=t;for(var s in i)if(Object.hasOwnProperty.call(i,s))switch(s){case"totalItems":this.totalItems=i[s];break;case"itemsPerPage":this.itemsPerPage=i[s];break;case"currentPage":this.currentPage=i[s]}this.totalPages=this.totalPages()}},{key:"isOutOfBounds",value:function(){return this.currentPage>this.totalPages}},{key:"getItemsPerPage",value:function(){return this.itemsPerPage}},{key:"getTotalItems",value:function(){return this.totalItems}},{key:"getTotalPages",value:function(){return this.totalPages=Math.ceil(this.totalItems/this.itemsPerPage),this.lastPage=this.totalPages,this.totalPages}},{key:"getCurrentPage",value:function(){return this.currentPage}},{key:"hasPreviousPage",value:function(){return this.currentPage>1}},{key:"getPreviousPage",value:function(){return!!this.hasPreviousPage()&&this.currentPage-1}},{key:"hasNextPage",value:function(){return this.currentPage<this.totalPages}},{key:"getNextPage",value:function(){return!!this.hasNextPage()&&this.currentPage+1}},{key:"enable",value:function(){this.disabled&&(this.disabled=!1,this.$element.removeClass(this.classes.disabled),this.bindEvents()),this._trigger("enable")}},{key:"disable",value:function(){!0!==this.disabled&&(this.disabled=!0,this.$element.addClass(this.classes.disabled),this.unbindEvents()),this._trigger("disable")}},{key:"destroy",value:function(){this.$element.removeClass(this.classes.disabled),this.unbindEvents(),this.$element.data("asPaginator",null),this._trigger("destroy")}},{key:"_trigger",value:function(t){for(var e=arguments.length,i=Array(e>1?e-1:0),s=1;s<e;s++)i[s-1]=arguments[s];var n=[this].concat(i);this.$element.trigger("asPaginator::"+t,n);var a="on"+(t=t.replace(/\b\w+\b/g,function(t){return t.substring(0,1).toUpperCase()+t.substring(1)}));"function"==typeof this.options[a]&&this.options[a].apply(this,i)}},{key:"eventName",value:function(t){if("string"!=typeof t||""===t)return"."+this.options.namespace;for(var e=(t=t.split(" ")).length,i=0;i<e;i++)t[i]=t[i]+"."+this.options.namespace;return t.join(" ")}}],[{key:"registerComponent",value:function(t,e){o[t]=e}},{key:"setDefaults",value:function(t){s.default.extend(a,s.default.isPlainObject(t)&&t)}}]),t}();r.registerComponent("prev",{defaults:{tpl:function(){return'<li class="'+this.namespace+'-prev"><a>Prev</a></li>'}},init:function(t){var e=s.default.extend({},this.defaults,t.options.components.prev);this.opts=e},bindEvents:function(t){this.$prev=t.$element.find("."+t.namespace+"-prev"),this.$prev.on("click.asPaginator",s.default.proxy(t.prev,t))},unbindEvents:function(){this.$prev.off("click.asPaginator")}}),r.registerComponent("next",{defaults:{tpl:function(){return'<li class="'+this.namespace+'-next"><a>Next</a></li>'}},init:function(t){var e=s.default.extend({},this.defaults,t.options.components.next);this.opts=e},bindEvents:function(t){this.$next=t.$element.find("."+t.namespace+"-next"),this.$next.on("click.asPaginator",s.default.proxy(t.next,t))},unbindEvents:function(){this.$next.off("click.asPaginator")}}),r.registerComponent("first",{defaults:{tpl:function(){return'<li class="'+this.namespace+'-first"><a>First</a></li>'}},init:function(t){var e=s.default.extend({},this.defaults,t.options.components.first);this.opts=e},bindEvents:function(t){this.$first=t.$element.find("."+t.namespace+"-first"),this.$first.on("click.asPaginator",s.default.proxy(t.goFirst,t))},unbindEvents:function(){this.$first.off("click.asPaginator")}}),r.registerComponent("last",{defaults:{tpl:function(){return'<li class="'+this.namespace+'-last"><a>Last</a></li>'}},init:function(t){var e=s.default.extend({},this.defaults,t.options.components.last);this.opts=e},bindEvents:function(t){this.$last=t.$element.find("."+t.namespace+"-last"),this.$last.on("click.asPaginator",s.default.proxy(t.goLast,t))},unbindEvents:function(){this.$last.off("click.asPaginator")}}),r.registerComponent("lists",{defaults:{tpl:function(){var t="",e=this.currentPage>=this.visible?this.currentPage%this.visible:this.currentPage;e=0===e?this.visible:e;for(var i=1;i<e;i++)t+='<li class="'+this.namespace+'-items" data-value="'+(this.currentPage-e+i)+'"><a href="#">'+(this.currentPage-e+i)+"</a></li>";t+='<li class="'+this.namespace+"-items "+this.classes.active+'" data-value="'+this.currentPage+'"><a href="#">'+this.currentPage+"</a></li>";for(var s=this.currentPage+1,n=s+this.visible-e-1>this.totalPages?this.totalPages:s+this.visible-e-1;s<=n;s++)t+='<li class="'+this.namespace+'-items" data-value="'+s+'"><a href="#">'+s+"</a></li>";return t}},init:function(t){var e=s.default.extend({},this.defaults,t.options.components.lists);this.opts=e,t.itemsTpl=this.opts.tpl.call(t)},bindEvents:function(t){var e=this;this.$items=t.$element.find("."+t.namespace+"-items"),t.$element.on("click",this.$items,function(e){var i=(0,s.default)(e.target).parent().data("value")||(0,s.default)(e.target).data("value");return void 0!==i&&(""!==i&&void t.goTo(i))}),e.render(t),t.$element.on("asPaginator::change",function(){e.render(t)})},unbindEvents:function(t){t.$element.off("click",this.$items)},resize:function(t){this.render(t)},render:function(t){var e=t.currentPage,i=void 0,n=this,a=this.$items.removeClass(t.classes.active);s.default.each(a,function(n,a){if((0,s.default)(a).data("value")===e)return(0,s.default)(a).addClass(t.classes.active),i=!1,!1}),!1===i&&this.visibleBefore===t.visible||(this.visibleBefore=t.visible,s.default.each(a,function(e,i){0===e?(0,s.default)(i).replaceWith(n.opts.tpl.call(t)):(0,s.default)(i).remove()}),this.$items=t.$element.find("."+t.namespace+"-items"))}}),r.registerComponent("goTo",{defaults:{tpl:function(){return'<div class="'+this.namespace+'-goTo"><input type="text" class="'+this.namespace+'-input" /><button type="submit" class="'+this.namespace+'-submit">Go</button></div>'}},init:function(t){var e=s.default.extend({},this.defaults,t.options.components.goTo);this.opts=e},bindEvents:function(t){var e=this;e.$goTo=t.$element.find("."+t.namespace+"-goTo"),e.$input=e.$goTo.find("."+t.namespace+"-input"),e.$button=e.$goTo.find("."+t.namespace+"-submit"),e.$button.on("click",function(){var i=parseInt(e.$input.val(),10);i=i>0?i:t.currentPage,t.goTo(i)})},unbindEvents:function(){this.$button.off("click")}}),r.registerComponent("altLists",{defaults:{tpl:function(){var t="",e=this.totalPages,i=this.currentPage,s=this.calculate(i,e,this.visible),n=this,a=void 0,o=function(t,e){return"active"===e?'<li class="'+n.namespace+"-items "+n.classes.active+'" data-value="'+t+'"><a href="#">'+t+"</a></li>":"omit"===e?'<li class="'+n.namespace+"-items "+n.namespace+'_ellipsis" data-value="ellipsis"><a href="#">...</a></li>':'<li class="'+n.namespace+'-items" data-value="'+t+'"><a href="#">'+t+"</a></li>"};if(0===s.left){for(a=1;a<=i-1;a++)t+=o(a);t+=o(i,"active")}else{for(a=1;a<=2;a++)t+=o(a);for(t+=o(i,"omit"),a=i-this.visible+1;a<=i-1;a++)t+=o(a);t+=o(i,"active")}if(0===s.right)for(a=i+1;a<=e;a++)t+=o(a);else{for(a=i+1;a<=i+this.visible-1;a++)t+=o(a);for(t+=o(i,"omit"),a=e-1;a<=e;a++)t+=o(a)}return t}},init:function(t){var e=s.default.extend({},this.defaults,t.options.components.altLists);this.opts=e},bindEvents:function(t){var e=this;this.$items=t.$element.find("."+t.namespace+"-items"),t.$element.on("click",this.$items,function(e){var i=(0,s.default)(e.target).parent().data("value")||(0,s.default)(e.target).data("value");return void 0!==i&&("ellipsis"!==i&&(""!==i&&void t.goTo(i)))}),e.render(t),t.$element.on("asPaginator::change",function(){e.render(t)})},unbindEvents:function(t){t.$wrap.off("click",this.$items)},resize:function(t){this.render(t)},render:function(t){var e=this,i=this.$items.removeClass(t.classes.active);s.default.each(i,function(i,n){0===i?(0,s.default)(n).replaceWith(e.opts.tpl.call(t)):(0,s.default)(n).remove()}),this.$items=t.$element.find("."+t.namespace+"-items")}}),r.registerComponent("info",{defaults:{tpl:function(){return'<li class="'+this.namespace+'-info"><a href="javascript:void(0);"><span class="'+this.namespace+'-current"></span> / <span class="'+this.namespace+'-total"></span></a></li>'}},init:function(t){var e=s.default.extend({},this.defaults,t.options.components.info);this.opts=e},bindEvents:function(t){var e=t.$element.find("."+t.namespace+"-info"),i=e.find("."+t.namespace+"-current");e.find("."+t.namespace+"-total").text(t.totalPages),i.text(t.currentPage),t.$element.on("asPaginator::change",function(){i.text(t.currentPage)})}});var l={version:"0.3.3"},u=s.default.fn.asPaginator,c=function(t){for(var e=arguments.length,i=Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];if(!s.default.isNumeric(t)&&"string"==typeof t){var a=t;if(/^_/.test(a))return!1;if(!/^(get)/.test(a))return this.each(function(){var t=s.default.data(this,"asPaginator");t&&"function"==typeof t[a]&&t[a].apply(t,i)});var o=this.first().data("asPaginator");if(o&&"function"==typeof o[a])return o[a].apply(o,i)}return this.each(function(){(0,s.default)(this).data("asPaginator")||(0,s.default)(this).data("asPaginator",new(Function.prototype.bind.apply(r,[null].concat([this,t],i))))})};s.default.fn.asPaginator=c,s.default.asPaginator=s.default.extend({registerComponent:r.registerComponent,setDefaults:r.setDefaults,noConflict:function(){return s.default.fn.asPaginator=u,c}},l)});
//# sourceMappingURL=jquery-asPaginator.min.js.map

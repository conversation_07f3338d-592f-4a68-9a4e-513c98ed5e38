.btn-toolbar {
  display: block;
  width: 20px;
  height: 20px;
  padding: 10px;
  text-align: center;
  background: #76838f;
  border-radius: 6px;
  transition: none;
}

.btn-toolbar > i {
  font-size: 16px;
  color: #76838f;
}

.btn-toolbar:hover {
  cursor: pointer;
  background: #589ffc;
}

.btn-toolbar:hover > i {
  color: white;
}

.btn-toolbar-primary {
  background-color: #3e8ef7;
}

.btn-toolbar-primary.pressed {
  background-color: #589ffc;
}

.btn-toolbar-primary:hover {
  background-color: #589ffc;
}

.btn-toolbar-primary > i {
  color: white;
}

.btn-toolbar-danger {
  background-color: #ff4c52;
}

.btn-toolbar-danger.pressed {
  background-color: #ff666b;
}

.btn-toolbar-danger:hover {
  background-color: #ff666b;
}

.btn-toolbar-danger > i {
  color: white;
}

.btn-toolbar-warning {
  background-color: #eb6709;
}

.btn-toolbar-warning.pressed {
  background-color: #f57d1b;
}

.btn-toolbar-warning:hover {
  background-color: #f57d1b;
}

.btn-toolbar-warning > i {
  color: white;
}

.btn-toolbar-info {
  background-color: #0bb2d4;
}

.btn-toolbar-info.pressed {
  background-color: #28c0de;
}

.btn-toolbar-info:hover {
  background-color: #28c0de;
}

.btn-toolbar-info > i {
  color: white;
}

.btn-toolbar-success {
  background-color: #11c26d;
}

.btn-toolbar-success.pressed {
  background-color: #28d17c;
}

.btn-toolbar-success:hover {
  background-color: #28d17c;
}

.btn-toolbar-success > i {
  color: white;
}

.btn-toolbar-info-o {
  background-color: #526069;
}

.btn-toolbar-info-o.pressed {
  background-color: #76838f;
}

.btn-toolbar-info-o:hover {
  background-color: #76838f;
}

.btn-toolbar-info-o > i {
  color: white;
}

.btn-toolbar-light {
  background-color: #f3f7f9;
}

.btn-toolbar-light.pressed {
  background-color: #e4eaec;
}

.btn-toolbar-light:hover {
  background-color: #e4eaec;
}

.btn-toolbar-light > i {
  color: white;
}

.btn-toolbar-dark {
  background-color: #76838f;
}

.btn-toolbar-dark.pressed {
  background-color: #526069;
}

.btn-toolbar-dark:hover {
  background-color: #526069;
}

.btn-toolbar-dark > i {
  color: white;
}

.tool-container {
  position: absolute;
  box-sizing: content-box;
  background-color: #e4eaec;
  background-size: 100% 100%;
  border-radius: 6px;
}

.tool-container.tool-top, .tool-container.tool-bottom {
  height: 40px;
  border-bottom: 0 solid #beb8b8;
}

.tool-container.tool-top .tool-item, .tool-container.tool-bottom .tool-item {
  float: left;
  border-right: 0;
  border-left: 0;
}

.tool-item {
  box-sizing: content-box;
  display: block;
  width: 20px;
  height: 100%;
  height: 20px;
  padding: 10px;
  text-align: center;
  transition: none;
}

.tool-item > .icon {
  color: #76838f;
}

.tool-item.selected, .tool-item:hover {
  background: #589ffc;
}

.tool-item.selected > .icon, .tool-item:hover > .icon {
  color: white;
}

.tool-top .tool-item:first-child:hover, .tool-bottom .tool-item:first-child:hover {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.tool-top .tool-item:last-child:hover, .tool-bottom .tool-item:last-child:hover {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

.tool-vertical-top .tool-item:first-child:hover, .tool-vertical-bottom .tool-item:first-child:hover, .tool-right .tool-item:first-child:hover, .tool-left .tool-item:first-child:hover {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

.tool-vertical-top .tool-item:last-child:hover, .tool-vertical-bottom .tool-item:last-child:hover, .tool-right .tool-item:last-child:hover, .tool-left .tool-item:last-child:hover {
  border-bottom-right-radius: 6px;
  border-bottom-left-radius: 6px;
}

.tool-container .arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 7px;
}

.tool-container.tool-top .arrow {
  bottom: -14px;
  left: 50%;
  margin-left: -7px;
  border-color: #e4eaec transparent transparent;
}

.tool-container.tool-bottom .arrow {
  top: -14px;
  left: 50%;
  margin-left: -7px;
  border-color: transparent transparent #e4eaec;
}

.tool-container.tool-left .arrow {
  top: 50%;
  right: -14px;
  margin-top: -7px;
  border-color: transparent transparent transparent #e4eaec;
}

.tool-container.tool-right .arrow {
  top: 50%;
  left: -14px;
  margin-top: -7px;
  border-color: transparent #e4eaec transparent transparent;
}

.toolbar-primary {
  background-color: #589ffc;
}

.toolbar-primary.tool-top .arrow {
  border-color: #589ffc transparent transparent;
}

.toolbar-primary.tool-bottom .arrow {
  border-color: transparent transparent #589ffc;
}

.toolbar-primary.tool-left .arrow {
  border-color: transparent transparent transparent #589ffc;
}

.toolbar-primary.tool-right .arrow {
  border-color: transparent #589ffc transparent transparent;
}

.toolbar-primary .tool-item > .icon {
  color: white;
}

.toolbar-primary .tool-item.selected, .toolbar-primary .tool-item:hover {
  color: white;
  background: #3e8ef7;
}

.toolbar-danger {
  background-color: #ff666b;
}

.toolbar-danger.tool-top .arrow {
  border-color: #ff666b transparent transparent;
}

.toolbar-danger.tool-bottom .arrow {
  border-color: transparent transparent #ff666b;
}

.toolbar-danger.tool-left .arrow {
  border-color: transparent transparent transparent #ff666b;
}

.toolbar-danger.tool-right .arrow {
  border-color: transparent #ff666b transparent transparent;
}

.toolbar-danger .tool-item > .icon {
  color: white;
}

.toolbar-danger .tool-item.selected, .toolbar-danger .tool-item:hover {
  color: white;
  background: #ff4c52;
}

.toolbar-warning {
  background-color: #eb6709;
}

.toolbar-warning.tool-top .arrow {
  border-color: #eb6709 transparent transparent;
}

.toolbar-warning.tool-bottom .arrow {
  border-color: transparent transparent #eb6709;
}

.toolbar-warning.tool-left .arrow {
  border-color: transparent transparent transparent #eb6709;
}

.toolbar-warning.tool-right .arrow {
  border-color: transparent #eb6709 transparent transparent;
}

.toolbar-warning .tool-item > .icon {
  color: white;
}

.toolbar-warning .tool-item.selected, .toolbar-warning .tool-item:hover {
  color: white;
  background: #f57d1b;
}

.toolbar-info {
  background-color: #0bb2d4;
}

.toolbar-info.tool-top .arrow {
  border-color: #0bb2d4 transparent transparent;
}

.toolbar-info.tool-bottom .arrow {
  border-color: transparent transparent #0bb2d4;
}

.toolbar-info.tool-left .arrow {
  border-color: transparent transparent transparent #0bb2d4;
}

.toolbar-info.tool-right .arrow {
  border-color: transparent #0bb2d4 transparent transparent;
}

.toolbar-info .tool-item > .icon {
  color: white;
}

.toolbar-info .tool-item.selected, .toolbar-info .tool-item:hover {
  color: white;
  background: #28c0de;
}

.toolbar-success {
  background-color: #11c26d;
}

.toolbar-success.tool-top .arrow {
  border-color: #11c26d transparent transparent;
}

.toolbar-success.tool-bottom .arrow {
  border-color: transparent transparent #11c26d;
}

.toolbar-success.tool-left .arrow {
  border-color: transparent transparent transparent #11c26d;
}

.toolbar-success.tool-right .arrow {
  border-color: transparent #11c26d transparent transparent;
}

.toolbar-success .tool-item > .icon {
  color: white;
}

.toolbar-success .tool-item.selected, .toolbar-success .tool-item:hover {
  color: white;
  background: #28d17c;
}

.toolbar-info-o {
  background-color: #526069;
}

.toolbar-info-o.tool-top .arrow {
  border-color: #526069 transparent transparent;
}

.toolbar-info-o.tool-bottom .arrow {
  border-color: transparent transparent #526069;
}

.toolbar-info-o.tool-left .arrow {
  border-color: transparent transparent transparent #526069;
}

.toolbar-info-o.tool-right .arrow {
  border-color: transparent #526069 transparent transparent;
}

.toolbar-info-o .tool-item > .icon {
  color: white;
}

.toolbar-info-o .tool-item.selected, .toolbar-info-o .tool-item:hover {
  color: white;
  background: #76838f;
}

.toolbar-light {
  background-color: #f3f7f9;
}

.toolbar-light.tool-top .arrow {
  border-color: #f3f7f9 transparent transparent;
}

.toolbar-light.tool-bottom .arrow {
  border-color: transparent transparent #f3f7f9;
}

.toolbar-light.tool-left .arrow {
  border-color: transparent transparent transparent #f3f7f9;
}

.toolbar-light.tool-right .arrow {
  border-color: transparent #f3f7f9 transparent transparent;
}

.toolbar-light .tool-item > .icon {
  color: white;
}

.toolbar-light .tool-item.selected, .toolbar-light .tool-item:hover {
  color: white;
  background: #e4eaec;
}

.toolbar-dark {
  background-color: #76838f;
}

.toolbar-dark.tool-top .arrow {
  border-color: #76838f transparent transparent;
}

.toolbar-dark.tool-bottom .arrow {
  border-color: transparent transparent #76838f;
}

.toolbar-dark.tool-left .arrow {
  border-color: transparent transparent transparent #76838f;
}

.toolbar-dark.tool-right .arrow {
  border-color: transparent #76838f transparent transparent;
}

.toolbar-dark .tool-item > .icon {
  color: white;
}

.toolbar-dark .tool-item.selected, .toolbar-dark .tool-item:hover {
  color: white;
  background: #526069;
}

.animate-standard {
  -webkit-animation: standardAnimate .3s 1 ease;
}

.animate-flyin {
  -webkit-animation: rotateAnimate .5s 1 ease;
}

.animate-grow {
  -webkit-animation: growAnimate .4s 1 ease;
}

.animate-flip {
  -webkit-animation: flipAnimate .4s 1 ease;
}

.animate-bounce {
  -webkit-animation: bounceAnimate .4s 1 ease-out;
}

@-webkit-keyframes rotateAnimate {
  from {
    opacity: 0;
    -webkit-transform: rotate(180deg) translate(-120px);
    transform: rotate(180deg) translate(-120px);
  }

  to {
    opacity: 1;
    -webkit-transform: rotate(0deg) translate(0);
    transform: rotate(0deg) translate(0);
  }
}

@-webkit-keyframes standardAnimate {
  from {
    opacity: 0;
    -webkit-transform: translateY(20px);
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

@-webkit-keyframes growAnimate {
  0% {
    opacity: 0;
    -webkit-transform: scale(0) translateY(40px);
    transform: scale(0) translateY(40px);
  }

  70% {
    -webkit-transform: scale(1.5) translate(0);
    transform: scale(1.5) translate(0);
  }

  100% {
    opacity: 1;
    -webkit-transform: scale(1) translate(0);
    transform: scale(1) translate(0);
  }
}

@-webkit-keyframes rotate2Animate {
  from {
    opacity: 0;
    -webkit-transform: rotate(-90deg);
    transform: rotate(-90deg);
    -webkit-transform-origin: 0% 100%;
    transform-origin: 0% 100%;
  }

  to {
    opacity: 1;
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
}

@-webkit-keyframes flipAnimate {
  from {
    opacity: 0;
    -webkit-transform: rotate3d(2, 2, 2, 180deg);
    transform: rotate3d(2, 2, 2, 180deg);
  }

  to {
    opacity: 1;
    -webkit-transform: rotate3d(0, 0, 0, 0deg);
    transform: rotate3d(0, 0, 0, 0deg);
  }
}

@-webkit-keyframes bounceAnimate {
  0% {
    opacity: 0;
    -webkit-transform: translateY(40px);
    transform: translateY(40px);
  }

  30% {
    -webkit-transform: translateY(-40px);
    transform: translateY(-40px);
  }

  70% {
    -webkit-transform: translateY(20px);
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

.hidden {
  display: none;
}

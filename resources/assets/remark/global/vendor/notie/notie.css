.notie-transition {
  transition: all .3s ease;
}

.notie-background-success {
  background-color: #28d17c !important;
}

.notie-background-warning {
  background-color: #f57d1b !important;
}

.notie-background-error {
  background-color: #ff666b !important;
}

.notie-background-info {
  background-color: #28c0de !important;
}

#notie-alert-outer, #notie-confirm-outer, #notie-force-outer, #notie-input-outer, #notie-select-outer, #notie-date-outer {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999999999;
  display: none;
  width: 100%;
  height: auto;
  font-size: 24px;
  text-align: center;
  cursor: pointer;
  -ms-box-shadow: 0 0 10px 0 rgba(0, 0, 0, .5);
  -o-box-shadow: 0 0 10px 0 rgba(0, 0, 0, .5);
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, .5);
}

@media (max-width: 600px) {
  #notie-alert-outer, #notie-confirm-outer, #notie-force-outer, #notie-input-outer, #notie-select-outer, #notie-date-outer {
    font-size: 18px;
  }
}

#notie-alert-inner {
  display: table-cell;
  padding: 20px;
}

#notie-alert-content {
  max-width: 900px;
  margin: 0 auto;
}

#notie-alert-text {
  color: #fff;
}

#notie-confirm-outer {
  cursor: default;
}

#notie-confirm-inner, #notie-force-inner, #notie-input-inner, #notie-select-inner {
  box-sizing: border-box;
  display: block;
  width: 100%;
  padding: 20px;
  cursor: default;
  background-color: #28c0de;
}

#notie-confirm-text {
  color: #fff;
}

#notie-confirm-text-yes {
  color: #fff;
}

#notie-confirm-text-no {
  color: #fff;
}

#notie-confirm-yes, #notie-confirm-no, #notie-input-no, #notie-input-yes {
  float: left;
  width: 50%;
  height: 50px;
  line-height: 50px;
  cursor: pointer;
  background-color: #28d17c;
}

#notie-confirm-no, #notie-input-no {
  float: right;
  background-color: #ff666b;
}

#notie-confirm-background, #notie-force-background, #notie-input-background, #notie-select-background, #notie-date-background {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999999980;
  display: none;
  width: 100%;
  height: 100%;
  background-color: #fff;
  opacity: 0;
}

/* FORCE */
#notie-force-outer {
  cursor: default;
}

#notie-force-text {
  color: #fff;
}

#notie-force-button {
  width: 100%;
  height: 50px;
  line-height: 50px;
  color: #fff;
  cursor: pointer;
}

/* INPUT */
#notie-input-outer {
  cursor: default;
}

#notie-input-field {
  box-sizing: border-box;
  display: block;
  width: 100%;
  height: 55px;
  font-family: inherit;
  font-size: 24px;
  text-align: center;
  background-color: #fff;
  border: 0;
  outline: 0;
}

@media (max-width: 600px) {
  #notie-input-field {
    font-size: 18px;
  }
}

#notie-input-text {
  color: #fff;
}

#notie-input-text-yes {
  color: #fff;
}

#notie-input-text-no {
  color: #fff;
}

#notie-select-outer {
  top: auto;
  bottom: 0;
  cursor: default;
}

#notie-select-text {
  color: #fff;
}

#notie-select-choices, .notie-select-choice {
  background-color: #28d17c;
}

.notie-select-choice {
  height: 50px;
  line-height: 50px;
  color: #fff;
  cursor: pointer;
}

#notie-select-cancel {
  height: 60px;
  line-height: 60px;
  color: #fff;
  cursor: pointer;
  background-color: #a3afb7;
}

.notie-select-choice-bottom-border {
  border-bottom: 1px solid rgba(255, 255, 255, .2);
}

#notie-date-outer {
  color: #fff;
  cursor: default;
  background-color: #28c0de;
}

#notie-date-selector {
  max-width: 900px;
  padding-right: 10px;
  padding-left: 10px;
  margin: 0 auto;
}

.notie-date-up, .notie-date-down {
  float: left;
  width: 33.333333%;
  height: 50px;
  cursor: pointer;
}

.notie-date-arrow, .notie-date-arrow-up, .notie-date-arrow-down {
  width: 40px;
  height: 40px;
  margin: 0 auto;
  margin-top: 5px;
  background-size: 40px 40px;
}

.notie-date-arrow-up {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/PjxzdmcgaGVpZ2h0PSI0OCIgdmlld0JveD0iMCAwIDQ4IDQ4IiB3aWR0aD0iNDgiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE0IDI4bDEwLTEwIDEwIDEweiIgZmlsbD0id2hpdGUiLz48cGF0aCBkPSJNMCAwaDQ4djQ4aC00OHoiIGZpbGw9Im5vbmUiLz48L3N2Zz4=);
}

.notie-date-arrow-down {
  background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/PjxzdmcgaGVpZ2h0PSI0OCIgdmlld0JveD0iMCAwIDQ4IDQ4IiB3aWR0aD0iNDgiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE0IDIwbDEwIDEwIDEwLTEweiIgZmlsbD0id2hpdGUiLz48cGF0aCBkPSJNMCAwaDQ4djQ4aC00OHoiIGZpbGw9Im5vbmUiLz48L3N2Zz4=");
}

.notie-date-text {
  float: left;
  width: 33.333333%;
  height: 50px;
  line-height: 50px;
}

#notie-date-yes, #notie-date-no {
  float: left;
  width: 50%;
  height: 50px;
  line-height: 50px;
  color: #fff;
  cursor: pointer;
  background-color: #28d17c;
}

#notie-date-no {
  background-color: #ff666b;
}

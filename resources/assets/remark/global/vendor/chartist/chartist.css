.ct-label {
  font-size: .858rem;
  line-height: 1;
  color: #a3afb7;
  fill: #a3afb7;
}

.ct-chart-line .ct-label, .ct-chart-bar .ct-label {
  display: block;
  display: -ms-flexbox;
  display: flex;
}

.ct-label.ct-horizontal.ct-start {
  -ms-flex-align: flex-end;
  -ms-flex-pack: flex-start;
  align-items: flex-end;
  justify-content: flex-start;
  text-align: left;
  text-anchor: start;
}

.ct-label.ct-horizontal.ct-end {
  -ms-flex-align: flex-start;
  -ms-flex-pack: flex-start;
  align-items: flex-start;
  justify-content: flex-start;
  text-align: left;
  text-anchor: start;
}

.ct-label.ct-vertical.ct-start {
  -ms-flex-align: flex-end;
  -ms-flex-pack: flex-end;
  align-items: flex-end;
  justify-content: flex-end;
  text-align: right;
  text-anchor: end;
}

.ct-label.ct-vertical.ct-end {
  -ms-flex-align: flex-end;
  -ms-flex-pack: flex-start;
  align-items: flex-end;
  justify-content: flex-start;
  text-align: left;
  text-anchor: start;
}

.ct-chart-bar .ct-label.ct-horizontal.ct-start {
  -ms-flex-align: flex-end;
  -ms-flex-pack: center;
  align-items: flex-end;
  justify-content: center;
  text-align: center;
  text-anchor: start;
}

.ct-chart-bar .ct-label.ct-horizontal.ct-end {
  -ms-flex-align: flex-start;
  -ms-flex-pack: center;
  align-items: flex-start;
  justify-content: center;
  text-align: center;
  text-anchor: start;
}

.ct-chart-bar.ct-horizontal-bars .ct-label.ct-horizontal.ct-start {
  -ms-flex-align: flex-end;
  -ms-flex-pack: flex-start;
  align-items: flex-end;
  justify-content: flex-start;
  text-align: left;
  text-anchor: start;
}

.ct-chart-bar.ct-horizontal-bars .ct-label.ct-horizontal.ct-end {
  -ms-flex-align: flex-start;
  -ms-flex-pack: flex-start;
  align-items: flex-start;
  justify-content: flex-start;
  text-align: left;
  text-anchor: start;
}

.ct-chart-bar.ct-horizontal-bars .ct-label.ct-vertical.ct-start {
  -ms-flex-align: center;
  -ms-flex-pack: flex-end;
  align-items: center;
  justify-content: flex-end;
  text-align: right;
  text-anchor: end;
}

.ct-chart-bar.ct-horizontal-bars .ct-label.ct-vertical.ct-end {
  -ms-flex-align: center;
  -ms-flex-pack: flex-start;
  align-items: center;
  justify-content: flex-start;
  text-align: left;
  text-anchor: end;
}

.ct-grid {
  stroke: rgba(0, 0, 0, .1);
  stroke-width: 1px;
  stroke-dasharray: 2px;
}

.ct-point {
  stroke-width: 8px;
  stroke-linecap: round;
}

.ct-line {
  fill: none;
  stroke-width: 3px;
}

.ct-area {
  stroke: none;
  fill-opacity: .15;
}

.ct-bar {
  fill: none;
  stroke-width: 10px;
}

.ct-slice-donut {
  fill: none;
  stroke-width: 60px;
}

.ct-series-a .ct-point, .ct-series-a .ct-line, .ct-series-a .ct-bar, .ct-series-a .ct-slice-donut {
  stroke: #3e8ef7;
}

.ct-series-a .ct-slice-pie, .ct-series-a .ct-area {
  fill: #3e8ef7;
}

.ct-series-b .ct-point, .ct-series-b .ct-line, .ct-series-b .ct-bar, .ct-series-b .ct-slice-donut {
  stroke: #49de94;
}

.ct-series-b .ct-slice-pie, .ct-series-b .ct-area {
  fill: #49de94;
}

.ct-series-c .ct-point, .ct-series-c .ct-line, .ct-series-c .ct-bar, .ct-series-c .ct-slice-donut {
  stroke: #ff666b;
}

.ct-series-c .ct-slice-pie, .ct-series-c .ct-area {
  fill: #ff666b;
}

.ct-series-d .ct-point, .ct-series-d .ct-line, .ct-series-d .ct-bar, .ct-series-d .ct-slice-donut {
  stroke: #9463f7;
}

.ct-series-d .ct-slice-pie, .ct-series-d .ct-area {
  fill: #9463f7;
}

.ct-series-e .ct-point, .ct-series-e .ct-line, .ct-series-e .ct-bar, .ct-series-e .ct-slice-donut {
  stroke: #ffcd17;
}

.ct-series-e .ct-slice-pie, .ct-series-e .ct-area {
  fill: #ffcd17;
}

.ct-series-f .ct-point, .ct-series-f .ct-line, .ct-series-f .ct-bar, .ct-series-f .ct-slice-donut {
  stroke: #f74584;
}

.ct-series-f .ct-slice-pie, .ct-series-f .ct-area {
  fill: #f74584;
}

.ct-series-g .ct-point, .ct-series-g .ct-line, .ct-series-g .ct-bar, .ct-series-g .ct-slice-donut {
  stroke: #96a3fa;
}

.ct-series-g .ct-slice-pie, .ct-series-g .ct-area {
  fill: #96a3fa;
}

.ct-series-h .ct-point, .ct-series-h .ct-line, .ct-series-h .ct-bar, .ct-series-h .ct-slice-donut {
  stroke: #5a9101;
}

.ct-series-h .ct-slice-pie, .ct-series-h .ct-area {
  fill: #5a9101;
}

.ct-series-i .ct-point, .ct-series-i .ct-line, .ct-series-i .ct-bar, .ct-series-i .ct-slice-donut {
  stroke: #fa983c;
}

.ct-series-i .ct-slice-pie, .ct-series-i .ct-area {
  fill: #fa983c;
}

.ct-series-j .ct-point, .ct-series-j .ct-line, .ct-series-j .ct-bar, .ct-series-j .ct-slice-donut {
  stroke: #54cbe3;
}

.ct-series-j .ct-slice-pie, .ct-series-j .ct-area {
  fill: #54cbe3;
}

.ct-series-k .ct-point, .ct-series-k .ct-line, .ct-series-k .ct-bar, .ct-series-k .ct-slice-donut {
  stroke: #17b3a3;
}

.ct-series-k .ct-slice-pie, .ct-series-k .ct-area {
  fill: #17b3a3;
}

.ct-series-l .ct-point, .ct-series-l .ct-line, .ct-series-l .ct-bar, .ct-series-l .ct-slice-donut {
  stroke: #ab8c82;
}

.ct-series-l .ct-slice-pie, .ct-series-l .ct-area {
  fill: #ab8c82;
}

.ct-series-m .ct-point, .ct-series-m .ct-line, .ct-series-m .ct-bar, .ct-series-m .ct-slice-donut {
  stroke: #bdbdbd;
}

.ct-series-m .ct-slice-pie, .ct-series-m .ct-area {
  fill: #bdbdbd;
}

.ct-series-n .ct-point, .ct-series-n .ct-line, .ct-series-n .ct-bar, .ct-series-n .ct-slice-donut {
  stroke: #76838f;
}

.ct-series-n .ct-slice-pie, .ct-series-n .ct-area {
  fill: #76838f;
}

.ct-square {
  position: relative;
  display: block;
  width: 100%;
}

.ct-square:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 100%;
  content: "";
}

.ct-square:after {
  display: table;
  clear: both;
  content: "";
}

.ct-square > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-minor-second {
  position: relative;
  display: block;
  width: 100%;
}

.ct-minor-second:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 93.75%;
  content: "";
}

.ct-minor-second:after {
  display: table;
  clear: both;
  content: "";
}

.ct-minor-second > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-major-second {
  position: relative;
  display: block;
  width: 100%;
}

.ct-major-second:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 88.8888888889%;
  content: "";
}

.ct-major-second:after {
  display: table;
  clear: both;
  content: "";
}

.ct-major-second > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-minor-third {
  position: relative;
  display: block;
  width: 100%;
}

.ct-minor-third:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 83.3333333333%;
  content: "";
}

.ct-minor-third:after {
  display: table;
  clear: both;
  content: "";
}

.ct-minor-third > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-major-third {
  position: relative;
  display: block;
  width: 100%;
}

.ct-major-third:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 80%;
  content: "";
}

.ct-major-third:after {
  display: table;
  clear: both;
  content: "";
}

.ct-major-third > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-perfect-fourth {
  position: relative;
  display: block;
  width: 100%;
}

.ct-perfect-fourth:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 75%;
  content: "";
}

.ct-perfect-fourth:after {
  display: table;
  clear: both;
  content: "";
}

.ct-perfect-fourth > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-perfect-fifth {
  position: relative;
  display: block;
  width: 100%;
}

.ct-perfect-fifth:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 66.6666666667%;
  content: "";
}

.ct-perfect-fifth:after {
  display: table;
  clear: both;
  content: "";
}

.ct-perfect-fifth > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-minor-sixth {
  position: relative;
  display: block;
  width: 100%;
}

.ct-minor-sixth:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 62.5%;
  content: "";
}

.ct-minor-sixth:after {
  display: table;
  clear: both;
  content: "";
}

.ct-minor-sixth > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-golden-section {
  position: relative;
  display: block;
  width: 100%;
}

.ct-golden-section:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 61.804697157%;
  content: "";
}

.ct-golden-section:after {
  display: table;
  clear: both;
  content: "";
}

.ct-golden-section > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-major-sixth {
  position: relative;
  display: block;
  width: 100%;
}

.ct-major-sixth:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 60%;
  content: "";
}

.ct-major-sixth:after {
  display: table;
  clear: both;
  content: "";
}

.ct-major-sixth > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-minor-seventh {
  position: relative;
  display: block;
  width: 100%;
}

.ct-minor-seventh:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 56.25%;
  content: "";
}

.ct-minor-seventh:after {
  display: table;
  clear: both;
  content: "";
}

.ct-minor-seventh > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-major-seventh {
  position: relative;
  display: block;
  width: 100%;
}

.ct-major-seventh:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 53.3333333333%;
  content: "";
}

.ct-major-seventh:after {
  display: table;
  clear: both;
  content: "";
}

.ct-major-seventh > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-octave {
  position: relative;
  display: block;
  width: 100%;
}

.ct-octave:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 50%;
  content: "";
}

.ct-octave:after {
  display: table;
  clear: both;
  content: "";
}

.ct-octave > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-major-tenth {
  position: relative;
  display: block;
  width: 100%;
}

.ct-major-tenth:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 40%;
  content: "";
}

.ct-major-tenth:after {
  display: table;
  clear: both;
  content: "";
}

.ct-major-tenth > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-major-eleventh {
  position: relative;
  display: block;
  width: 100%;
}

.ct-major-eleventh:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 37.5%;
  content: "";
}

.ct-major-eleventh:after {
  display: table;
  clear: both;
  content: "";
}

.ct-major-eleventh > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-major-twelfth {
  position: relative;
  display: block;
  width: 100%;
}

.ct-major-twelfth:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 33.3333333333%;
  content: "";
}

.ct-major-twelfth:after {
  display: table;
  clear: both;
  content: "";
}

.ct-major-twelfth > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-double-octave {
  position: relative;
  display: block;
  width: 100%;
}

.ct-double-octave:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 25%;
  content: "";
}

.ct-double-octave:after {
  display: table;
  clear: both;
  content: "";
}

.ct-double-octave > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

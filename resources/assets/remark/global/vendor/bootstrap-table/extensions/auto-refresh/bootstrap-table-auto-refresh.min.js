/*
* bootstrap-table - v1.11.1 - 2017-02-22
* https://github.com/wenzhixin/bootstrap-table
* Copyright (c) 2017 zhixin wen
* Licensed MIT License
*/
!function(a){"use strict";a.extend(a.fn.bootstrapTable.defaults,{autoRefresh:!1,autoRefreshInterval:60,autoRefreshSilent:!0,autoRefreshStatus:!0,autoRefreshFunction:null}),a.extend(a.fn.bootstrapTable.defaults.icons,{autoRefresh:"glyphicon-time icon-time"}),a.extend(a.fn.bootstrapTable.locales,{formatAutoRefresh:function(){return"Auto Refresh"}}),a.extend(a.fn.bootstrapTable.defaults,a.fn.bootstrapTable.locales);var b=a.fn.bootstrapTable.Constructor,c=b.prototype.init,d=b.prototype.initToolbar,e=a.fn.bootstrapTable.utils.sprintf;b.prototype.init=function(){if(c.apply(this,Array.prototype.slice.apply(arguments)),this.options.autoRefresh&&this.options.autoRefreshStatus){var a=this;this.options.autoRefreshFunction=setInterval(function(){a.refresh({silent:a.options.autoRefreshSilent})},1e3*this.options.autoRefreshInterval)}},b.prototype.initToolbar=function(){if(d.apply(this,Array.prototype.slice.apply(arguments)),this.options.autoRefresh){var b=this.$toolbar.find(">.btn-group"),c=b.find(".auto-refresh");c.length||(c=a([e('<button class="btn btn-default auto-refresh %s" ',this.options.autoRefreshStatus?"enabled":""),'type="button" ',e('title="%s">',this.options.formatAutoRefresh()),e('<i class="%s %s"></i>',this.options.iconsPrefix,this.options.icons.autoRefresh),"</button>"].join("")).appendTo(b),c.on("click",a.proxy(this.toggleAutoRefresh,this)))}},b.prototype.toggleAutoRefresh=function(){if(this.options.autoRefresh){if(this.options.autoRefreshStatus)clearInterval(this.options.autoRefreshFunction),this.$toolbar.find(">.btn-group").find(".auto-refresh").removeClass("enabled");else{var a=this;this.options.autoRefreshFunction=setInterval(function(){a.refresh({silent:a.options.autoRefreshSilent})},1e3*this.options.autoRefreshInterval),this.$toolbar.find(">.btn-group").find(".auto-refresh").addClass("enabled")}this.options.autoRefreshStatus=!this.options.autoRefreshStatus}}}(jQuery);
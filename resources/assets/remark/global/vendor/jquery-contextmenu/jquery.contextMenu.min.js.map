{"version": 3, "sources": ["jquery.contextMenu.min.js"], "names": ["factory", "define", "amd", "exports", "require", "j<PERSON><PERSON><PERSON>", "$", "splitAccesskey", "val", "k", "t", "split", "keys", "i", "char<PERSON>t", "toUpperCase", "push", "inputLabel", "node", "id", "name", "menu<PERSON><PERSON><PERSON>n", "items", "$children", "counter", "each", "label", "item", "$node", "this", "nodeName", "toLowerCase", "find", "length", "text", "children", "first", "get", "attr", "disabled", "callback", "click", "undefined", "icon", "type", "selected", "radio", "value", "options", "html", "clone", "support", "htmlMenuitem", "window", "htmlCommand", "eventSelectstart", "document", "documentElement", "ui", "widget", "cleanData", "orig", "elems", "events", "elem", "_data", "remove", "<PERSON><PERSON><PERSON><PERSON>", "e", "$currentTrigger", "initialized", "$win", "namespaces", "menus", "types", "defaults", "selector", "appendTo", "trigger", "autoHide", "delay", "reposition", "hideOnSecondTrigger", "selectableSubMenu", "classNames", "hover", "visible", "notSelectable", "iconEdit", "iconCut", "iconCopy", "iconPaste", "iconDelete", "iconAdd", "iconQuit", "iconLoadingClass", "determinePosition", "$menu", "position", "css", "my", "at", "of", "offset", "collision", "top", "outerHeight", "left", "outerWidth", "opt", "x", "y", "offsetParentOffset", "offsetParent", "bottom", "scrollTop", "height", "right", "scrollLeft", "width", "call", "positionSubmenu", "zIndex", "animation", "duration", "show", "hide", "noop", "activated", "hoveract", "timer", "pageX", "pageY", "zindex", "$t", "zin", "$tt", "Math", "max", "parseInt", "parent", "indexOf", "prop", "handle", "abortevent", "preventDefault", "stopImmediatePropagation", "contextmenu", "$this", "data", "originalEvent", "mouseButton", "hasClass", "build", "built", "extend", "isEmptyObject", "console", "error", "log", "Error", "$trigger", "op", "create", "showMenu", "hasOwnProperty", "isFunction", "currentTarget", "Event", "mousedown", "is", "button", "mouseup", "removeData", "mouseenter", "$related", "relatedTarget", "$document", "closest", "on", "mousemove", "setTimeout", "off", "mouseleave", "clearTimeout", "layerClick", "target", "root", "$window", "triggerAction", "elementFromPoint", "$layer", "isContentEditable", "range", "createRange", "sel", "getSelection", "selectNode", "collapse", "removeAllRanges", "addRange", "one", "contextMenu", "keyStop", "isInput", "stopPropagation", "key", "targetZIndex", "getZIndexOfTriggerTarget", "style", "parentElement", "keyCode", "shift<PERSON>ey", "$selected", "blur", "$parent", "itemdata", "String", "fromCharCode", "accesskeys", "prevItem", "$s", "$prev", "prev", "last", "$round", "itemMouseleave", "itemMouseenter", "$input", "focus", "nextItem", "$next", "next", "focusInput", "contextMenuRoot", "blurInput", "menuMouseenter", "hovering", "menuMouseleave", "itemClick", "contextMenuKey", "callbacks", "Object", "prototype", "update", "inputClick", "hideMenu", "force", "focusItem", "addClass", "join", "not", "removeClass", "filter", "blurItem", "additionalZValue", "layer", "pos", "createNameNode", "$name", "_accesskey", "_beforeAccesskey", "append", "createTextNode", "_afterAccesskey", "isHtmlName", "accesskey", "className", "$label", "ak", "aks", "matched", "match", "RegExp", "commands", "hasTypes", "inputs", "prependTo", "then", "processPromises", "_icon", "substring", "body", "resize", "nested", "domMenu", "display", "getBoundingClientRect", "ceil", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "addBack", "$item", "z-index", "opacity", "background-color", "insertBefore", "promise", "errorPromise", "errorItem", "finishPromiseProcess", "bind", "$menuOffset", "winHeight", "winScrollTop", "menuHeight", "overflow-x", "overflow-y", "fn", "operation", "$o", "context", "isPlainObject", "o", "$context", "_hasContext", "menu", "ns", "itemClickEvent", "contextMenuItemObj", "contextmenu:focus.contextMenu", "contextmenu:blur.contextMenu", "contextmenu.contextMenu", "mouseenter.contextMenu", "mouseleave.contextMenu", "contextmenu:hide.contextMenu", "prevcommand.contextMenu", "nextcommand.contextMenu", "$visibleMenu", "fromMenu", "setInputValues", "getInputValues", "element"], "mappings": "CAkBA,SAAWA,GACe,mBAAXC,QAAyBA,OAAOC,IAEvCD,QAAQ,UAAWD,GAGnBA,EAF0B,iBAAZG,QAENC,QAAQ,UAGRC,QAThB,CAWG,SAAUC,GAET,aA+/CA,SAASC,EAAeC,GAIpB,IAAK,IAAWC,EAHZC,EAAIF,EAAIG,MAAM,OACdC,KAEKC,EAAI,EAAMJ,EAAIC,EAAEG,GAAIA,IACzBJ,EAAIA,EAAEK,OAAO,GAAGC,cAGhBH,EAAKI,KAAKP,GAGd,OAAOG,EAwUX,SAASK,EAAWC,GAChB,OAAQA,EAAKC,IAAMb,EAAE,cAAgBY,EAAKC,GAAK,MAAMX,OAAUU,EAAKE,KAIxE,SAASC,EAAaC,EAAOC,EAAWC,GA0KpC,OAzKKA,IACDA,EAAU,GAGdD,EAAUE,KAAK,WACX,IAGIC,EACAC,EAJAC,EAAQtB,EAAEuB,MACVX,EAAOW,KACPC,EAAWD,KAAKC,SAASC,cAoB7B,OAfiB,UAAbD,GAAwBF,EAAMI,KAAK,2BAA2BC,SAC9DP,EAAQE,EAAMM,OAGdJ,GADAZ,GADAU,EAAQA,EAAMO,WAAWC,SACZC,IAAI,IACDP,SAASC,eAWrBD,GAEJ,IAAK,OACDH,GAAQP,KAAMQ,EAAMU,KAAK,SAAUhB,UACnCE,EAAUH,EAAaM,EAAKL,MAAOM,EAAMO,WAAYX,GACrD,MAGJ,IAAK,IAEL,IAAK,SACDG,GACIP,KAAMQ,EAAMM,OACZK,WAAYX,EAAMU,KAAK,YACvBE,SACW,WACHZ,EAAMS,IAAI,GAAGI,UAIzB,MAGJ,IAAK,WACL,IAAK,UACD,OAAQb,EAAMU,KAAK,SACf,UAAKI,EACL,IAAK,UACL,IAAK,WACDf,GACIP,KAAMQ,EAAMU,KAAK,SACjBC,WAAYX,EAAMU,KAAK,YACvBK,KAAMf,EAAMU,KAAK,QACjBE,SACW,WACHZ,EAAMS,IAAI,GAAGI,UAIzB,MAEJ,IAAK,WACDd,GACIiB,KAAM,WACNL,WAAYX,EAAMU,KAAK,YACvBlB,KAAMQ,EAAMU,KAAK,SACjBO,WAAYjB,EAAMU,KAAK,YAE3B,MACJ,IAAK,QACDX,GACIiB,KAAM,QACNL,WAAYX,EAAMU,KAAK,YACvBlB,KAAMQ,EAAMU,KAAK,SACjBQ,MAAOlB,EAAMU,KAAK,cAClBS,MAAOnB,EAAMU,KAAK,MAClBO,WAAYjB,EAAMU,KAAK,YAE3B,MAEJ,QACIX,OAAOe,EAEf,MAEJ,IAAK,KACDf,EAAO,UACP,MAEJ,IAAK,QACD,OAAQC,EAAMU,KAAK,SACf,IAAK,OACDX,GACIiB,KAAM,OACNxB,KAAMM,GAAST,EAAWC,GAC1BqB,WAAYX,EAAMU,KAAK,YACvBS,MAAOnB,EAAMpB,OAEjB,MAEJ,IAAK,WACDmB,GACIiB,KAAM,WACNxB,KAAMM,GAAST,EAAWC,GAC1BqB,WAAYX,EAAMU,KAAK,YACvBO,WAAYjB,EAAMU,KAAK,YAE3B,MAEJ,IAAK,QACDX,GACIiB,KAAM,QACNxB,KAAMM,GAAST,EAAWC,GAC1BqB,WAAYX,EAAMU,KAAK,YACvBQ,QAASlB,EAAMU,KAAK,QACpBS,MAAOnB,EAAMpB,MACbqC,WAAYjB,EAAMU,KAAK,YAE3B,MAEJ,QACIX,OAAOe,EAGf,MAEJ,IAAK,SACDf,GACIiB,KAAM,SACNxB,KAAMM,GAAST,EAAWC,GAC1BqB,WAAYX,EAAMU,KAAK,YACvBO,SAAUjB,EAAMpB,MAChBwC,YAEJpB,EAAMO,WAAWV,KAAK,WAClBE,EAAKqB,QAAQnB,KAAKkB,OAASzC,EAAEuB,MAAMK,SAEvC,MAEJ,IAAK,WACDP,GACIiB,KAAM,WACNxB,KAAMM,GAAST,EAAWC,GAC1BqB,WAAYX,EAAMU,KAAK,YACvBS,MAAOnB,EAAMpB,OAEjB,MAEJ,IAAK,QACD,MAEJ,QACImB,GAAQiB,KAAM,OAAQK,KAAMrB,EAAMsB,OAAM,IAI5CvB,IAEAL,EAAM,SADNE,GACyBG,KAI1BH,EA1/DXlB,EAAE6C,QAAQC,aAAgB,wBAAyBC,OACnD/C,EAAE6C,QAAQG,YAAe,uBAAwBD,OACjD/C,EAAE6C,QAAQI,iBAAoB,kBAAmBC,SAASC,gBAwBrDnD,EAAEoD,IAAOpD,EAAEqD,SAEZrD,EAAEsD,UAAY,SAAWC,GACrB,OAAO,SAAUC,GACb,IAAIC,EAAQC,EAAMnD,EAClB,IAAKA,EAAI,EAAe,MAAZiD,EAAMjD,GAAYA,IAAK,CAC/BmD,EAAOF,EAAMjD,GACb,KAEIkD,EAASzD,EAAE2D,MAAMD,EAAM,YACTD,EAAOG,QACjB5D,EAAE0D,GAAMG,eAAe,UAI7B,MAAOC,KAGbP,EAAKC,IAhBC,CAkBXxD,EAAEsD,YAKT,IACIS,EAAkB,KAElBC,GAAc,EAEdC,EAAOjE,EAAE+C,QAET7B,EAAU,EAEVgD,KAEAC,KAEAC,KAEAC,GAEIC,SAAU,KAEVC,SAAU,KAEVC,QAAS,QAETC,UAAU,EAEVC,MAAO,IAGPC,YAAY,EAIZC,qBAAqB,EAGrBC,mBAAmB,EAGnBC,YACIC,MAAO,qBACP9C,SAAU,wBACV+C,QAAS,uBACTC,cAAe,8BAEf5C,KAAM,oBACN6C,SAAU,yBACVC,QAAS,wBACTC,SAAU,yBACVC,UAAW,0BACXC,WAAY,2BACZC,QAAS,wBACTC,SAAU,yBACVC,iBAAkB,6BAItBC,kBAAmB,SAAUC,GAEzB,GAAI3F,EAAEoD,IAAMpD,EAAEoD,GAAGwC,SAGbD,EAAME,IAAI,UAAW,SAASD,UAC1BE,GAAI,aACJC,GAAI,gBACJC,GAAIzE,KACJ0E,OAAQ,MACRC,UAAW,QACZL,IAAI,UAAW,YACf,CAEH,IAAII,EAAS1E,KAAK0E,SAClBA,EAAOE,KAAO5E,KAAK6E,cACnBH,EAAOI,MAAQ9E,KAAK+E,aAAe,EAAIX,EAAMW,aAAe,EAC5DX,EAAME,IAAII,KAIlBL,SAAU,SAAUW,EAAKC,EAAGC,GACxB,IAAIR,EAEJ,GAAKO,GAAMC,EAAX,CAGO,GAAU,aAAND,GAA0B,aAANC,EAE3BR,EAASM,EAAIZ,MAAMC,eAChB,CAEH,IAAIc,EAAqBH,EAAIZ,MAAMgB,eAAeV,SAClDA,GAAUE,IAAKM,EAAIC,EAAmBP,IAAKE,KAAMG,EAAGE,EAAmBL,MAI3E,IAAIO,EAAS3C,EAAK4C,YAAc5C,EAAK6C,SACjCC,EAAQ9C,EAAK+C,aAAe/C,EAAKgD,QACjCH,EAASP,EAAIZ,MAAMS,cACnBa,EAAQV,EAAIZ,MAAMW,aAElBL,EAAOE,IAAMW,EAASF,IACtBX,EAAOE,KAAOW,GAGdb,EAAOE,IAAM,IACbF,EAAOE,IAAM,GAGbF,EAAOI,KAAOY,EAAQF,IACtBd,EAAOI,MAAQY,GAGfhB,EAAOI,KAAO,IACdJ,EAAOI,KAAO,GAGlBE,EAAIZ,MAAME,IAAII,QAjCVM,EAAIb,kBAAkBwB,KAAK3F,KAAMgF,EAAIZ,QAoC7CwB,gBAAiB,SAAUxB,GACvB,QAAqB,IAAVA,EAMX,GAAI3F,EAAEoD,IAAMpD,EAAEoD,GAAGwC,SAGbD,EAAME,IAAI,UAAW,SAASD,UAC1BE,GAAI,aACJC,GAAI,YACJC,GAAIzE,KACJ2E,UAAW,gBACZL,IAAI,UAAW,QACf,CAEH,IAAII,GACAE,KAAM,EACNE,KAAM9E,KAAK+E,aAAe,GAE9BX,EAAME,IAAII,KAIlBmB,OAAQ,EAERC,WACIC,SAAU,GACVC,KAAM,YACNC,KAAM,WAGV/D,QACI8D,KAAMvH,EAAEyH,KACRD,KAAMxH,EAAEyH,KACRC,UAAW1H,EAAEyH,MAGjBvF,SAAU,KAEVlB,UAGJ2G,GACIC,MAAO,KACPC,MAAO,KACPC,MAAO,MAGXC,EAAS,SAAUC,GAIf,IAHA,IAAIC,EAAM,EACNC,EAAMF,IAKN,GAFAC,EAAME,KAAKC,IAAIH,EAAKI,SAASH,EAAIrC,IAAI,WAAY,KAAO,KACxDqC,EAAMA,EAAII,YACGJ,EAAIvG,QAAU,YAAY4G,QAAQL,EAAIM,KAAK,YAAY/G,gBAAkB,EAClF,MAGR,OAAOwG,GAGXQ,GAEIC,WAAY,SAAU5E,GAClBA,EAAE6E,iBACF7E,EAAE8E,4BAGNC,YAAa,SAAU/E,GACnB,IAAIgF,EAAQ9I,EAAEuB,MASd,GANuB,UAAnBuC,EAAEiF,KAAKvE,UACPV,EAAE6E,iBACF7E,EAAE8E,8BAIkB,UAAnB9E,EAAEiF,KAAKvE,SAA0C,WAAnBV,EAAEiF,KAAKvE,SAAyBV,EAAEkF,sBAKxC,IAAlBlF,EAAEmF,cAA+BnF,EAAEiF,MACjB,SAAnBjF,EAAEiF,KAAKvE,SAAwC,IAAlBV,EAAEmF,aAA2C,UAAnBnF,EAAEiF,KAAKvE,SAAyC,IAAlBV,EAAEmF,cAO7FH,EAAMI,SAAS,wBAIdJ,EAAMI,SAAS,0BAA0B,CAO1C,GADAnF,EAAkB+E,EACdhF,EAAEiF,KAAKI,MAAO,CACd,IAAIC,EAAQtF,EAAEiF,KAAKI,MAAMpF,EAAiBD,GAE1C,IAAc,IAAVsF,EACA,OAOJ,GAHAtF,EAAEiF,KAAO/I,EAAEqJ,QAAO,KAAUhF,EAAUP,EAAEiF,KAAMK,QAGzCtF,EAAEiF,KAAK/H,OAAShB,EAAEsJ,cAAcxF,EAAEiF,KAAK/H,OAMxC,MAJI+B,OAAOwG,UACNA,QAAQC,OAASD,QAAQE,KAAKvC,KAAKqC,QAAS,6CAG3C,IAAIG,MAAM,sBAIpB5F,EAAEiF,KAAKY,SAAW5F,EAElB6F,EAAGC,OAAO/F,EAAEiF,MAEhB,IAAIe,GAAW,EACf,IAAK,IAAIzI,KAAQyC,EAAEiF,KAAK/H,MACpB,GAAI8C,EAAEiF,KAAK/H,MAAM+I,eAAe1I,GAAO,EAE/BrB,EAAEgK,WAAWlG,EAAEiF,KAAK/H,MAAMK,GAAM2D,SACtBlB,EAAEiF,KAAK/H,MAAMK,GAAM2D,QAAQkC,KAAKlH,EAAE8D,EAAEmG,eAAgB5I,EAAMyC,EAAEiF,WACjC,IAAvBjF,EAAEiF,KAAK/H,MAAMK,KAAyByC,EAAEiF,KAAK/H,MAAMK,GAAM2D,UAC9B,IAA/BlB,EAAEiF,KAAK/H,MAAMK,GAAM2D,WAK7B8E,GAAW,GAInBA,GAEAF,EAAGrC,KAAKL,KAAK4B,EAAOhF,EAAEiF,KAAMjF,EAAE+D,MAAO/D,EAAEgE,SAKnD3F,MAAO,SAAU2B,GACbA,EAAE6E,iBACF7E,EAAE8E,2BACF5I,EAAEuB,MAAMiD,QAAQxE,EAAEkK,MAAM,eAAgBnB,KAAMjF,EAAEiF,KAAMlB,MAAO/D,EAAE+D,MAAOC,MAAOhE,EAAEgE,UAGnFqC,UAAW,SAAUrG,GAEjB,IAAIgF,EAAQ9I,EAAEuB,MAGVwC,GAAmBA,EAAgBpC,SAAWoC,EAAgBqG,GAAGtB,IACjE/E,EAAgBgF,KAAK,eAAepD,MAAMnB,QAAQ,oBAIrC,IAAbV,EAAEuG,SACFtG,EAAkB+E,EAAMC,KAAK,qBAAqB,KAI1DuB,QAAS,SAAUxG,GAEf,IAAIgF,EAAQ9I,EAAEuB,MACVuH,EAAMC,KAAK,sBAAwBhF,GAAmBA,EAAgBpC,QAAUoC,EAAgBqG,GAAGtB,KAAWA,EAAMI,SAAS,2BAC7HpF,EAAE6E,iBACF7E,EAAE8E,2BACF7E,EAAkB+E,EAClBA,EAAMtE,QAAQxE,EAAEkK,MAAM,eAAgBnB,KAAMjF,EAAEiF,KAAMlB,MAAO/D,EAAE+D,MAAOC,MAAOhE,EAAEgE,UAGjFgB,EAAMyB,WAAW,sBAGrBC,WAAY,SAAU1G,GAClB,IAAIgF,EAAQ9I,EAAEuB,MACVkJ,EAAWzK,EAAE8D,EAAE4G,eACfC,EAAY3K,EAAEkD,UAGduH,EAASL,GAAG,uBAAyBK,EAASG,QAAQ,sBAAsBjJ,QAK5EoC,GAAmBA,EAAgBpC,SAIvCgG,EAASE,MAAQ/D,EAAE+D,MACnBF,EAASG,MAAQhE,EAAEgE,MACnBH,EAASoB,KAAOjF,EAAEiF,KAClB4B,EAAUE,GAAG,4BAA6BpC,EAAOqC,WACjDnD,EAASC,MAAQmD,WAAW,WACxBpD,EAASC,MAAQ,KACjB+C,EAAUK,IAAI,6BACdjH,EAAkB+E,EAClBA,EAAMtE,QAAQxE,EAAEkK,MAAM,eAClBnB,KAAMpB,EAASoB,KACflB,MAAOF,EAASE,MAChBC,MAAOH,EAASG,UAErBhE,EAAEiF,KAAKrE,SAGdoG,UAAW,SAAUhH,GACjB6D,EAASE,MAAQ/D,EAAE+D,MACnBF,EAASG,MAAQhE,EAAEgE,OAGvBmD,WAAY,SAAUnH,GAElB,IAAI2G,EAAWzK,EAAE8D,EAAE4G,eACnB,IAAID,EAASL,GAAG,wBAAyBK,EAASG,QAAQ,sBAAsBjJ,OAAhF,CAIA,IACIuJ,aAAavD,EAASC,OACxB,MAAO9D,IAGT6D,EAASC,MAAQ,OAGrBuD,WAAY,SAAUrH,GAClB,IAKIsH,EACAnF,EALAoF,EADQrL,EAAEuB,MACGwH,KAAK,mBAClBsB,EAASvG,EAAEuG,OACX7D,EAAI1C,EAAE+D,MACNpB,EAAI3C,EAAEgE,MAIVhE,EAAE6E,iBAEFoC,WAAW,WACP,IAAIO,EACAC,EAAmC,SAAjBF,EAAK7G,SAAiC,IAAX6F,GAAmC,UAAjBgB,EAAK7G,SAAkC,IAAX6F,EAG/F,GAAInH,SAASsI,kBAAoBH,EAAKI,OAAQ,CAM1C,GALAJ,EAAKI,OAAOjE,QACZ4D,EAASlI,SAASsI,iBAAiBhF,EAAIvC,EAAK+C,aAAcP,EAAIxC,EAAK4C,cAIxD6E,kBAAmB,CAC1B,IAAIC,EAAQzI,SAAS0I,cACjBC,EAAM9I,OAAO+I,eACjBH,EAAMI,WAAWX,GACjBO,EAAMK,UAAS,GACfH,EAAII,kBACJJ,EAAIK,SAASP,GAEjB3L,EAAEoL,GAAQ5G,QAAQV,GAClBuH,EAAKI,OAAOlE,OAGhB,GAAI8D,EAAKzG,qBAAuB2G,GAAgC,OAAfF,EAAK1F,YAAwC,IAAf0F,EAAK1F,MAClF0F,EAAK1F,MAAMnB,QAAQ,wBADrB,CAKA,GAAI6G,EAAK1G,YAAc4G,EACnB,GAAIrI,SAASsI,kBACT,GAAIH,EAAK1B,SAASS,GAAGgB,GAEjB,YADAC,EAAKzF,SAASsB,KAAKmE,EAAK1B,SAAU0B,EAAM7E,EAAGC,QAS/C,GALAR,EAASoF,EAAK1B,SAAS1D,SACvBqF,EAAUtL,EAAE+C,QAGZkD,EAAOE,KAAOmF,EAAQzE,YAClBZ,EAAOE,KAAOrC,EAAEgE,QAChB7B,EAAOI,MAAQiF,EAAQtE,aACnBf,EAAOI,MAAQvC,EAAE+D,QACjB5B,EAAOW,OAASX,EAAOE,IAAMkF,EAAK1B,SAASvD,cACvCH,EAAOW,QAAU9C,EAAEgE,QACnB7B,EAAOc,MAAQd,EAAOI,KAAOgF,EAAK1B,SAASrD,aACvCL,EAAOc,OAASjD,EAAE+D,SAGlB,YADAwD,EAAKzF,SAASsB,KAAKmE,EAAK1B,SAAU0B,EAAM7E,EAAGC,GAS/D2E,GAAUG,GACVF,EAAK1B,SAASwC,IAAI,qBAAsB,WACpCnM,EAAEoL,GAAQgB,aAAa5F,EAAGA,EAAGC,EAAGA,EAAG4D,OAAQA,MAItC,OAATgB,QAAiC,IAATA,GAAuC,OAAfA,EAAK1F,YAAyC,IAAf0F,EAAK1F,OACpF0F,EAAK1F,MAAMnB,QAAQ,sBAExB,KAGP6H,QAAS,SAAUvI,EAAGyC,GACbA,EAAI+F,SACLxI,EAAE6E,iBAGN7E,EAAEyI,mBAENC,IAAK,SAAU1I,GAEX,IAAIyC,KAGAxC,IACAwC,EAAMxC,EAAgBgF,KAAK,yBAGL,IAAfxC,EAAIa,SACXb,EAAIa,OAAS,GAEjB,IAAIqF,EAAe,EACfC,EAA2B,SAAUtB,GACT,KAAxBA,EAAOuB,MAAMvF,OACbqF,EAAerB,EAAOuB,MAAMvF,OAEA,OAAxBgE,EAAOzE,mBAAwD,IAAxByE,EAAOzE,aAC9C+F,EAAyBtB,EAAOzE,cAEF,OAAzByE,EAAOwB,oBAA0D,IAAzBxB,EAAOwB,eACpDF,EAAyBtB,EAAOwB,gBAQ5C,GAJAF,EAAyB5I,EAAEsH,UAIvB7E,EAAIZ,OAAS0C,SAASoE,EAAa,IAAMpE,SAAS9B,EAAIZ,MAAME,IAAI,UAAU,KAA9E,CAGA,OAAQ/B,EAAE+I,SACN,KAAK,EACL,KAAK,GAGD,GAFApE,EAAO4D,QAAQvI,EAAGyC,GAEdA,EAAI+F,QAAS,CACb,GAAkB,IAAdxI,EAAE+I,SAAiB/I,EAAEgJ,SAQrB,OAPAhJ,EAAE6E,iBACEpC,EAAIwG,WACJxG,EAAIwG,UAAUrL,KAAK,2BAA2BsL,YAEhC,OAAdzG,EAAIZ,YAAuC,IAAdY,EAAIZ,OACjCY,EAAIZ,MAAMnB,QAAQ,gBAGnB,GAAkB,KAAdV,EAAE+I,SAAiF,aAA/DtG,EAAIwG,UAAUrL,KAAK,2BAA2B8G,KAAK,QAG9E,YADA1E,EAAE6E,sBAGH,GAAkB,IAAd7E,EAAE+I,SAAiB/I,EAAEgJ,SAI5B,YAHkB,OAAdvG,EAAIZ,YAAuC,IAAdY,EAAIZ,OACjCY,EAAIZ,MAAMnB,QAAQ,gBAI1B,MAGJ,KAAK,GAED,GADAiE,EAAO4D,QAAQvI,EAAGyC,IACdA,EAAI+F,QAmBJ,YAHkB,OAAd/F,EAAIZ,YAAuC,IAAdY,EAAIZ,OACjCY,EAAIZ,MAAMnB,QAAQ,gBAhBtB,GAAkB,IAAdV,EAAE+I,QAQF,OAPA/I,EAAE6E,iBACEpC,EAAIwG,WACJxG,EAAIwG,UAAUrL,KAAK,2BAA2BsL,YAEhC,OAAdzG,EAAIZ,YAAuC,IAAdY,EAAIZ,OACjCY,EAAIZ,MAAMnB,QAAQ,gBAGnB,GAAkB,KAAdV,EAAE+I,SAAiF,aAA/DtG,EAAIwG,UAAUrL,KAAK,2BAA2B8G,KAAK,QAG9E,YADA1E,EAAE6E,iBASV,MAEJ,KAAK,GAED,GADAF,EAAO4D,QAAQvI,EAAGyC,GACdA,EAAI+F,UAAY/F,EAAIwG,YAAcxG,EAAIwG,UAAUpL,OAChD,MAGJ,IAAK4E,EAAIwG,UAAUzE,SAASY,SAAS,qBAAsB,CACvD,IAAI+D,EAAU1G,EAAIwG,UAAUzE,SAASA,SAGrC,OAFA/B,EAAIwG,UAAUvI,QAAQ,yBACtB+B,EAAIwG,UAAYE,GAGpB,MAEJ,KAAK,GAED,GADAxE,EAAO4D,QAAQvI,EAAGyC,GACdA,EAAI+F,UAAY/F,EAAIwG,YAAcxG,EAAIwG,UAAUpL,OAChD,MAGJ,IAAIuL,EAAW3G,EAAIwG,UAAUhE,KAAK,mBAClC,GAAImE,EAASvH,OAASY,EAAIwG,UAAU7D,SAAS,wBAIzC,OAHA3C,EAAIwG,UAAY,KAChBG,EAASH,UAAY,UACrBG,EAASvH,MAAMnB,QAAQ,eAG3B,MAEJ,KAAK,GACL,KAAK,GACD,OAAI+B,EAAIwG,WAAaxG,EAAIwG,UAAUrL,KAAK,2BAA2BC,YAC/D,IAEC4E,EAAIwG,WAAaxG,EAAIwG,UAAUzE,UAAY/B,EAAIZ,OAC3C9D,SAAS,SAAW0E,EAAIzB,WAAW7C,SAAW,MAAQsE,EAAIzB,WAAWG,cAAgB,KAAmB,KAAdnB,EAAE+I,QAAiB,QAAU,UACvHrI,QAAQ,0BACbV,EAAE6E,kBAKV,KAAK,GAED,GADAF,EAAO4D,QAAQvI,EAAGyC,GACdA,EAAI+F,QAAS,CACb,GAAI/F,EAAIwG,YAAcxG,EAAIwG,UAAU3C,GAAG,oBAEnC,YADAtG,EAAE6E,iBAGN,MAKJ,iBAH6B,IAAlBpC,EAAIwG,WAA+C,OAAlBxG,EAAIwG,WAC5CxG,EAAIwG,UAAUvI,QAAQ,YAI9B,KAAK,GACL,KAAK,GACL,KAAK,GAGD,YADAiE,EAAO4D,QAAQvI,EAAGyC,GAGtB,KAAK,GAKD,OAJAkC,EAAO4D,QAAQvI,EAAGyC,QACA,OAAdA,EAAIZ,YAAuC,IAAdY,EAAIZ,OACjCY,EAAIZ,MAAMnB,QAAQ,qBAI1B,QACI,IAAIrE,EAAKgN,OAAOC,aAAatJ,EAAE+I,SAAUpM,cACzC,GAAI8F,EAAI8G,YAAc9G,EAAI8G,WAAWlN,GAGjC,YADAoG,EAAI8G,WAAWlN,GAAGmB,MAAMkD,QAAQ+B,EAAI8G,WAAWlN,GAAGwF,MAAQ,oBAAsB,WAO5F7B,EAAEyI,uBAC2B,IAAlBhG,EAAIwG,WAA+C,OAAlBxG,EAAIwG,WAC5CxG,EAAIwG,UAAUvI,QAAQV,KAI9BwJ,SAAU,SAAUxJ,GAChBA,EAAEyI,kBACF,IAAIhG,EAAMvG,EAAEuB,MAAMwH,KAAK,mBACnBsC,EAAOrL,EAAEuB,MAAMwH,KAAK,uBAGxB,GAAIxC,EAAIwG,UAAW,CACf,IAAIQ,EAAKhH,EAAIwG,WACbxG,EAAMA,EAAIwG,UAAUzE,SAASS,KAAK,oBAC9BgE,UAAYQ,EAQpB,IALA,IAAItM,EAAYsF,EAAIZ,MAAM9D,WACtB2L,EAASjH,EAAIwG,WAAcxG,EAAIwG,UAAUU,OAAO9L,OAA4B4E,EAAIwG,UAAUU,OAAjCxM,EAAUyM,OACnEC,EAASH,EAGNA,EAAMtE,SAASmC,EAAKvG,WAAW7C,WAAauL,EAAMtE,SAASmC,EAAKvG,WAAWG,gBAAkBuI,EAAMpD,GAAG,YAMzG,IAJIoD,EADAA,EAAMC,OAAO9L,OACL6L,EAAMC,OAENxM,EAAUyM,QAEZtD,GAAGuD,GAET,OAKJpH,EAAIwG,WACJtE,EAAOmF,eAAe1G,KAAKX,EAAIwG,UAAUhL,IAAI,GAAI+B,GAIrD2E,EAAOoF,eAAe3G,KAAKsG,EAAMzL,IAAI,GAAI+B,GAGzC,IAAIgK,EAASN,EAAM9L,KAAK,2BACpBoM,EAAOnM,QACPmM,EAAOC,SAIfC,SAAU,SAAUlK,GAChBA,EAAEyI,kBACF,IAAIhG,EAAMvG,EAAEuB,MAAMwH,KAAK,mBACnBsC,EAAOrL,EAAEuB,MAAMwH,KAAK,uBAGxB,GAAIxC,EAAIwG,UAAW,CACf,IAAIQ,EAAKhH,EAAIwG,WACbxG,EAAMA,EAAIwG,UAAUzE,SAASS,KAAK,oBAC9BgE,UAAYQ,EAQpB,IALA,IAAItM,EAAYsF,EAAIZ,MAAM9D,WACtBoM,EAAS1H,EAAIwG,WAAcxG,EAAIwG,UAAUmB,OAAOvM,OAA6B4E,EAAIwG,UAAUmB,OAAlCjN,EAAUa,QACnE6L,EAASM,EAGNA,EAAM/E,SAASmC,EAAKvG,WAAW7C,WAAagM,EAAM/E,SAASmC,EAAKvG,WAAWG,gBAAkBgJ,EAAM7D,GAAG,YAMzG,IAJI6D,EADAA,EAAMC,OAAOvM,OACLsM,EAAMC,OAENjN,EAAUa,SAEZsI,GAAGuD,GAET,OAKJpH,EAAIwG,WACJtE,EAAOmF,eAAe1G,KAAKX,EAAIwG,UAAUhL,IAAI,GAAI+B,GAIrD2E,EAAOoF,eAAe3G,KAAK+G,EAAMlM,IAAI,GAAI+B,GAGzC,IAAIgK,EAASG,EAAMvM,KAAK,2BACpBoM,EAAOnM,QACPmM,EAAOC,SAIfI,WAAY,WACR,IAAIrF,EAAQ9I,EAAEuB,MAAMqJ,QAAQ,sBACxB7B,EAAOD,EAAMC,OACbxC,EAAMwC,EAAKqD,YACXf,EAAOtC,EAAKqF,gBAEhB/C,EAAK0B,UAAYxG,EAAIwG,UAAYjE,EACjCuC,EAAKiB,QAAU/F,EAAI+F,SAAU,GAGjC+B,UAAW,WACP,IACItF,EADQ/I,EAAEuB,MAAMqJ,QAAQ,sBACX7B,OACbxC,EAAMwC,EAAKqD,YACJrD,EAAKqF,gBAEX9B,QAAU/F,EAAI+F,SAAU,GAGjCgC,eAAgB,WACDtO,EAAEuB,MAAMwH,OAAOqF,gBACrBG,UAAW,GAGpBC,eAAgB,SAAU1K,GACtB,IAAIuH,EAAOrL,EAAEuB,MAAMwH,OAAOqF,gBACtB/C,EAAKI,QAAUJ,EAAKI,OAAOrB,GAAGtG,EAAE4G,iBAChCW,EAAKkD,UAAW,IAIxBV,eAAgB,SAAU/J,GACtB,IAAIgF,EAAQ9I,EAAEuB,MACVwH,EAAOD,EAAMC,OACbxC,EAAMwC,EAAKqD,YACXf,EAAOtC,EAAKqF,gBAEhB/C,EAAKkD,UAAW,EAGZzK,GAAKuH,EAAKI,QAAUJ,EAAKI,OAAOrB,GAAGtG,EAAE4G,iBACrC5G,EAAE6E,iBACF7E,EAAE8E,6BAILrC,EAAIZ,MAAQY,EAAM8E,GAAM1F,MACpB9D,SAAS,IAAMwJ,EAAKvG,WAAWC,OAAOP,QAAQ,oBAC9C3C,SAAS,UAAU2C,QAAQ,oBAE5BsE,EAAMI,SAASmC,EAAKvG,WAAW7C,WAAa6G,EAAMI,SAASmC,EAAKvG,WAAWG,eAC3EsB,EAAIwG,UAAY,KAKpBjE,EAAMtE,QAAQ,sBAGlBoJ,eAAgB,SAAU9J,GACtB,IAAIgF,EAAQ9I,EAAEuB,MACVwH,EAAOD,EAAMC,OACbxC,EAAMwC,EAAKqD,YACXf,EAAOtC,EAAKqF,gBAEhB,GAAI/C,IAAS9E,GAAO8E,EAAKI,QAAUJ,EAAKI,OAAOrB,GAAGtG,EAAE4G,eAOhD,YAN8B,IAAnBW,EAAK0B,WAAgD,OAAnB1B,EAAK0B,WAC9C1B,EAAK0B,UAAUvI,QAAQ,oBAE3BV,EAAE6E,iBACF7E,EAAE8E,gCACFyC,EAAK0B,UAAYxG,EAAIwG,UAAYxG,EAAIjF,OAItCiF,GAAOA,EAAIZ,OAASY,EAAIZ,MAAMuD,SAAS,yBAI1CJ,EAAMtE,QAAQ,qBAGlBiK,UAAW,SAAU3K,GACjB,IAKI5B,EALA4G,EAAQ9I,EAAEuB,MACVwH,EAAOD,EAAMC,OACbxC,EAAMwC,EAAKqD,YACXf,EAAOtC,EAAKqF,gBACZ5B,EAAMzD,EAAK2F,eAIf,MAAKnI,EAAIvF,MAAMwL,IAAQ1D,EAAMsB,GAAG,IAAMiB,EAAKvG,WAAW7C,SAAW,+BAAiCoJ,EAAKvG,WAAWG,gBAAmB6D,EAAMsB,GAAG,2BAAuD,IAA3BiB,EAAKxG,mBAA/K,CAOA,GAHAf,EAAE6E,iBACF7E,EAAE8E,2BAEE5I,EAAEgK,WAAWzD,EAAIoI,UAAUnC,KAASoC,OAAOC,UAAU9E,eAAe7C,KAAKX,EAAIoI,UAAWnC,GAExFtK,EAAWqE,EAAIoI,UAAUnC,OACtB,CAAA,IAAIxM,EAAEgK,WAAWqB,EAAKnJ,UAKzB,OAHAA,EAAWmJ,EAAKnJ,UAO+B,IAA/CA,EAASgF,KAAKmE,EAAK1B,SAAU6C,EAAKnB,EAAMvH,GACxCuH,EAAK1F,MAAMnB,QAAQ,oBACZ6G,EAAK1F,MAAM2C,SAAS3G,QAC3BiI,EAAGkF,OAAO5H,KAAKmE,EAAK1B,SAAU0B,KAItC0D,WAAY,SAAUjL,GAClBA,EAAE8E,4BAGNoG,SAAU,SAAUlL,EAAGiF,GACnB,IAAIsC,EAAOrL,EAAEuB,MAAMwH,KAAK,mBACxBa,EAAGpC,KAAKN,KAAKmE,EAAK1B,SAAU0B,EAAMtC,GAAQA,EAAKkG,QAGnDC,UAAW,SAAUpL,GACjBA,EAAEyI,kBACF,IAAIzD,EAAQ9I,EAAEuB,MACVwH,EAAOD,EAAMC,OACbxC,EAAMwC,EAAKqD,YACXf,EAAOtC,EAAKqF,gBAEZtF,EAAMI,SAASmC,EAAKvG,WAAW7C,WAAa6G,EAAMI,SAASmC,EAAKvG,WAAWG,iBAI/E6D,EACKqG,UAAU9D,EAAKvG,WAAWC,MAAOsG,EAAKvG,WAAWE,SAASoK,KAAK,MAE/D9G,SAAS5G,KAAK,sBAAsB2N,IAAIvG,GACxCwG,YAAYjE,EAAKvG,WAAWE,SAC5BuK,OAAO,IAAMlE,EAAKvG,WAAWC,OAC7BP,QAAQ,oBAGb+B,EAAIwG,UAAY1B,EAAK0B,UAAYjE,EAG9BvC,GAAOA,EAAIjF,OAASiF,EAAIjF,MAAM4H,SAAS,yBACtC3C,EAAIjF,MAAM6N,SAAS9D,EAAKvG,WAAWC,OAInCwB,EAAIjF,OACJ+J,EAAKlE,gBAAgBD,KAAKX,EAAIjF,MAAOiF,EAAIZ,SAIjD6J,SAAU,SAAU1L,GAChBA,EAAEyI,kBACF,IAAIzD,EAAQ9I,EAAEuB,MACVwH,EAAOD,EAAMC,OACbxC,EAAMwC,EAAKqD,YACXf,EAAOtC,EAAKqF,gBAEZ7H,EAAI9B,UACJqE,EAAMwG,YAAYjE,EAAKvG,WAAWE,SAEtC8D,EAAMwG,YAAYjE,EAAKvG,WAAWC,OAClCwB,EAAIwG,UAAY,OAIxBnD,GACIrC,KAAM,SAAUhB,EAAKC,EAAGC,GACpB,IAAIkD,EAAW3J,EAAEuB,MACbsE,KASJ,GANA7F,EAAE,uBAAuBwE,QAAQ,aAGjC+B,EAAIoD,SAAWA,GAG6B,IAAxCpD,EAAI9C,OAAO8D,KAAKL,KAAKyC,EAAUpD,GAAnC,CAYA,GANAqD,EAAGkF,OAAO5H,KAAKyC,EAAUpD,GAGzBA,EAAIX,SAASsB,KAAKyC,EAAUpD,EAAKC,EAAGC,GAGhCF,EAAIa,OAAQ,CACZ,IAAIqI,EAAmBlJ,EAAIa,OAED,mBAAfb,EAAIa,SACXqI,EAAmBlJ,EAAIa,OAAOF,KAAKyC,EAAUpD,IAEjDV,EAAIuB,OAASW,EAAO4B,GAAY8F,EAIpC7F,EAAG8F,MAAMxI,KAAKX,EAAIZ,MAAOY,EAAKV,EAAIuB,QAGlCb,EAAIZ,MAAMjE,KAAK,MAAMmE,IAAI,SAAUA,EAAIuB,OAAS,GAGhDb,EAAIZ,MAAME,IAAIA,GAAKU,EAAIc,UAAUE,MAAMhB,EAAIc,UAAUC,SAAU,WAC3DqC,EAASnF,QAAQ,uBAEjBoF,EAAGlC,UAAUnB,GACbA,EAAI9C,OAAOiE,cAGfiC,EACKZ,KAAK,cAAexC,GACpB4I,SAAS,uBAGdnP,EAAEkD,UAAU8H,IAAI,uBAAuBH,GAAG,sBAAuBpC,EAAO+D,KAEpEjG,EAAI9B,UAEJzE,EAAEkD,UAAU2H,GAAG,gCAAiC,SAAU/G,GAGtD,IAAI6L,EAAMhG,EAAS1D,SACnB0J,EAAI5I,MAAQ4I,EAAItJ,KAAOsD,EAASrD,aAChCqJ,EAAI/I,OAAS+I,EAAIxJ,IAAMwD,EAASvD,eAE5BG,EAAIkF,QAAWlF,EAAIgI,UAAezK,EAAE+D,OAAS8H,EAAItJ,MAAQvC,EAAE+D,OAAS8H,EAAI5I,OAAYjD,EAAEgE,OAAS6H,EAAIxJ,KAAOrC,EAAEgE,OAAS6H,EAAI/I,QAEzHmE,WAAW,WACFxE,EAAIgI,UAA0B,OAAdhI,EAAIZ,YAAuC,IAAdY,EAAIZ,OAClDY,EAAIZ,MAAMnB,QAAQ,qBAEvB,WAxDXT,EAAkB,MA6D1ByD,KAAM,SAAUjB,EAAK0I,GACjB,IAAItF,EAAW3J,EAAEuB,MAMjB,GALKgF,IACDA,EAAMoD,EAASZ,KAAK,oBAInBkG,IAAS1I,EAAI9C,SAAkD,IAAxC8C,EAAI9C,OAAO+D,KAAKN,KAAKyC,EAAUpD,GAA3D,CASA,GAJAoD,EACKY,WAAW,eACX+E,YAAY,uBAEb/I,EAAIkF,OAAQ,CAEZV,WAAW,SAAWU,GAClB,OAAO,WACHA,EAAO7H,UAFJ,CAIR2C,EAAIkF,QAAS,IAEhB,WACWlF,EAAIkF,OACb,MAAO3H,GACLyC,EAAIkF,OAAS,MAKrB1H,EAAkB,KAElBwC,EAAIZ,MAAMjE,KAAK,IAAM6E,EAAIzB,WAAWC,OAAOP,QAAQ,oBACnD+B,EAAIwG,UAAY,KAEhBxG,EAAIZ,MAAMjE,KAAK,IAAM6E,EAAIzB,WAAWE,SAASsK,YAAY/I,EAAIzB,WAAWE,SAGxEhF,EAAEkD,UAAU8H,IAAI,wBAAwBA,IAAI,uBAExCzE,EAAIZ,OACJY,EAAIZ,MAAMY,EAAIc,UAAUG,MAAMjB,EAAIc,UAAUC,SAAU,WAE9Cf,EAAI4C,QACJ5C,EAAIZ,MAAM/B,SACV5D,EAAEmB,KAAKoF,EAAK,SAAUiG,GAClB,OAAQA,GACJ,IAAK,KACL,IAAK,WACL,IAAK,QACL,IAAK,UACD,OAAO,EAEX,QACIjG,EAAIiG,QAAOpK,EACX,WACWmE,EAAIiG,GACb,MAAO1I,IAET,OAAO,MAKvBiH,WAAW,WACPpB,EAASnF,QAAQ,uBAClB,QAIfqF,OAAQ,SAAUtD,EAAK8E,GAsBnB,SAASuE,EAAevO,GACpB,IAAIwO,EAAQ7P,EAAE,iBACd,GAAIqB,EAAKyO,WACDzO,EAAK0O,kBACLF,EAAMG,OAAO9M,SAAS+M,eAAe5O,EAAK0O,mBAE9C/P,EAAE,iBACGmP,SAAS,0BACTvN,KAAKP,EAAKyO,YACVvL,SAASsL,GACVxO,EAAK6O,iBACLL,EAAMG,OAAO9M,SAAS+M,eAAe5O,EAAK6O,uBAG9C,GAAI7O,EAAK8O,WAAY,CAEjB,QAA8B,IAAnB9O,EAAK+O,UACZ,MAAM,IAAI1G,MAAM,8FAEpBmG,EAAMlN,KAAKtB,EAAKP,WAEhB+O,EAAMjO,KAAKP,EAAKP,MAGxB,OAAO+O,OA7CS,IAATxE,IACPA,EAAO9E,GAIXA,EAAIZ,MAAQ3F,EAAE,uCAAuCmP,SAAS5I,EAAI8J,WAAa,IAAItH,MAC/EqD,YAAe7F,EACf6H,gBAAmB/C,IAGvBrL,EAAEmB,MAAM,YAAa,WAAY,UAAW,SAAUZ,EAAGJ,GACrDoG,EAAIpG,MACCkL,EAAKlL,KACNkL,EAAKlL,SAIRkL,EAAKgC,aACNhC,EAAKgC,eA+BTrN,EAAEmB,KAAKoF,EAAIvF,MAAO,SAAUwL,EAAKnL,GAC7B,IAAI2G,EAAKhI,EAAE,uCAAuCmP,SAAS9N,EAAKgP,WAAa,IACzEC,EAAS,KACTxC,EAAS,KAqBb,GAjBA9F,EAAG6C,GAAG,QAAS7K,EAAEyH,MAKG,iBAATpG,GAAmC,iBAAdA,EAAKiB,OACjCjB,GAAQiB,KAAM,iBAGlBjB,EAAKC,MAAQ0G,EAAGe,MACZqD,YAAe7F,EACf6H,gBAAmB/C,EACnBqD,eAAkBlC,SAKQ,IAAnBnL,EAAK+O,UAEZ,IAAK,IAAWG,EADZC,EAAMvQ,EAAeoB,EAAK+O,WACrB7P,EAAI,EAAOgQ,EAAKC,EAAIjQ,GAAIA,IAC7B,IAAK8K,EAAKgC,WAAWkD,GAAK,CACtBlF,EAAKgC,WAAWkD,GAAMlP,EACtB,IAAIoP,EAAUpP,EAAKP,KAAK4P,MAAM,IAAIC,OAAO,UAAYJ,EAAK,SAAU,MAChEE,IACApP,EAAK0O,iBAAmBU,EAAQ,GAChCpP,EAAKyO,WAAaW,EAAQ,GAC1BpP,EAAK6O,gBAAkBO,EAAQ,IAEnC,MAKZ,GAAIpP,EAAKiB,MAAQ8B,EAAM/C,EAAKiB,MAExB8B,EAAM/C,EAAKiB,MAAM4E,KAAKc,EAAI3G,EAAMkF,EAAK8E,GAErCrL,EAAEmB,MAAMoF,EAAK8E,GAAO,SAAU9K,EAAGJ,GAC7BA,EAAEyQ,SAASpE,GAAOnL,GAGdrB,EAAEgK,WAAW3I,EAAKa,gBAA0C,IAArB/B,EAAEwO,UAAUnC,SAA4C,IAAbjG,EAAIjE,OACtFnC,EAAEwO,UAAUnC,GAAOnL,EAAKa,gBAG7B,CAsBH,OApBkB,iBAAdb,EAAKiB,KACL0F,EAAGmH,SAAS,0BAA4B9D,EAAKvG,WAAWG,eACnC,SAAd5D,EAAKiB,KACZ0F,EAAGmH,SAAS,qBAAuB9D,EAAKvG,WAAWG,eAC9B,QAAd5D,EAAKiB,OAELjB,EAAKiB,MACZgO,EAAStQ,EAAE,mBAAmBuE,SAASyD,GACvC4H,EAAevO,GAAMkD,SAAS+L,GAE9BtI,EAAGmH,SAAS,sBACZ5I,EAAIsK,UAAW,EACf7Q,EAAEmB,MAAMoF,EAAK8E,GAAO,SAAU9K,EAAGJ,GAC7BA,EAAEyQ,SAASpE,GAAOnL,EAClBlB,EAAE2Q,OAAOtE,GAAOnL,KAEbA,EAAKL,QACZK,EAAKiB,KAAO,QAGRjB,EAAKiB,MACT,IAAK,eACD,MAEJ,IAAK,OACDwL,EAAS9N,EAAE,2CACNgC,KAAK,OAAQ,sBAAwBwK,GACrCtM,IAAImB,EAAKoB,OAAS,IAClB8B,SAAS+L,GACd,MAEJ,IAAK,WACDxC,EAAS9N,EAAE,iCACNgC,KAAK,OAAQ,sBAAwBwK,GACrCtM,IAAImB,EAAKoB,OAAS,IAClB8B,SAAS+L,GAEVjP,EAAKyF,QACLgH,EAAOhH,OAAOzF,EAAKyF,QAEvB,MAEJ,IAAK,WACDgH,EAAS9N,EAAE,+CACNgC,KAAK,OAAQ,sBAAwBwK,GACrCtM,IAAImB,EAAKoB,OAAS,IAClB+F,KAAK,YAAanH,EAAKkB,UACvBwO,UAAUT,GACf,MAEJ,IAAK,QACDxC,EAAS9N,EAAE,4CACNgC,KAAK,OAAQ,sBAAwBX,EAAKmB,OAC1CtC,IAAImB,EAAKoB,OAAS,IAClB+F,KAAK,YAAanH,EAAKkB,UACvBwO,UAAUT,GACf,MAEJ,IAAK,SACDxC,EAAS9N,EAAE,6BACNgC,KAAK,OAAQ,sBAAwBwK,GACrCjI,SAAS+L,GACVjP,EAAKqB,UACL1C,EAAEmB,KAAKE,EAAKqB,QAAS,SAAUD,EAAOb,GAClC5B,EAAE,qBAAqBE,IAAIuC,GAAOb,KAAKA,GAAM2C,SAASuJ,KAE1DA,EAAO5N,IAAImB,EAAKkB,WAEpB,MAEJ,IAAK,MACDqN,EAAevO,GAAMkD,SAASyD,GAC9B3G,EAAKkD,SAAWlD,EAAKC,MACrB0G,EAAGe,KAAK,cAAe1H,GAAM8N,SAAS,wBACtC9N,EAAKa,SAAW,KAKZ,mBAAsBb,EAAKL,MAAMgQ,KAEjCpH,EAAGqH,gBAAgB5P,EAAMgK,EAAMhK,EAAKL,OAGpC4I,EAAGC,OAAOxI,EAAMgK,GAEpB,MAEJ,IAAK,OACDrL,EAAEqB,EAAKsB,MAAM4B,SAASyD,GACtB,MAEJ,QACIhI,EAAEmB,MAAMoF,EAAK8E,GAAO,SAAU9K,EAAGJ,GAC7BA,EAAEyQ,SAASpE,GAAOnL,GAGdrB,EAAEgK,WAAW3I,EAAKa,gBAA0C,IAArB/B,EAAEwO,UAAUnC,SAA4C,IAAbjG,EAAIjE,OACtFnC,EAAEwO,UAAUnC,GAAOnL,EAAKa,YAGhC0N,EAAevO,GAAMkD,SAASyD,GAKlC3G,EAAKiB,MAAsB,QAAdjB,EAAKiB,MAAgC,SAAdjB,EAAKiB,MAAiC,iBAAdjB,EAAKiB,OACjEwL,EACKjD,GAAG,QAASpC,EAAO0F,YACnBtD,GAAG,OAAQpC,EAAO4F,WAEnBhN,EAAKoC,QACLqK,EAAOjD,GAAGxJ,EAAKoC,OAAQ8C,IAK3BlF,EAAKgB,OACDrC,EAAEgK,WAAW3I,EAAKgB,MAClBhB,EAAK6P,MAAQ7P,EAAKgB,KAAK6E,KAAK3F,KAAMA,KAAMyG,EAAIwE,EAAKnL,GAEvB,iBAAfA,EAAS,MAAgD,QAA9BA,EAAKgB,KAAK8O,UAAU,EAAG,GAEzD9P,EAAK6P,MAAQ7F,EAAKvG,WAAWzC,KAAO,IAAMgJ,EAAKvG,WAAWzC,KAAO,WAAahB,EAAKgB,KAEnFhB,EAAK6P,MAAQ7F,EAAKvG,WAAWzC,KAAO,IAAMgJ,EAAKvG,WAAWzC,KAAO,IAAMhB,EAAKgB,KAGpF2F,EAAGmH,SAAS9N,EAAK6P,QAKzB7P,EAAKyM,OAASA,EACdzM,EAAKiP,OAASA,EAGdtI,EAAGzD,SAASgC,EAAIZ,QAGXY,EAAIsK,UAAY7Q,EAAE6C,QAAQI,kBAI3B+E,EAAG6C,GAAG,gCAAiCpC,EAAOC,cAIjDnC,EAAIjF,OACLiF,EAAIZ,MAAME,IAAI,UAAW,QAAQsJ,SAAS,qBAE9C5I,EAAIZ,MAAMpB,SAASgC,EAAIhC,UAAYrB,SAASkO,OAEhDC,OAAQ,SAAU1L,EAAO2L,GACrB,IAAIC,EAMJ5L,EAAME,KAAKD,SAAU,WAAY4L,QAAS,UAE1C7L,EAAMoD,KAAK,SACNwI,EAAU5L,EAAM5D,IAAI,IAAI0P,sBACrBtJ,KAAKuJ,KAAKH,EAAQE,wBAAwBxK,OAC1CtB,EAAMW,aAAe,GAE7BX,EAAME,KACFD,SAAU,SACV+L,SAAU,MACVC,SAAU,aAGdjM,EAAMjE,KAAK,aAAaP,KAAK,WACzByI,EAAGyH,OAAOrR,EAAEuB,OAAO,KAIlB+P,GACD3L,EAAMjE,KAAK,MAAMmQ,UAAUhM,KACvBD,SAAU,GACV4L,QAAS,GACTG,SAAU,GACVC,SAAU,KACXtL,WAAW,WACV,OAAOtG,EAAEuB,MAAMwH,KAAK,YAIhC+F,OAAQ,SAAUvI,EAAK8E,GACnB,IAAI1B,EAAWpI,UACK,IAAT8J,IACPA,EAAO9E,EACPqD,EAAGyH,OAAO9K,EAAIZ,QAGlBY,EAAIZ,MAAM9D,WAAWV,KAAK,WACtB,IAII6D,EAJA8M,EAAQ9R,EAAEuB,MACViL,EAAMsF,EAAM/I,KAAK,kBACjB1H,EAAOkF,EAAIvF,MAAMwL,GACjBvK,EAAYjC,EAAEgK,WAAW3I,EAAKY,WAAaZ,EAAKY,SAASiF,KAAKyC,EAAU6C,EAAKnB,KAA4B,IAAlBhK,EAAKY,SAoBhG,GAjBI+C,EADAhF,EAAEgK,WAAW3I,EAAK2D,SACR3D,EAAK2D,QAAQkC,KAAKyC,EAAU6C,EAAKnB,QACZ,IAAjBhK,EAAK2D,UACQ,IAAjB3D,EAAK2D,QAInB8M,EAAM9M,EAAU,OAAS,UAGzB8M,EAAM7P,EAAW,WAAa,eAAeoJ,EAAKvG,WAAW7C,UAEzDjC,EAAEgK,WAAW3I,EAAKgB,QAClByP,EAAMxC,YAAYjO,EAAK6P,OACvB7P,EAAK6P,MAAQ7P,EAAKgB,KAAK6E,KAAK3F,KAAMoI,EAAUmI,EAAOtF,EAAKnL,GACxDyQ,EAAM3C,SAAS9N,EAAK6P,QAGpB7P,EAAKiB,KAKL,OAHAwP,EAAMpQ,KAAK,2BAA2B8G,KAAK,WAAYvG,GAG/CZ,EAAKiB,MACT,IAAK,OACL,IAAK,WACDjB,EAAKyM,OAAO5N,IAAImB,EAAKoB,OAAS,IAC9B,MAEJ,IAAK,WACL,IAAK,QACDpB,EAAKyM,OAAO5N,IAAImB,EAAKoB,OAAS,IAAI+F,KAAK,YAAanH,EAAKkB,UACzD,MAEJ,IAAK,SACDlB,EAAKyM,OAAO5N,KAAuB,IAAlBmB,EAAKkB,SAAiB,IAAMlB,EAAKkB,WAAa,IAKvElB,EAAKsE,OAELiE,EAAGkF,OAAO5H,KAAKyC,EAAUtI,EAAMgK,MAI3CqE,MAAO,SAAUnJ,EAAKa,GAGlB,IAAIqE,EAASlF,EAAIkF,OAASzL,EAAE,uCACvB6F,KACGiB,OAAQ7C,EAAK6C,SACbG,MAAOhD,EAAKgD,QACZuK,QAAS,QACT5L,SAAU,QACVmM,UAAW3K,EACXjB,IAAK,EACLE,KAAM,EACN2L,QAAS,EACTzC,OAAQ,mBACR0C,mBAAoB,SAEvBlJ,KAAK,kBAAmBxC,GACxB2L,aAAa3Q,MACbsJ,GAAG,cAAepC,EAAOC,YACzBmC,GAAG,YAAapC,EAAO0C,YAU5B,YAP4C,IAAjCjI,SAASkO,KAAKzE,MAAMiF,UAC3BnG,EAAO5F,KACHD,SAAY,WACZkB,OAAU9G,EAAEkD,UAAU4D,WAIvB2E,GAEXwF,gBAAiB,SAAU1K,EAAK8E,EAAM8G,GAclC,SAASC,EAAa7L,EAAK8E,EAAMgH,QAEJ,IAAdA,GACPA,GACI7I,OACI1I,KAAM,6BACNuB,KAAM,6CAGVU,OAAOwG,UACNA,QAAQC,OAASD,QAAQE,KAAKvC,KAAKqC,QAAS,yFAErB,iBAAd8I,IACdA,GAAa7I,OAAU1I,KAAMuR,KAEjCC,EAAqB/L,EAAK8E,EAAMgH,GAGpC,SAASC,EAAqB/L,EAAK8E,EAAMrK,QACX,IAAfqK,EAAK1F,OAA0B0F,EAAK1F,MAAMyE,GAAG,cAGxD7D,EAAIjF,MAAMgO,YAAYjE,EAAKvG,WAAWW,kBACtCc,EAAIvF,MAAQA,EACZ4I,EAAGC,OAAOtD,EAAK8E,GAAM,GACrBzB,EAAGkF,OAAOvI,EAAK8E,GACfA,EAAKlE,gBAAgBD,KAAKX,EAAIjF,MAAOiF,EAAIZ,QAtC7CY,EAAIjF,MAAM6N,SAAS9D,EAAKvG,WAAWW,kBA2CnC0M,EAAQnB,KAzCR,SAA0BzK,EAAK8E,EAAMrK,QAGZ,IAAVA,GAEPoR,OAAahQ,GAEjBkQ,EAAqB/L,EAAK8E,EAAMrK,IAkCNuR,KAAKhR,KAAMgF,EAAK8E,GAAO+G,EAAaG,KAAKhR,KAAMgF,EAAK8E,KAGtF3D,UAAW,SAASnB,GAChB,IAAIZ,EAAQY,EAAIZ,MACZ6M,EAAc7M,EAAMM,SACpBwM,EAAYzS,EAAE+C,QAAQ+D,SACtB4L,EAAe1S,EAAE+C,QAAQ8D,YACzB8L,EAAahN,EAAMmB,SACpB6L,EAAaF,EACZ9M,EAAME,KACFiB,OAAW2L,EAAY,KACvBG,aAAc,SACdC,aAAc,OACd1M,IAAOuM,EAAe,QAEnBF,EAAYrM,IAAMuM,GAAkBF,EAAYrM,IAAMwM,EAAaD,EAAeD,IACzF9M,EAAME,KACFM,IAAO,UAsB3BnG,EAAE8S,GAAG1G,YAAc,SAAU2G,GACzB,IAAI/K,EAAKzG,KAAMyR,EAAKD,EACpB,GAAIxR,KAAKI,OAAS,EACd,QAAyB,IAAdoR,EACPxR,KAAKO,QAAQ0C,QAAQ,oBAClB,QAA2B,IAAhBuO,EAAUvM,QAA4C,IAAhBuM,EAAUtM,EAC9DlF,KAAKO,QAAQ0C,QAAQxE,EAAEkK,MAAM,eACzBrC,MAAOkL,EAAUvM,EACjBsB,MAAOiL,EAAUtM,EACjBwC,YAAa8J,EAAU1I,eAExB,GAAkB,SAAd0I,EAAsB,CAC7B,IAAIpN,EAAQpE,KAAKO,QAAQiH,KAAK,eAAiBxH,KAAKO,QAAQiH,KAAK,eAAepD,MAAQ,KACpFA,GACAA,EAAMnB,QAAQ,wBAEG,YAAduO,EACP/S,EAAEoM,YAAY,WAAY6G,QAAS1R,OAC5BvB,EAAEkT,cAAcH,IACvBA,EAAUE,QAAU1R,KACpBvB,EAAEoM,YAAY,SAAU2G,IACjBA,EACPxR,KAAK+N,YAAY,yBACTyD,GACRxR,KAAK4N,SAAS,8BAGlBnP,EAAEmB,KAAKgD,EAAO,WACN5C,KAAK+C,WAAa0D,EAAG1D,WACrB0O,EAAGjK,KAAOxH,KAEVvB,EAAEqJ,OAAO2J,EAAGjK,MAAOvE,QAAS,cAIpCiE,EAAOI,YAAY3B,KAAK8L,EAAG5H,OAAQ4H,GAGvC,OAAOzR,MAIXvB,EAAEoM,YAAc,SAAU2G,EAAWrQ,GACR,iBAAdqQ,IACPrQ,EAAUqQ,EACVA,EAAY,UAGO,iBAAZrQ,EACPA,GAAW4B,SAAU5B,QACK,IAAZA,IACdA,MAIJ,IAAIyQ,EAAInT,EAAEqJ,QAAO,KAAUhF,EAAU3B,OACjCiI,EAAY3K,EAAEkD,UACdkQ,EAAWzI,EACX0I,GAAc,EAWlB,OATKF,EAAEF,SAAYE,EAAEF,QAAQtR,QAIzByR,EAAWpT,EAAEmT,EAAEF,SAASnR,QACxBqR,EAAEF,QAAUG,EAASrR,IAAI,GACzBsR,GAAerT,EAAEmT,EAAEF,SAAS7I,GAAGlH,WAL/BiQ,EAAEF,QAAU/P,SAQR6P,GAEJ,IAAK,SAED,GAAGM,EACCzJ,EAAGkF,OAAOsE,QAEV,IAAI,IAAIE,KAAQnP,EACTA,EAAM4F,eAAeuJ,IACpB1J,EAAGkF,OAAO3K,EAAMmP,IAI5B,MAEJ,IAAK,SAED,IAAKH,EAAE7O,SACH,MAAM,IAAIoF,MAAM,yBAGpB,GAAIyJ,EAAE7O,SAASoM,MAAM,yCACjB,MAAM,IAAIhH,MAAM,4BAA8ByJ,EAAE7O,SAAW,yCAE/D,IAAK6O,EAAEhK,SAAWgK,EAAEnS,OAAShB,EAAEsJ,cAAc6J,EAAEnS,QAC3C,MAAM,IAAI0I,MAAM,sBAcpB,GAZAxI,IACAiS,EAAEI,GAAK,eAAiBrS,EACnBmS,IACDnP,EAAWiP,EAAE7O,UAAY6O,EAAEI,IAE/BpP,EAAMgP,EAAEI,IAAMJ,EAGTA,EAAE3O,UACH2O,EAAE3O,QAAU,UAGXR,EAAa,CACd,IAAIyK,EAAiC,UAArB0E,EAAEK,eAA6B,oBAAsB,sBACjEC,GAGAC,gCAAiCjL,EAAOyG,UACxCyE,+BAAgClL,EAAO+G,SACvCoE,0BAA2BnL,EAAOC,WAClCmL,yBAA0BpL,EAAOoF,eACjCiG,yBAA0BrL,EAAOmF,gBAErC6F,EAAmBhF,GAAahG,EAAOgG,UAEvC9D,EACKE,IACGkJ,+BAAgCtL,EAAOuG,SACvCgF,0BAA2BvL,EAAO6E,SAClC2G,0BAA2BxL,EAAOuF,SAClC4F,0BAA2BnL,EAAOC,WAClCmL,yBAA0BpL,EAAO6F,eACjCwF,yBAA0BrL,EAAO+F,gBAClC,sBACF3D,GAAG,sBAAuB,sBAAuBpC,EAAOsG,YACxDlE,GAAG4I,EAAoB,sBAE5BzP,GAAc,EAclB,OAVAoP,EACKvI,GAAG,cAAgBsI,EAAEI,GAAIJ,EAAE7O,SAAU6O,EAAG1K,EAAOI,aAEhDwK,GAEAD,EAASvI,GAAG,SAAWsI,EAAEI,GAAI,WACzBvT,EAAEuB,MAAM6K,YAAY,aAIpB+G,EAAE3O,SACN,IAAK,QACD4O,EACKvI,GAAG,aAAesI,EAAEI,GAAIJ,EAAE7O,SAAU6O,EAAG1K,EAAO+B,YAC9CK,GAAG,aAAesI,EAAEI,GAAIJ,EAAE7O,SAAU6O,EAAG1K,EAAOwC,YACnD,MAEJ,IAAK,OACDmI,EAASvI,GAAG,QAAUsI,EAAEI,GAAIJ,EAAE7O,SAAU6O,EAAG1K,EAAOtG,OAClD,MAChB,IAAK,aACWiR,EAASvI,GAAG,aAAesI,EAAEI,GAAIJ,EAAE7O,SAAU6O,EAAG1K,EAAOtG,OAa1DgR,EAAEhK,OACHS,EAAGC,OAAOsJ,GAEd,MAEJ,IAAK,UACD,IAAIe,EACJ,GAAIb,EAAa,CAEb,IAAIJ,EAAUE,EAAEF,QAChBjT,EAAEmB,KAAKgD,EAAO,SAAUoP,EAAIJ,GAExB,IAAKA,EACD,OAAO,EAIX,IAAKnT,EAAEiT,GAAS7I,GAAG+I,EAAE7O,UACjB,OAAO,GAGX4P,EAAelU,EAAE,sBAAsBuP,OAAO,aAC7B5N,QAAUuS,EAAanL,OAAOqF,gBAAgBzE,SAASS,GAAGpK,EAAEmT,EAAEF,SAASvR,KAAKyR,EAAE7O,YAC3F4P,EAAa1P,QAAQ,oBAAqByK,OAAO,IAGrD,IACQ9K,EAAMgP,EAAEI,IAAI5N,OACZxB,EAAMgP,EAAEI,IAAI5N,MAAM/B,gBAGfO,EAAMgP,EAAEI,IACjB,MAAOzP,GACLK,EAAMgP,EAAEI,IAAM,KAKlB,OAFAvT,EAAEmT,EAAEF,SAASjI,IAAImI,EAAEI,KAEZ,SAER,GAAKJ,EAAE7O,UAYP,GAAIJ,EAAWiP,EAAE7O,UAAW,EAC/B4P,EAAelU,EAAE,sBAAsBuP,OAAO,aAC7B5N,QAAUuS,EAAanL,OAAOqF,gBAAgBzE,SAASS,GAAG+I,EAAE7O,WACzE4P,EAAa1P,QAAQ,oBAAqByK,OAAO,IAGrD,IACQ9K,EAAMD,EAAWiP,EAAE7O,WAAWqB,OAC9BxB,EAAMD,EAAWiP,EAAE7O,WAAWqB,MAAM/B,gBAGjCO,EAAMD,EAAWiP,EAAE7O,WAC5B,MAAOR,GACLK,EAAMD,EAAWiP,EAAE7O,WAAa,KAGpCqG,EAAUK,IAAI9G,EAAWiP,EAAE7O,iBA3B3BqG,EAAUK,IAAI,qCACdhL,EAAEmB,KAAKgD,EAAO,SAAUoP,EAAIJ,GACxBnT,EAAEmT,EAAEF,SAASjI,IAAImI,EAAEI,MAGvBrP,KACAC,KACAjD,EAAU,EACV8C,GAAc,EAEdhE,EAAE,2CAA2C4D,SAmBjD,MAEJ,IAAK,UAIK5D,EAAE6C,QAAQG,cAAgBhD,EAAE6C,QAAQC,cAAqC,kBAAZJ,GAAyBA,IACxF1C,EAAE,wBAAwBmB,KAAK,WACvBI,KAAKV,IACLb,EAAEoM,aACE9H,SAAU,gBAAkB/C,KAAKV,GAAK,IACtCG,MAAOhB,EAAEoM,YAAY+H,SAAS5S,UAGvCsE,IAAI,UAAW,QAEtB,MAEJ,QACI,MAAM,IAAI6D,MAAM,sBAAwBqJ,EAAY,KAG5D,OAAOxR,MAIXvB,EAAEoM,YAAYgI,eAAiB,SAAU7N,EAAKwC,QACtB,IAATA,IACPA,MAGJ/I,EAAEmB,KAAKoF,EAAIuK,OAAQ,SAAUtE,EAAKnL,GAC9B,OAAQA,EAAKiB,MACT,IAAK,OACL,IAAK,WACDjB,EAAKoB,MAAQsG,EAAKyD,IAAQ,GAC1B,MAEJ,IAAK,WACDnL,EAAKkB,WAAWwG,EAAKyD,GACrB,MAEJ,IAAK,QACDnL,EAAKkB,UAAYwG,EAAK1H,EAAKmB,QAAU,MAAQnB,EAAKoB,MAClD,MAEJ,IAAK,SACDpB,EAAKkB,SAAWwG,EAAKyD,IAAQ,OAO7CxM,EAAEoM,YAAYiI,eAAiB,SAAU9N,EAAKwC,GAyB1C,YAxBoB,IAATA,IACPA,MAGJ/I,EAAEmB,KAAKoF,EAAIuK,OAAQ,SAAUtE,EAAKnL,GAC9B,OAAQA,EAAKiB,MACT,IAAK,OACL,IAAK,WACL,IAAK,SACDyG,EAAKyD,GAAOnL,EAAKyM,OAAO5N,MACxB,MAEJ,IAAK,WACD6I,EAAKyD,GAAOnL,EAAKyM,OAAOtF,KAAK,WAC7B,MAEJ,IAAK,QACGnH,EAAKyM,OAAOtF,KAAK,aACjBO,EAAK1H,EAAKmB,OAASnB,EAAKoB,UAMjCsG,GAuLX/I,EAAEoM,YAAY+H,SAAW,SAAUG,GAC/B,IACItT,KAIJ,OAFAD,EAAaC,EAHDhB,EAAEsU,GAGYzS,YAEnBb,GAIXhB,EAAEoM,YAAY/H,SAAWA,EACzBrE,EAAEoM,YAAYhI,MAAQA,EAEtBpE,EAAEoM,YAAY3D,OAASA,EACvBzI,EAAEoM,YAAYxC,GAAKA,EACnB5J,EAAEoM,YAAYjI,MAAQA", "file": "jquery.contextMenu.min.js", "sourcesContent": ["/**\n * jQuery contextMenu v2.6.3 - Plugin for simple contextMenu handling\n *\n * Version: v2.6.3\n *\n * Authors: <AUTHORS>\n * Web: http://swisnl.github.io/jQuery-contextMenu/\n *\n * Copyright (c) 2011-2017 SWIS BV and contributors\n *\n * Licensed under\n *   MIT License http://www.opensource.org/licenses/mit-license\n *\n * Date: 2017-10-30T19:03:13.804Z\n */\n\n// jscs:disable\n/* jshint ignore:start */\n(function (factory) {\n    if (typeof define === 'function' && define.amd) {\n        // AMD. Register as anonymous module.\n        define(['jquery'], factory);\n    } else if (typeof exports === 'object') {\n        // Node / CommonJS\n        factory(require('jquery'));\n    } else {\n        // Browser globals.\n        factory(jQuery);\n    }\n})(function ($) {\n\n    'use strict';\n\n    // TODO: -\n    // ARIA stuff: menuitem, menuitemcheckbox und menuitemradio\n    // create <menu> structure if $.support[htmlCommand || htmlMenuitem] and !opt.disableNative\n\n    // determine html5 compatibility\n    $.support.htmlMenuitem = ('HTMLMenuItemElement' in window);\n    $.support.htmlCommand = ('HTMLCommandElement' in window);\n    $.support.eventSelectstart = ('onselectstart' in document.documentElement);\n    /* // should the need arise, test for css user-select\n     $.support.cssUserSelect = (function(){\n     var t = false,\n     e = document.createElement('div');\n\n     $.each('Moz|Webkit|Khtml|O|ms|Icab|'.split('|'), function(i, prefix) {\n     var propCC = prefix + (prefix ? 'U' : 'u') + 'serSelect',\n     prop = (prefix ? ('-' + prefix.toLowerCase() + '-') : '') + 'user-select';\n\n     e.style.cssText = prop + ': text;';\n     if (e.style[propCC] == 'text') {\n     t = true;\n     return false;\n     }\n\n     return true;\n     });\n\n     return t;\n     })();\n     */\n\n\n    if (!$.ui || !$.widget) {\n        // duck punch $.cleanData like jQueryUI does to get that remove event\n        $.cleanData = (function (orig) {\n            return function (elems) {\n                var events, elem, i;\n                for (i = 0; elems[i] != null; i++) {\n                    elem = elems[i];\n                    try {\n                        // Only trigger remove when necessary to save time\n                        events = $._data(elem, 'events');\n                        if (events && events.remove) {\n                            $(elem).triggerHandler('remove');\n                        }\n\n                        // Http://bugs.jquery.com/ticket/8235\n                    } catch (e) {\n                    }\n                }\n                orig(elems);\n            };\n        })($.cleanData);\n    }\n    /* jshint ignore:end */\n    // jscs:enable\n\n    var // currently active contextMenu trigger\n        $currentTrigger = null,\n        // is contextMenu initialized with at least one menu?\n        initialized = false,\n        // window handle\n        $win = $(window),\n        // number of registered menus\n        counter = 0,\n        // mapping selector to namespace\n        namespaces = {},\n        // mapping namespace to options\n        menus = {},\n        // custom command type handlers\n        types = {},\n        // default values\n        defaults = {\n            // selector of contextMenu trigger\n            selector: null,\n            // where to append the menu to\n            appendTo: null,\n            // method to trigger context menu [\"right\", \"left\", \"hover\"]\n            trigger: 'right',\n            // hide menu when mouse leaves trigger / menu elements\n            autoHide: false,\n            // ms to wait before showing a hover-triggered context menu\n            delay: 200,\n            // flag denoting if a second trigger should simply move (true) or rebuild (false) an open menu\n            // as long as the trigger happened on one of the trigger-element's child nodes\n            reposition: true,\n            // Flag denoting if a second trigger should close the menu, as long as \n            // the trigger happened on one of the trigger-element's child nodes.\n            // This overrides the reposition option.\n            hideOnSecondTrigger: false,\n\n            //ability to select submenu\n            selectableSubMenu: false,\n\n            // Default classname configuration to be able avoid conflicts in frameworks\n            classNames: {\n                hover: 'context-menu-hover', // Item hover\n                disabled: 'context-menu-disabled', // Item disabled\n                visible: 'context-menu-visible', // Item visible\n                notSelectable: 'context-menu-not-selectable', // Item not selectable\n\n                icon: 'context-menu-icon',\n                iconEdit: 'context-menu-icon-edit',\n                iconCut: 'context-menu-icon-cut',\n                iconCopy: 'context-menu-icon-copy',\n                iconPaste: 'context-menu-icon-paste',\n                iconDelete: 'context-menu-icon-delete',\n                iconAdd: 'context-menu-icon-add',\n                iconQuit: 'context-menu-icon-quit',\n                iconLoadingClass: 'context-menu-icon-loading'\n            },\n\n            // determine position to show menu at\n            determinePosition: function ($menu) {\n                // position to the lower middle of the trigger element\n                if ($.ui && $.ui.position) {\n                    // .position() is provided as a jQuery UI utility\n                    // (...and it won't work on hidden elements)\n                    $menu.css('display', 'block').position({\n                        my: 'center top',\n                        at: 'center bottom',\n                        of: this,\n                        offset: '0 5',\n                        collision: 'fit'\n                    }).css('display', 'none');\n                } else {\n                    // determine contextMenu position\n                    var offset = this.offset();\n                    offset.top += this.outerHeight();\n                    offset.left += this.outerWidth() / 2 - $menu.outerWidth() / 2;\n                    $menu.css(offset);\n                }\n            },\n            // position menu\n            position: function (opt, x, y) {\n                var offset;\n                // determine contextMenu position\n                if (!x && !y) {\n                    opt.determinePosition.call(this, opt.$menu);\n                    return;\n                } else if (x === 'maintain' && y === 'maintain') {\n                    // x and y must not be changed (after re-show on command click)\n                    offset = opt.$menu.position();\n                } else {\n                    // x and y are given (by mouse event)\n                    var offsetParentOffset = opt.$menu.offsetParent().offset();\n                    offset = {top: y - offsetParentOffset.top, left: x -offsetParentOffset.left};\n                }\n\n                // correct offset if viewport demands it\n                var bottom = $win.scrollTop() + $win.height(),\n                    right = $win.scrollLeft() + $win.width(),\n                    height = opt.$menu.outerHeight(),\n                    width = opt.$menu.outerWidth();\n\n                if (offset.top + height > bottom) {\n                    offset.top -= height;\n                }\n\n                if (offset.top < 0) {\n                    offset.top = 0;\n                }\n\n                if (offset.left + width > right) {\n                    offset.left -= width;\n                }\n\n                if (offset.left < 0) {\n                    offset.left = 0;\n                }\n\n                opt.$menu.css(offset);\n            },\n            // position the sub-menu\n            positionSubmenu: function ($menu) {\n                if (typeof $menu === 'undefined') {\n                    // When user hovers over item (which has sub items) handle.focusItem will call this.\n                    // but the submenu does not exist yet if opt.items is a promise. just return, will\n                    // call positionSubmenu after promise is completed.\n                    return;\n                }\n                if ($.ui && $.ui.position) {\n                    // .position() is provided as a jQuery UI utility\n                    // (...and it won't work on hidden elements)\n                    $menu.css('display', 'block').position({\n                        my: 'left top-5',\n                        at: 'right top',\n                        of: this,\n                        collision: 'flipfit fit'\n                    }).css('display', '');\n                } else {\n                    // determine contextMenu position\n                    var offset = {\n                        top: -9,\n                        left: this.outerWidth() - 5\n                    };\n                    $menu.css(offset);\n                }\n            },\n            // offset to add to zIndex\n            zIndex: 1,\n            // show hide animation settings\n            animation: {\n                duration: 50,\n                show: 'slideDown',\n                hide: 'slideUp'\n            },\n            // events\n            events: {\n                show: $.noop,\n                hide: $.noop,\n                activated: $.noop\n            },\n            // default callback\n            callback: null,\n            // list of contextMenu items\n            items: {}\n        },\n        // mouse position for hover activation\n        hoveract = {\n            timer: null,\n            pageX: null,\n            pageY: null\n        },\n        // determine zIndex\n        zindex = function ($t) {\n            var zin = 0,\n                $tt = $t;\n\n            while (true) {\n                zin = Math.max(zin, parseInt($tt.css('z-index'), 10) || 0);\n                $tt = $tt.parent();\n                if (!$tt || !$tt.length || 'html body'.indexOf($tt.prop('nodeName').toLowerCase()) > -1) {\n                    break;\n                }\n            }\n            return zin;\n        },\n        // event handlers\n        handle = {\n            // abort anything\n            abortevent: function (e) {\n                e.preventDefault();\n                e.stopImmediatePropagation();\n            },\n            // contextmenu show dispatcher\n            contextmenu: function (e) {\n                var $this = $(this);\n\n                // disable actual context-menu if we are using the right mouse button as the trigger\n                if (e.data.trigger === 'right') {\n                    e.preventDefault();\n                    e.stopImmediatePropagation();\n                }\n\n                // abort native-triggered events unless we're triggering on right click\n                if ((e.data.trigger !== 'right' && e.data.trigger !== 'demand') && e.originalEvent) {\n                    return;\n                }\n\n                // Let the current contextmenu decide if it should show or not based on its own trigger settings\n                if (typeof e.mouseButton !== 'undefined' && e.data) {\n                    if (!(e.data.trigger === 'left' && e.mouseButton === 0) && !(e.data.trigger === 'right' && e.mouseButton === 2)) {\n                        // Mouse click is not valid.\n                        return;\n                    }\n                }\n\n                // abort event if menu is visible for this trigger\n                if ($this.hasClass('context-menu-active')) {\n                    return;\n                }\n\n                if (!$this.hasClass('context-menu-disabled')) {\n                    // theoretically need to fire a show event at <menu>\n                    // http://www.whatwg.org/specs/web-apps/current-work/multipage/interactive-elements.html#context-menus\n                    // var evt = jQuery.Event(\"show\", { data: data, pageX: e.pageX, pageY: e.pageY, relatedTarget: this });\n                    // e.data.$menu.trigger(evt);\n\n                    $currentTrigger = $this;\n                    if (e.data.build) {\n                        var built = e.data.build($currentTrigger, e);\n                        // abort if build() returned false\n                        if (built === false) {\n                            return;\n                        }\n\n                        // dynamically build menu on invocation\n                        e.data = $.extend(true, {}, defaults, e.data, built || {});\n\n                        // abort if there are no items to display\n                        if (!e.data.items || $.isEmptyObject(e.data.items)) {\n                            // Note: jQuery captures and ignores errors from event handlers\n                            if (window.console) {\n                                (console.error || console.log).call(console, 'No items specified to show in contextMenu');\n                            }\n\n                            throw new Error('No Items specified');\n                        }\n\n                        // backreference for custom command type creation\n                        e.data.$trigger = $currentTrigger;\n\n                        op.create(e.data);\n                    }\n                    var showMenu = false;\n                    for (var item in e.data.items) {\n                        if (e.data.items.hasOwnProperty(item)) {\n                            var visible;\n                            if ($.isFunction(e.data.items[item].visible)) {\n                                visible = e.data.items[item].visible.call($(e.currentTarget), item, e.data);\n                            } else if (typeof e.data.items[item] !== 'undefined' && e.data.items[item].visible) {\n                                visible = e.data.items[item].visible === true;\n                            } else {\n                                visible = true;\n                            }\n                            if (visible) {\n                                showMenu = true;\n                            }\n                        }\n                    }\n                    if (showMenu) {\n                        // show menu\n                        op.show.call($this, e.data, e.pageX, e.pageY);\n                    }\n                }\n            },\n            // contextMenu left-click trigger\n            click: function (e) {\n                e.preventDefault();\n                e.stopImmediatePropagation();\n                $(this).trigger($.Event('contextmenu', {data: e.data, pageX: e.pageX, pageY: e.pageY}));\n            },\n            // contextMenu right-click trigger\n            mousedown: function (e) {\n                // register mouse down\n                var $this = $(this);\n\n                // hide any previous menus\n                if ($currentTrigger && $currentTrigger.length && !$currentTrigger.is($this)) {\n                    $currentTrigger.data('contextMenu').$menu.trigger('contextmenu:hide');\n                }\n\n                // activate on right click\n                if (e.button === 2) {\n                    $currentTrigger = $this.data('contextMenuActive', true);\n                }\n            },\n            // contextMenu right-click trigger\n            mouseup: function (e) {\n                // show menu\n                var $this = $(this);\n                if ($this.data('contextMenuActive') && $currentTrigger && $currentTrigger.length && $currentTrigger.is($this) && !$this.hasClass('context-menu-disabled')) {\n                    e.preventDefault();\n                    e.stopImmediatePropagation();\n                    $currentTrigger = $this;\n                    $this.trigger($.Event('contextmenu', {data: e.data, pageX: e.pageX, pageY: e.pageY}));\n                }\n\n                $this.removeData('contextMenuActive');\n            },\n            // contextMenu hover trigger\n            mouseenter: function (e) {\n                var $this = $(this),\n                    $related = $(e.relatedTarget),\n                    $document = $(document);\n\n                // abort if we're coming from a menu\n                if ($related.is('.context-menu-list') || $related.closest('.context-menu-list').length) {\n                    return;\n                }\n\n                // abort if a menu is shown\n                if ($currentTrigger && $currentTrigger.length) {\n                    return;\n                }\n\n                hoveract.pageX = e.pageX;\n                hoveract.pageY = e.pageY;\n                hoveract.data = e.data;\n                $document.on('mousemove.contextMenuShow', handle.mousemove);\n                hoveract.timer = setTimeout(function () {\n                    hoveract.timer = null;\n                    $document.off('mousemove.contextMenuShow');\n                    $currentTrigger = $this;\n                    $this.trigger($.Event('contextmenu', {\n                        data: hoveract.data,\n                        pageX: hoveract.pageX,\n                        pageY: hoveract.pageY\n                    }));\n                }, e.data.delay);\n            },\n            // contextMenu hover trigger\n            mousemove: function (e) {\n                hoveract.pageX = e.pageX;\n                hoveract.pageY = e.pageY;\n            },\n            // contextMenu hover trigger\n            mouseleave: function (e) {\n                // abort if we're leaving for a menu\n                var $related = $(e.relatedTarget);\n                if ($related.is('.context-menu-list') || $related.closest('.context-menu-list').length) {\n                    return;\n                }\n\n                try {\n                    clearTimeout(hoveract.timer);\n                } catch (e) {\n                }\n\n                hoveract.timer = null;\n            },\n            // click on layer to hide contextMenu\n            layerClick: function (e) {\n                var $this = $(this),\n                    root = $this.data('contextMenuRoot'),\n                    button = e.button,\n                    x = e.pageX,\n                    y = e.pageY,\n                    target,\n                    offset;\n\n                e.preventDefault();\n\n                setTimeout(function () {\n                    var $window;\n                    var triggerAction = ((root.trigger === 'left' && button === 0) || (root.trigger === 'right' && button === 2));\n\n                    // find the element that would've been clicked, wasn't the layer in the way\n                    if (document.elementFromPoint && root.$layer) {\n                        root.$layer.hide();\n                        target = document.elementFromPoint(x - $win.scrollLeft(), y - $win.scrollTop());\n\n                        // also need to try and focus this element if we're in a contenteditable area,\n                        // as the layer will prevent the browser mouse action we want\n                        if (target.isContentEditable) {\n                            var range = document.createRange(),\n                                sel = window.getSelection();\n                            range.selectNode(target);\n                            range.collapse(true);\n                            sel.removeAllRanges();\n                            sel.addRange(range);\n                        }\n                        $(target).trigger(e);\n                        root.$layer.show();\n                    }\n                    \n                    if (root.hideOnSecondTrigger && triggerAction && root.$menu !== null && typeof root.$menu !== 'undefined') {\n                      root.$menu.trigger('contextmenu:hide');\n                      return;\n                    }\n                    \n                    if (root.reposition && triggerAction) {\n                        if (document.elementFromPoint) {\n                            if (root.$trigger.is(target)) {\n                                root.position.call(root.$trigger, root, x, y);\n                                return;\n                            }\n                        } else {\n                            offset = root.$trigger.offset();\n                            $window = $(window);\n                            // while this looks kinda awful, it's the best way to avoid\n                            // unnecessarily calculating any positions\n                            offset.top += $window.scrollTop();\n                            if (offset.top <= e.pageY) {\n                                offset.left += $window.scrollLeft();\n                                if (offset.left <= e.pageX) {\n                                    offset.bottom = offset.top + root.$trigger.outerHeight();\n                                    if (offset.bottom >= e.pageY) {\n                                        offset.right = offset.left + root.$trigger.outerWidth();\n                                        if (offset.right >= e.pageX) {\n                                            // reposition\n                                            root.position.call(root.$trigger, root, x, y);\n                                            return;\n                                        }\n                                    }\n                                }\n                            }\n                        }\n                    }\n\n                    if (target && triggerAction) {\n                        root.$trigger.one('contextmenu:hidden', function () {\n                            $(target).contextMenu({x: x, y: y, button: button});\n                        });\n                    }\n\n                    if (root !== null && typeof root !== 'undefined' && root.$menu !== null  && typeof root.$menu !== 'undefined') {\n                        root.$menu.trigger('contextmenu:hide');\n                    }\n                }, 50);\n            },\n            // key handled :hover\n            keyStop: function (e, opt) {\n                if (!opt.isInput) {\n                    e.preventDefault();\n                }\n\n                e.stopPropagation();\n            },\n            key: function (e) {\n\n                var opt = {};\n\n                // Only get the data from $currentTrigger if it exists\n                if ($currentTrigger) {\n                    opt = $currentTrigger.data('contextMenu') || {};\n                }\n                // If the trigger happen on a element that are above the contextmenu do this\n                if (typeof opt.zIndex === 'undefined') {\n                    opt.zIndex = 0;\n                }\n                var targetZIndex = 0;\n                var getZIndexOfTriggerTarget = function (target) {\n                    if (target.style.zIndex !== '') {\n                        targetZIndex = target.style.zIndex;\n                    } else {\n                        if (target.offsetParent !== null && typeof target.offsetParent !== 'undefined') {\n                            getZIndexOfTriggerTarget(target.offsetParent);\n                        }\n                        else if (target.parentElement !== null && typeof target.parentElement !== 'undefined') {\n                            getZIndexOfTriggerTarget(target.parentElement);\n                        }\n                    }\n                };\n                getZIndexOfTriggerTarget(e.target);\n                // If targetZIndex is heigher then opt.zIndex dont progress any futher.\n                // This is used to make sure that if you are using a dialog with a input / textarea / contenteditable div\n                // and its above the contextmenu it wont steal keys events\n                if (opt.$menu && parseInt(targetZIndex,10) > parseInt(opt.$menu.css(\"zIndex\"),10)) {\n                    return;\n                }\n                switch (e.keyCode) {\n                    case 9:\n                    case 38: // up\n                        handle.keyStop(e, opt);\n                        // if keyCode is [38 (up)] or [9 (tab) with shift]\n                        if (opt.isInput) {\n                            if (e.keyCode === 9 && e.shiftKey) {\n                                e.preventDefault();\n                                if (opt.$selected) {\n                                    opt.$selected.find('input, textarea, select').blur();\n                                }\n                                if (opt.$menu !== null && typeof opt.$menu !== 'undefined') {\n                                    opt.$menu.trigger('prevcommand');\n                                }\n                                return;\n                            } else if (e.keyCode === 38 && opt.$selected.find('input, textarea, select').prop('type') === 'checkbox') {\n                                // checkboxes don't capture this key\n                                e.preventDefault();\n                                return;\n                            }\n                        } else if (e.keyCode !== 9 || e.shiftKey) {\n                            if (opt.$menu !== null && typeof opt.$menu !== 'undefined') {\n                                opt.$menu.trigger('prevcommand');\n                            }\n                            return;\n                        }\n                        break;\n                    // omitting break;\n                    // case 9: // tab - reached through omitted break;\n                    case 40: // down\n                        handle.keyStop(e, opt);\n                        if (opt.isInput) {\n                            if (e.keyCode === 9) {\n                                e.preventDefault();\n                                if (opt.$selected) {\n                                    opt.$selected.find('input, textarea, select').blur();\n                                }\n                                if (opt.$menu !== null && typeof opt.$menu !== 'undefined') {\n                                    opt.$menu.trigger('nextcommand');\n                                }\n                                return;\n                            } else if (e.keyCode === 40 && opt.$selected.find('input, textarea, select').prop('type') === 'checkbox') {\n                                // checkboxes don't capture this key\n                                e.preventDefault();\n                                return;\n                            }\n                        } else {\n                            if (opt.$menu !== null && typeof opt.$menu !== 'undefined') {\n                                opt.$menu.trigger('nextcommand');\n                            }\n                            return;\n                        }\n                        break;\n\n                    case 37: // left\n                        handle.keyStop(e, opt);\n                        if (opt.isInput || !opt.$selected || !opt.$selected.length) {\n                            break;\n                        }\n\n                        if (!opt.$selected.parent().hasClass('context-menu-root')) {\n                            var $parent = opt.$selected.parent().parent();\n                            opt.$selected.trigger('contextmenu:blur');\n                            opt.$selected = $parent;\n                            return;\n                        }\n                        break;\n\n                    case 39: // right\n                        handle.keyStop(e, opt);\n                        if (opt.isInput || !opt.$selected || !opt.$selected.length) {\n                            break;\n                        }\n\n                        var itemdata = opt.$selected.data('contextMenu') || {};\n                        if (itemdata.$menu && opt.$selected.hasClass('context-menu-submenu')) {\n                            opt.$selected = null;\n                            itemdata.$selected = null;\n                            itemdata.$menu.trigger('nextcommand');\n                            return;\n                        }\n                        break;\n\n                    case 35: // end\n                    case 36: // home\n                        if (opt.$selected && opt.$selected.find('input, textarea, select').length) {\n                            return;\n                        } else {\n                            (opt.$selected && opt.$selected.parent() || opt.$menu)\n                                .children(':not(.' + opt.classNames.disabled + ', .' + opt.classNames.notSelectable + ')')[e.keyCode === 36 ? 'first' : 'last']()\n                                .trigger('contextmenu:focus');\n                            e.preventDefault();\n                            return;\n                        }\n                        break;\n\n                    case 13: // enter\n                        handle.keyStop(e, opt);\n                        if (opt.isInput) {\n                            if (opt.$selected && !opt.$selected.is('textarea, select')) {\n                                e.preventDefault();\n                                return;\n                            }\n                            break;\n                        }\n                        if (typeof opt.$selected !== 'undefined' && opt.$selected !== null) {\n                            opt.$selected.trigger('mouseup');\n                        }\n                        return;\n\n                    case 32: // space\n                    case 33: // page up\n                    case 34: // page down\n                        // prevent browser from scrolling down while menu is visible\n                        handle.keyStop(e, opt);\n                        return;\n\n                    case 27: // esc\n                        handle.keyStop(e, opt);\n                        if (opt.$menu !== null && typeof opt.$menu !== 'undefined') {\n                            opt.$menu.trigger('contextmenu:hide');\n                        }\n                        return;\n\n                    default: // 0-9, a-z\n                        var k = (String.fromCharCode(e.keyCode)).toUpperCase();\n                        if (opt.accesskeys && opt.accesskeys[k]) {\n                            // according to the specs accesskeys must be invoked immediately\n                            opt.accesskeys[k].$node.trigger(opt.accesskeys[k].$menu ? 'contextmenu:focus' : 'mouseup');\n                            return;\n                        }\n                        break;\n                }\n                // pass event to selected item,\n                // stop propagation to avoid endless recursion\n                e.stopPropagation();\n                if (typeof opt.$selected !== 'undefined' && opt.$selected !== null) {\n                    opt.$selected.trigger(e);\n                }\n            },\n            // select previous possible command in menu\n            prevItem: function (e) {\n                e.stopPropagation();\n                var opt = $(this).data('contextMenu') || {};\n                var root = $(this).data('contextMenuRoot') || {};\n\n                // obtain currently selected menu\n                if (opt.$selected) {\n                    var $s = opt.$selected;\n                    opt = opt.$selected.parent().data('contextMenu') || {};\n                    opt.$selected = $s;\n                }\n\n                var $children = opt.$menu.children(),\n                    $prev = !opt.$selected || !opt.$selected.prev().length ? $children.last() : opt.$selected.prev(),\n                    $round = $prev;\n\n                // skip disabled or hidden elements\n                while ($prev.hasClass(root.classNames.disabled) || $prev.hasClass(root.classNames.notSelectable) || $prev.is(':hidden')) {\n                    if ($prev.prev().length) {\n                        $prev = $prev.prev();\n                    } else {\n                        $prev = $children.last();\n                    }\n                    if ($prev.is($round)) {\n                        // break endless loop\n                        return;\n                    }\n                }\n\n                // leave current\n                if (opt.$selected) {\n                    handle.itemMouseleave.call(opt.$selected.get(0), e);\n                }\n\n                // activate next\n                handle.itemMouseenter.call($prev.get(0), e);\n\n                // focus input\n                var $input = $prev.find('input, textarea, select');\n                if ($input.length) {\n                    $input.focus();\n                }\n            },\n            // select next possible command in menu\n            nextItem: function (e) {\n                e.stopPropagation();\n                var opt = $(this).data('contextMenu') || {};\n                var root = $(this).data('contextMenuRoot') || {};\n\n                // obtain currently selected menu\n                if (opt.$selected) {\n                    var $s = opt.$selected;\n                    opt = opt.$selected.parent().data('contextMenu') || {};\n                    opt.$selected = $s;\n                }\n\n                var $children = opt.$menu.children(),\n                    $next = !opt.$selected || !opt.$selected.next().length ? $children.first() : opt.$selected.next(),\n                    $round = $next;\n\n                // skip disabled\n                while ($next.hasClass(root.classNames.disabled) || $next.hasClass(root.classNames.notSelectable) || $next.is(':hidden')) {\n                    if ($next.next().length) {\n                        $next = $next.next();\n                    } else {\n                        $next = $children.first();\n                    }\n                    if ($next.is($round)) {\n                        // break endless loop\n                        return;\n                    }\n                }\n\n                // leave current\n                if (opt.$selected) {\n                    handle.itemMouseleave.call(opt.$selected.get(0), e);\n                }\n\n                // activate next\n                handle.itemMouseenter.call($next.get(0), e);\n\n                // focus input\n                var $input = $next.find('input, textarea, select');\n                if ($input.length) {\n                    $input.focus();\n                }\n            },\n            // flag that we're inside an input so the key handler can act accordingly\n            focusInput: function () {\n                var $this = $(this).closest('.context-menu-item'),\n                    data = $this.data(),\n                    opt = data.contextMenu,\n                    root = data.contextMenuRoot;\n\n                root.$selected = opt.$selected = $this;\n                root.isInput = opt.isInput = true;\n            },\n            // flag that we're inside an input so the key handler can act accordingly\n            blurInput: function () {\n                var $this = $(this).closest('.context-menu-item'),\n                    data = $this.data(),\n                    opt = data.contextMenu,\n                    root = data.contextMenuRoot;\n\n                root.isInput = opt.isInput = false;\n            },\n            // :hover on menu\n            menuMouseenter: function () {\n                var root = $(this).data().contextMenuRoot;\n                root.hovering = true;\n            },\n            // :hover on menu\n            menuMouseleave: function (e) {\n                var root = $(this).data().contextMenuRoot;\n                if (root.$layer && root.$layer.is(e.relatedTarget)) {\n                    root.hovering = false;\n                }\n            },\n            // :hover done manually so key handling is possible\n            itemMouseenter: function (e) {\n                var $this = $(this),\n                    data = $this.data(),\n                    opt = data.contextMenu,\n                    root = data.contextMenuRoot;\n\n                root.hovering = true;\n\n                // abort if we're re-entering\n                if (e && root.$layer && root.$layer.is(e.relatedTarget)) {\n                    e.preventDefault();\n                    e.stopImmediatePropagation();\n                }\n\n                // make sure only one item is selected\n                (opt.$menu ? opt : root).$menu\n                    .children('.' + root.classNames.hover).trigger('contextmenu:blur')\n                    .children('.hover').trigger('contextmenu:blur');\n\n                if ($this.hasClass(root.classNames.disabled) || $this.hasClass(root.classNames.notSelectable)) {\n                    opt.$selected = null;\n                    return;\n                }\n\n\n                $this.trigger('contextmenu:focus');\n            },\n            // :hover done manually so key handling is possible\n            itemMouseleave: function (e) {\n                var $this = $(this),\n                    data = $this.data(),\n                    opt = data.contextMenu,\n                    root = data.contextMenuRoot;\n\n                if (root !== opt && root.$layer && root.$layer.is(e.relatedTarget)) {\n                    if (typeof root.$selected !== 'undefined' && root.$selected !== null) {\n                        root.$selected.trigger('contextmenu:blur');\n                    }\n                    e.preventDefault();\n                    e.stopImmediatePropagation();\n                    root.$selected = opt.$selected = opt.$node;\n                    return;\n                }\n\n                if(opt && opt.$menu && opt.$menu.hasClass('context-menu-visible')){\n                    return;\n                }\n\n                $this.trigger('contextmenu:blur');\n            },\n            // contextMenu item click\n            itemClick: function (e) {\n                var $this = $(this),\n                    data = $this.data(),\n                    opt = data.contextMenu,\n                    root = data.contextMenuRoot,\n                    key = data.contextMenuKey,\n                    callback;\n\n                // abort if the key is unknown or disabled or is a menu\n                if (!opt.items[key] || $this.is('.' + root.classNames.disabled + ', .context-menu-separator, .' + root.classNames.notSelectable) || ($this.is('.context-menu-submenu') && root.selectableSubMenu === false )) {\n                    return;\n                }\n\n                e.preventDefault();\n                e.stopImmediatePropagation();\n\n                if ($.isFunction(opt.callbacks[key]) && Object.prototype.hasOwnProperty.call(opt.callbacks, key)) {\n                    // item-specific callback\n                    callback = opt.callbacks[key];\n                } else if ($.isFunction(root.callback)) {\n                    // default callback\n                    callback = root.callback;\n                } else {\n                    // no callback, no action\n                    return;\n                }\n\n                // hide menu if callback doesn't stop that\n                if (callback.call(root.$trigger, key, root, e) !== false) {\n                    root.$menu.trigger('contextmenu:hide');\n                } else if (root.$menu.parent().length) {\n                    op.update.call(root.$trigger, root);\n                }\n            },\n            // ignore click events on input elements\n            inputClick: function (e) {\n                e.stopImmediatePropagation();\n            },\n            // hide <menu>\n            hideMenu: function (e, data) {\n                var root = $(this).data('contextMenuRoot');\n                op.hide.call(root.$trigger, root, data && data.force);\n            },\n            // focus <command>\n            focusItem: function (e) {\n                e.stopPropagation();\n                var $this = $(this),\n                    data = $this.data(),\n                    opt = data.contextMenu,\n                    root = data.contextMenuRoot;\n\n                if ($this.hasClass(root.classNames.disabled) || $this.hasClass(root.classNames.notSelectable)) {\n                    return;\n                }\n\n                $this\n                    .addClass([root.classNames.hover, root.classNames.visible].join(' '))\n                    // select other items and included items\n                    .parent().find('.context-menu-item').not($this)\n                    .removeClass(root.classNames.visible)\n                    .filter('.' + root.classNames.hover)\n                    .trigger('contextmenu:blur');\n\n                // remember selected\n                opt.$selected = root.$selected = $this;\n\n\n                if(opt && opt.$node && opt.$node.hasClass('context-menu-submenu')){\n                    opt.$node.addClass(root.classNames.hover);\n                }\n\n                // position sub-menu - do after show so dumb $.ui.position can keep up\n                if (opt.$node) {\n                    root.positionSubmenu.call(opt.$node, opt.$menu);\n                }\n            },\n            // blur <command>\n            blurItem: function (e) {\n                e.stopPropagation();\n                var $this = $(this),\n                    data = $this.data(),\n                    opt = data.contextMenu,\n                    root = data.contextMenuRoot;\n\n                if (opt.autoHide) { // for tablets and touch screens this needs to remain\n                    $this.removeClass(root.classNames.visible);\n                }\n                $this.removeClass(root.classNames.hover);\n                opt.$selected = null;\n            }\n        },\n        // operations\n        op = {\n            show: function (opt, x, y) {\n                var $trigger = $(this),\n                    css = {};\n\n                // hide any open menus\n                $('#context-menu-layer').trigger('mousedown');\n\n                // backreference for callbacks\n                opt.$trigger = $trigger;\n\n                // show event\n                if (opt.events.show.call($trigger, opt) === false) {\n                    $currentTrigger = null;\n                    return;\n                }\n\n                // create or update context menu\n                op.update.call($trigger, opt);\n\n                // position menu\n                opt.position.call($trigger, opt, x, y);\n\n                // make sure we're in front\n                if (opt.zIndex) {\n                    var additionalZValue = opt.zIndex;\n                    // If opt.zIndex is a function, call the function to get the right zIndex.\n                    if (typeof opt.zIndex === 'function') {\n                        additionalZValue = opt.zIndex.call($trigger, opt);\n                    }\n                    css.zIndex = zindex($trigger) + additionalZValue;\n                }\n\n                // add layer\n                op.layer.call(opt.$menu, opt, css.zIndex);\n\n                // adjust sub-menu zIndexes\n                opt.$menu.find('ul').css('zIndex', css.zIndex + 1);\n\n                // position and show context menu\n                opt.$menu.css(css)[opt.animation.show](opt.animation.duration, function () {\n                    $trigger.trigger('contextmenu:visible');\n                    \n                    op.activated(opt);\n                    opt.events.activated();\n                });\n                // make options available and set state\n                $trigger\n                    .data('contextMenu', opt)\n                    .addClass('context-menu-active');\n\n                // register key handler\n                $(document).off('keydown.contextMenu').on('keydown.contextMenu', handle.key);\n                // register autoHide handler\n                if (opt.autoHide) {\n                    // mouse position handler\n                    $(document).on('mousemove.contextMenuAutoHide', function (e) {\n                        // need to capture the offset on mousemove,\n                        // since the page might've been scrolled since activation\n                        var pos = $trigger.offset();\n                        pos.right = pos.left + $trigger.outerWidth();\n                        pos.bottom = pos.top + $trigger.outerHeight();\n\n                        if (opt.$layer && !opt.hovering && (!(e.pageX >= pos.left && e.pageX <= pos.right) || !(e.pageY >= pos.top && e.pageY <= pos.bottom))) {\n                            /* Additional hover check after short time, you might just miss the edge of the menu */\n                            setTimeout(function () {\n                                if (!opt.hovering && opt.$menu !== null && typeof opt.$menu !== 'undefined') {\n                                    opt.$menu.trigger('contextmenu:hide');\n                                }\n                            }, 50);\n                        }\n                    });\n                }\n            },\n            hide: function (opt, force) {\n                var $trigger = $(this);\n                if (!opt) {\n                    opt = $trigger.data('contextMenu') || {};\n                }\n\n                // hide event\n                if (!force && opt.events && opt.events.hide.call($trigger, opt) === false) {\n                    return;\n                }\n\n                // remove options and revert state\n                $trigger\n                    .removeData('contextMenu')\n                    .removeClass('context-menu-active');\n\n                if (opt.$layer) {\n                    // keep layer for a bit so the contextmenu event can be aborted properly by opera\n                    setTimeout((function ($layer) {\n                        return function () {\n                            $layer.remove();\n                        };\n                    })(opt.$layer), 10);\n\n                    try {\n                        delete opt.$layer;\n                    } catch (e) {\n                        opt.$layer = null;\n                    }\n                }\n\n                // remove handle\n                $currentTrigger = null;\n                // remove selected\n                opt.$menu.find('.' + opt.classNames.hover).trigger('contextmenu:blur');\n                opt.$selected = null;\n                // collapse all submenus\n                opt.$menu.find('.' + opt.classNames.visible).removeClass(opt.classNames.visible);\n                // unregister key and mouse handlers\n                // $(document).off('.contextMenuAutoHide keydown.contextMenu'); // http://bugs.jquery.com/ticket/10705\n                $(document).off('.contextMenuAutoHide').off('keydown.contextMenu');\n                // hide menu\n                if (opt.$menu) {\n                    opt.$menu[opt.animation.hide](opt.animation.duration, function () {\n                        // tear down dynamically built menu after animation is completed.\n                        if (opt.build) {\n                            opt.$menu.remove();\n                            $.each(opt, function (key) {\n                                switch (key) {\n                                    case 'ns':\n                                    case 'selector':\n                                    case 'build':\n                                    case 'trigger':\n                                        return true;\n\n                                    default:\n                                        opt[key] = undefined;\n                                        try {\n                                            delete opt[key];\n                                        } catch (e) {\n                                        }\n                                        return true;\n                                }\n                            });\n                        }\n\n                        setTimeout(function () {\n                            $trigger.trigger('contextmenu:hidden');\n                        }, 10);\n                    });\n                }\n            },\n            create: function (opt, root) {\n                if (typeof root === 'undefined') {\n                    root = opt;\n                }\n\n                // create contextMenu\n                opt.$menu = $('<ul class=\"context-menu-list\"></ul>').addClass(opt.className || '').data({\n                    'contextMenu': opt,\n                    'contextMenuRoot': root\n                });\n\n                $.each(['callbacks', 'commands', 'inputs'], function (i, k) {\n                    opt[k] = {};\n                    if (!root[k]) {\n                        root[k] = {};\n                    }\n                });\n\n                if (!root.accesskeys) {\n                    root.accesskeys = {};\n                }\n\n                function createNameNode(item) {\n                    var $name = $('<span></span>');\n                    if (item._accesskey) {\n                        if (item._beforeAccesskey) {\n                            $name.append(document.createTextNode(item._beforeAccesskey));\n                        }\n                        $('<span></span>')\n                            .addClass('context-menu-accesskey')\n                            .text(item._accesskey)\n                            .appendTo($name);\n                        if (item._afterAccesskey) {\n                            $name.append(document.createTextNode(item._afterAccesskey));\n                        }\n                    } else {\n                        if (item.isHtmlName) {\n                            // restrict use with access keys\n                            if (typeof item.accesskey !== 'undefined') {\n                                throw new Error('accesskeys are not compatible with HTML names and cannot be used together in the same item');\n                            }\n                            $name.html(item.name);\n                        } else {\n                            $name.text(item.name);\n                        }\n                    }\n                    return $name;\n                }\n\n                // create contextMenu items\n                $.each(opt.items, function (key, item) {\n                    var $t = $('<li class=\"context-menu-item\"></li>').addClass(item.className || ''),\n                        $label = null,\n                        $input = null;\n\n                    // iOS needs to see a click-event bound to an element to actually\n                    // have the TouchEvents infrastructure trigger the click event\n                    $t.on('click', $.noop);\n\n                    // Make old school string seperator a real item so checks wont be\n                    // akward later.\n                    // And normalize 'cm_separator' into 'cm_seperator'.\n                    if (typeof item === 'string' || item.type === 'cm_separator') {\n                        item = {type: 'cm_seperator'};\n                    }\n\n                    item.$node = $t.data({\n                        'contextMenu': opt,\n                        'contextMenuRoot': root,\n                        'contextMenuKey': key\n                    });\n\n                    // register accesskey\n                    // NOTE: the accesskey attribute should be applicable to any element, but Safari5 and Chrome13 still can't do that\n                    if (typeof item.accesskey !== 'undefined') {\n                        var aks = splitAccesskey(item.accesskey);\n                        for (var i = 0, ak; ak = aks[i]; i++) {\n                            if (!root.accesskeys[ak]) {\n                                root.accesskeys[ak] = item;\n                                var matched = item.name.match(new RegExp('^(.*?)(' + ak + ')(.*)$', 'i'));\n                                if (matched) {\n                                    item._beforeAccesskey = matched[1];\n                                    item._accesskey = matched[2];\n                                    item._afterAccesskey = matched[3];\n                                }\n                                break;\n                            }\n                        }\n                    }\n\n                    if (item.type && types[item.type]) {\n                        // run custom type handler\n                        types[item.type].call($t, item, opt, root);\n                        // register commands\n                        $.each([opt, root], function (i, k) {\n                            k.commands[key] = item;\n                            // Overwrite only if undefined or the item is appended to the root. This so it\n                            // doesn't overwrite callbacks of root elements if the name is the same.\n                            if ($.isFunction(item.callback) && (typeof k.callbacks[key] === 'undefined' || typeof opt.type === 'undefined')) {\n                                k.callbacks[key] = item.callback;\n                            }\n                        });\n                    } else {\n                        // add label for input\n                        if (item.type === 'cm_seperator') {\n                            $t.addClass('context-menu-separator ' + root.classNames.notSelectable);\n                        } else if (item.type === 'html') {\n                            $t.addClass('context-menu-html ' + root.classNames.notSelectable);\n                        } else if (item.type === 'sub') {\n                            // We don't want to execute the next else-if if it is a sub.\n                        } else if (item.type) {\n                            $label = $('<label></label>').appendTo($t);\n                            createNameNode(item).appendTo($label);\n\n                            $t.addClass('context-menu-input');\n                            opt.hasTypes = true;\n                            $.each([opt, root], function (i, k) {\n                                k.commands[key] = item;\n                                k.inputs[key] = item;\n                            });\n                        } else if (item.items) {\n                            item.type = 'sub';\n                        }\n\n                        switch (item.type) {\n                            case 'cm_seperator':\n                                break;\n\n                            case 'text':\n                                $input = $('<input type=\"text\" value=\"1\" name=\"\" />')\n                                    .attr('name', 'context-menu-input-' + key)\n                                    .val(item.value || '')\n                                    .appendTo($label);\n                                break;\n\n                            case 'textarea':\n                                $input = $('<textarea name=\"\"></textarea>')\n                                    .attr('name', 'context-menu-input-' + key)\n                                    .val(item.value || '')\n                                    .appendTo($label);\n\n                                if (item.height) {\n                                    $input.height(item.height);\n                                }\n                                break;\n\n                            case 'checkbox':\n                                $input = $('<input type=\"checkbox\" value=\"1\" name=\"\" />')\n                                    .attr('name', 'context-menu-input-' + key)\n                                    .val(item.value || '')\n                                    .prop('checked', !!item.selected)\n                                    .prependTo($label);\n                                break;\n\n                            case 'radio':\n                                $input = $('<input type=\"radio\" value=\"1\" name=\"\" />')\n                                    .attr('name', 'context-menu-input-' + item.radio)\n                                    .val(item.value || '')\n                                    .prop('checked', !!item.selected)\n                                    .prependTo($label);\n                                break;\n\n                            case 'select':\n                                $input = $('<select name=\"\"></select>')\n                                    .attr('name', 'context-menu-input-' + key)\n                                    .appendTo($label);\n                                if (item.options) {\n                                    $.each(item.options, function (value, text) {\n                                        $('<option></option>').val(value).text(text).appendTo($input);\n                                    });\n                                    $input.val(item.selected);\n                                }\n                                break;\n\n                            case 'sub':\n                                createNameNode(item).appendTo($t);\n                                item.appendTo = item.$node;\n                                $t.data('contextMenu', item).addClass('context-menu-submenu');\n                                item.callback = null;\n\n                                // If item contains items, and this is a promise, we should create it later\n                                // check if subitems is of type promise. If it is a promise we need to create\n                                // it later, after promise has been resolved.\n                                if ('function' === typeof item.items.then) {\n                                    // probably a promise, process it, when completed it will create the sub menu's.\n                                    op.processPromises(item, root, item.items);\n                                } else {\n                                    // normal submenu.\n                                    op.create(item, root);\n                                }\n                                break;\n\n                            case 'html':\n                                $(item.html).appendTo($t);\n                                break;\n\n                            default:\n                                $.each([opt, root], function (i, k) {\n                                    k.commands[key] = item;\n                                    // Overwrite only if undefined or the item is appended to the root. This so it\n                                    // doesn't overwrite callbacks of root elements if the name is the same.\n                                    if ($.isFunction(item.callback) && (typeof k.callbacks[key] === 'undefined' || typeof opt.type === 'undefined')) {\n                                        k.callbacks[key] = item.callback;\n                                    }\n                                });\n                                createNameNode(item).appendTo($t);\n                                break;\n                        }\n\n                        // disable key listener in <input>\n                        if (item.type && item.type !== 'sub' && item.type !== 'html' && item.type !== 'cm_seperator') {\n                            $input\n                                .on('focus', handle.focusInput)\n                                .on('blur', handle.blurInput);\n\n                            if (item.events) {\n                                $input.on(item.events, opt);\n                            }\n                        }\n\n                        // add icons\n                        if (item.icon) {\n                            if ($.isFunction(item.icon)) {\n                                item._icon = item.icon.call(this, this, $t, key, item);\n                            } else {\n                                if (typeof(item.icon) === 'string' && item.icon.substring(0, 3) === 'fa-') {\n                                    // to enable font awesome\n                                    item._icon = root.classNames.icon + ' ' + root.classNames.icon + '--fa fa ' + item.icon;\n                                } else {\n                                    item._icon = root.classNames.icon + ' ' + root.classNames.icon + '-' + item.icon;\n                                }\n                            }\n                            $t.addClass(item._icon);\n                        }\n                    }\n\n                    // cache contained elements\n                    item.$input = $input;\n                    item.$label = $label;\n\n                    // attach item to menu\n                    $t.appendTo(opt.$menu);\n\n                    // Disable text selection\n                    if (!opt.hasTypes && $.support.eventSelectstart) {\n                        // browsers support user-select: none,\n                        // IE has a special event for text-selection\n                        // browsers supporting neither will not be preventing text-selection\n                        $t.on('selectstart.disableTextSelect', handle.abortevent);\n                    }\n                });\n                // attach contextMenu to <body> (to bypass any possible overflow:hidden issues on parents of the trigger element)\n                if (!opt.$node) {\n                    opt.$menu.css('display', 'none').addClass('context-menu-root');\n                }\n                opt.$menu.appendTo(opt.appendTo || document.body);\n            },\n            resize: function ($menu, nested) {\n                var domMenu;\n                // determine widths of submenus, as CSS won't grow them automatically\n                // position:absolute within position:absolute; min-width:100; max-width:200; results in width: 100;\n                // kinda sucks hard...\n\n                // determine width of absolutely positioned element\n                $menu.css({position: 'absolute', display: 'block'});\n                // don't apply yet, because that would break nested elements' widths\n                $menu.data('width',\n                    (domMenu = $menu.get(0)).getBoundingClientRect ?\n                        Math.ceil(domMenu.getBoundingClientRect().width) :\n                        $menu.outerWidth() + 1); // outerWidth() returns rounded pixels\n                // reset styles so they allow nested elements to grow/shrink naturally\n                $menu.css({\n                    position: 'static',\n                    minWidth: '0px',\n                    maxWidth: '100000px'\n                });\n                // identify width of nested menus\n                $menu.find('> li > ul').each(function () {\n                    op.resize($(this), true);\n                });\n                // reset and apply changes in the end because nested\n                // elements' widths wouldn't be calculatable otherwise\n                if (!nested) {\n                    $menu.find('ul').addBack().css({\n                        position: '',\n                        display: '',\n                        minWidth: '',\n                        maxWidth: ''\n                    }).outerWidth(function () {\n                        return $(this).data('width');\n                    });\n                }\n            },\n            update: function (opt, root) {\n                var $trigger = this;\n                if (typeof root === 'undefined') {\n                    root = opt;\n                    op.resize(opt.$menu);\n                }\n                // re-check disabled for each item\n                opt.$menu.children().each(function () {\n                    var $item = $(this),\n                        key = $item.data('contextMenuKey'),\n                        item = opt.items[key],\n                        disabled = ($.isFunction(item.disabled) && item.disabled.call($trigger, key, root)) || item.disabled === true,\n                        visible;\n                    if ($.isFunction(item.visible)) {\n                        visible = item.visible.call($trigger, key, root);\n                    } else if (typeof item.visible !== 'undefined') {\n                        visible = item.visible === true;\n                    } else {\n                        visible = true;\n                    }\n                    $item[visible ? 'show' : 'hide']();\n\n                    // dis- / enable item\n                    $item[disabled ? 'addClass' : 'removeClass'](root.classNames.disabled);\n\n                    if ($.isFunction(item.icon)) {\n                        $item.removeClass(item._icon);\n                        item._icon = item.icon.call(this, $trigger, $item, key, item);\n                        $item.addClass(item._icon);\n                    }\n\n                    if (item.type) {\n                        // dis- / enable input elements\n                        $item.find('input, select, textarea').prop('disabled', disabled);\n\n                        // update input states\n                        switch (item.type) {\n                            case 'text':\n                            case 'textarea':\n                                item.$input.val(item.value || '');\n                                break;\n\n                            case 'checkbox':\n                            case 'radio':\n                                item.$input.val(item.value || '').prop('checked', !!item.selected);\n                                break;\n\n                            case 'select':\n                                item.$input.val((item.selected === 0 ? \"0\" : item.selected) || '');\n                                break;\n                        }\n                    }\n\n                    if (item.$menu) {\n                        // update sub-menu\n                        op.update.call($trigger, item, root);\n                    }\n                });\n            },\n            layer: function (opt, zIndex) {\n                // add transparent layer for click area\n                // filter and background for Internet Explorer, Issue #23\n                var $layer = opt.$layer = $('<div id=\"context-menu-layer\"></div>')\n                    .css({\n                        height: $win.height(),\n                        width: $win.width(),\n                        display: 'block',\n                        position: 'fixed',\n                        'z-index': zIndex,\n                        top: 0,\n                        left: 0,\n                        opacity: 0,\n                        filter: 'alpha(opacity=0)',\n                        'background-color': '#000'\n                    })\n                    .data('contextMenuRoot', opt)\n                    .insertBefore(this)\n                    .on('contextmenu', handle.abortevent)\n                    .on('mousedown', handle.layerClick);\n\n                // IE6 doesn't know position:fixed;\n                if (typeof document.body.style.maxWidth === 'undefined') { // IE6 doesn't support maxWidth\n                    $layer.css({\n                        'position': 'absolute',\n                        'height': $(document).height()\n                    });\n                }\n\n                return $layer;\n            },\n            processPromises: function (opt, root, promise) {\n                // Start\n                opt.$node.addClass(root.classNames.iconLoadingClass);\n\n                function completedPromise(opt, root, items) {\n                    // Completed promise (dev called promise.resolve). We now have a list of items which can\n                    // be used to create the rest of the context menu.\n                    if (typeof items === 'undefined') {\n                        // Null result, dev should have checked\n                        errorPromise(undefined);//own error object\n                    }\n                    finishPromiseProcess(opt, root, items);\n                }\n\n                function errorPromise(opt, root, errorItem) {\n                    // User called promise.reject() with an error item, if not, provide own error item.\n                    if (typeof errorItem === 'undefined') {\n                        errorItem = {\n                            \"error\": {\n                                name: \"No items and no error item\",\n                                icon: \"context-menu-icon context-menu-icon-quit\"\n                            }\n                        };\n                        if (window.console) {\n                            (console.error || console.log).call(console, 'When you reject a promise, provide an \"items\" object, equal to normal sub-menu items');\n                        }\n                    } else if (typeof errorItem === 'string') {\n                        errorItem = {\"error\": {name: errorItem}};\n                    }\n                    finishPromiseProcess(opt, root, errorItem);\n                }\n\n                function finishPromiseProcess(opt, root, items) {\n                    if (typeof root.$menu === 'undefined' || !root.$menu.is(':visible')) {\n                        return;\n                    }\n                    opt.$node.removeClass(root.classNames.iconLoadingClass);\n                    opt.items = items;\n                    op.create(opt, root, true); // Create submenu\n                    op.update(opt, root); // Correctly update position if user is already hovered over menu item\n                    root.positionSubmenu.call(opt.$node, opt.$menu); // positionSubmenu, will only do anything if user already hovered over menu item that just got new subitems.\n                }\n\n                // Wait for promise completion. .then(success, error, notify) (we don't track notify). Bind the opt\n                // and root to avoid scope problems\n                promise.then(completedPromise.bind(this, opt, root), errorPromise.bind(this, opt, root));\n            },\n            // operation that will run after contextMenu showed on screen\n            activated: function(opt){\n                var $menu = opt.$menu;\n                var $menuOffset = $menu.offset();\n                var winHeight = $(window).height();\n                var winScrollTop = $(window).scrollTop();\n                var menuHeight = $menu.height();\n                if(menuHeight > winHeight){\n                    $menu.css({\n                        'height' : winHeight + 'px',\n                        'overflow-x': 'hidden',\n                        'overflow-y': 'auto',\n                        'top': winScrollTop + 'px'\n                    });\n                } else if(($menuOffset.top < winScrollTop) || ($menuOffset.top + menuHeight > winScrollTop + winHeight)){\n                    $menu.css({\n                        'top': '0px'\n                    });\n                } \n            }\n        };\n\n    // split accesskey according to http://www.whatwg.org/specs/web-apps/current-work/multipage/editing.html#assigned-access-key\n    function splitAccesskey(val) {\n        var t = val.split(/\\s+/);\n        var keys = [];\n\n        for (var i = 0, k; k = t[i]; i++) {\n            k = k.charAt(0).toUpperCase(); // first character only\n            // theoretically non-accessible characters should be ignored, but different systems, different keyboard layouts, ... screw it.\n            // a map to look up already used access keys would be nice\n            keys.push(k);\n        }\n\n        return keys;\n    }\n\n// handle contextMenu triggers\n    $.fn.contextMenu = function (operation) {\n        var $t = this, $o = operation;\n        if (this.length > 0) {  // this is not a build on demand menu\n            if (typeof operation === 'undefined') {\n                this.first().trigger('contextmenu');\n            } else if (typeof operation.x !== 'undefined' && typeof operation.y !== 'undefined') {\n                this.first().trigger($.Event('contextmenu', {\n                    pageX: operation.x,\n                    pageY: operation.y,\n                    mouseButton: operation.button\n                }));\n            } else if (operation === 'hide') {\n                var $menu = this.first().data('contextMenu') ? this.first().data('contextMenu').$menu : null;\n                if ($menu) {\n                    $menu.trigger('contextmenu:hide');\n                }\n            } else if (operation === 'destroy') {\n                $.contextMenu('destroy', {context: this});\n            } else if ($.isPlainObject(operation)) {\n                operation.context = this;\n                $.contextMenu('create', operation);\n            } else if (operation) {\n                this.removeClass('context-menu-disabled');\n            } else if (!operation) {\n                this.addClass('context-menu-disabled');\n            }\n        } else {\n            $.each(menus, function () {\n                if (this.selector === $t.selector) {\n                    $o.data = this;\n\n                    $.extend($o.data, {trigger: 'demand'});\n                }\n            });\n\n            handle.contextmenu.call($o.target, $o);\n        }\n\n        return this;\n    };\n\n    // manage contextMenu instances\n    $.contextMenu = function (operation, options) {\n        if (typeof operation !== 'string') {\n            options = operation;\n            operation = 'create';\n        }\n\n        if (typeof options === 'string') {\n            options = {selector: options};\n        } else if (typeof options === 'undefined') {\n            options = {};\n        }\n\n        // merge with default options\n        var o = $.extend(true, {}, defaults, options || {});\n        var $document = $(document);\n        var $context = $document;\n        var _hasContext = false;\n\n        if (!o.context || !o.context.length) {\n            o.context = document;\n        } else {\n            // you never know what they throw at you...\n            $context = $(o.context).first();\n            o.context = $context.get(0);\n            _hasContext = !$(o.context).is(document);\n        }\n\n        switch (operation) {\n\n            case 'update':\n                // Updates visibility and such\n                if(_hasContext){\n                    op.update($context);\n                } else {\n                    for(var menu in menus){\n                        if(menus.hasOwnProperty(menu)){\n                            op.update(menus[menu]);\n                        }\n                    }\n                }\n                break;\n\n            case 'create':\n                // no selector no joy\n                if (!o.selector) {\n                    throw new Error('No selector specified');\n                }\n                // make sure internal classes are not bound to\n                if (o.selector.match(/.context-menu-(list|item|input)($|\\s)/)) {\n                    throw new Error('Cannot bind to selector \"' + o.selector + '\" as it contains a reserved className');\n                }\n                if (!o.build && (!o.items || $.isEmptyObject(o.items))) {\n                    throw new Error('No Items specified');\n                }\n                counter++;\n                o.ns = '.contextMenu' + counter;\n                if (!_hasContext) {\n                    namespaces[o.selector] = o.ns;\n                }\n                menus[o.ns] = o;\n\n                // default to right click\n                if (!o.trigger) {\n                    o.trigger = 'right';\n                }\n\n                if (!initialized) {\n                    var itemClick = o.itemClickEvent === 'click' ? 'click.contextMenu' : 'mouseup.contextMenu';\n                    var contextMenuItemObj = {\n                        // 'mouseup.contextMenu': handle.itemClick,\n                        // 'click.contextMenu': handle.itemClick,\n                        'contextmenu:focus.contextMenu': handle.focusItem,\n                        'contextmenu:blur.contextMenu': handle.blurItem,\n                        'contextmenu.contextMenu': handle.abortevent,\n                        'mouseenter.contextMenu': handle.itemMouseenter,\n                        'mouseleave.contextMenu': handle.itemMouseleave\n                    };\n                    contextMenuItemObj[itemClick] = handle.itemClick;\n                    // make sure item click is registered first\n                    $document\n                        .on({\n                            'contextmenu:hide.contextMenu': handle.hideMenu,\n                            'prevcommand.contextMenu': handle.prevItem,\n                            'nextcommand.contextMenu': handle.nextItem,\n                            'contextmenu.contextMenu': handle.abortevent,\n                            'mouseenter.contextMenu': handle.menuMouseenter,\n                            'mouseleave.contextMenu': handle.menuMouseleave\n                        }, '.context-menu-list')\n                        .on('mouseup.contextMenu', '.context-menu-input', handle.inputClick)\n                        .on(contextMenuItemObj, '.context-menu-item');\n\n                    initialized = true;\n                }\n\n                // engage native contextmenu event\n                $context\n                    .on('contextmenu' + o.ns, o.selector, o, handle.contextmenu);\n\n                if (_hasContext) {\n                    // add remove hook, just in case\n                    $context.on('remove' + o.ns, function () {\n                        $(this).contextMenu('destroy');\n                    });\n                }\n\n                switch (o.trigger) {\n                    case 'hover':\n                        $context\n                            .on('mouseenter' + o.ns, o.selector, o, handle.mouseenter)\n                            .on('mouseleave' + o.ns, o.selector, o, handle.mouseleave);\n                        break;\n\n                    case 'left':\n                        $context.on('click' + o.ns, o.selector, o, handle.click);\n                        break;\n\t\t\t\t    case 'touchstart':\n                        $context.on('touchstart' + o.ns, o.selector, o, handle.click);\n                        break;\n                    /*\n                     default:\n                     // http://www.quirksmode.org/dom/events/contextmenu.html\n                     $document\n                     .on('mousedown' + o.ns, o.selector, o, handle.mousedown)\n                     .on('mouseup' + o.ns, o.selector, o, handle.mouseup);\n                     break;\n                     */\n                }\n\n                // create menu\n                if (!o.build) {\n                    op.create(o);\n                }\n                break;\n\n            case 'destroy':\n                var $visibleMenu;\n                if (_hasContext) {\n                    // get proper options\n                    var context = o.context;\n                    $.each(menus, function (ns, o) {\n\n                        if (!o) {\n                            return true;\n                        }\n\n                        // Is this menu equest to the context called from\n                        if (!$(context).is(o.selector)) {\n                            return true;\n                        }\n\n                        $visibleMenu = $('.context-menu-list').filter(':visible');\n                        if ($visibleMenu.length && $visibleMenu.data().contextMenuRoot.$trigger.is($(o.context).find(o.selector))) {\n                            $visibleMenu.trigger('contextmenu:hide', {force: true});\n                        }\n\n                        try {\n                            if (menus[o.ns].$menu) {\n                                menus[o.ns].$menu.remove();\n                            }\n\n                            delete menus[o.ns];\n                        } catch (e) {\n                            menus[o.ns] = null;\n                        }\n\n                        $(o.context).off(o.ns);\n\n                        return true;\n                    });\n                } else if (!o.selector) {\n                    $document.off('.contextMenu .contextMenuAutoHide');\n                    $.each(menus, function (ns, o) {\n                        $(o.context).off(o.ns);\n                    });\n\n                    namespaces = {};\n                    menus = {};\n                    counter = 0;\n                    initialized = false;\n\n                    $('#context-menu-layer, .context-menu-list').remove();\n                } else if (namespaces[o.selector]) {\n                    $visibleMenu = $('.context-menu-list').filter(':visible');\n                    if ($visibleMenu.length && $visibleMenu.data().contextMenuRoot.$trigger.is(o.selector)) {\n                        $visibleMenu.trigger('contextmenu:hide', {force: true});\n                    }\n\n                    try {\n                        if (menus[namespaces[o.selector]].$menu) {\n                            menus[namespaces[o.selector]].$menu.remove();\n                        }\n\n                        delete menus[namespaces[o.selector]];\n                    } catch (e) {\n                        menus[namespaces[o.selector]] = null;\n                    }\n\n                    $document.off(namespaces[o.selector]);\n                }\n                break;\n\n            case 'html5':\n                // if <command> and <menuitem> are not handled by the browser,\n                // or options was a bool true,\n                // initialize $.contextMenu for them\n                if ((!$.support.htmlCommand && !$.support.htmlMenuitem) || (typeof options === 'boolean' && options)) {\n                    $('menu[type=\"context\"]').each(function () {\n                        if (this.id) {\n                            $.contextMenu({\n                                selector: '[contextmenu=' + this.id + ']',\n                                items: $.contextMenu.fromMenu(this)\n                            });\n                        }\n                    }).css('display', 'none');\n                }\n                break;\n\n            default:\n                throw new Error('Unknown operation \"' + operation + '\"');\n        }\n\n        return this;\n    };\n\n// import values into <input> commands\n    $.contextMenu.setInputValues = function (opt, data) {\n        if (typeof data === 'undefined') {\n            data = {};\n        }\n\n        $.each(opt.inputs, function (key, item) {\n            switch (item.type) {\n                case 'text':\n                case 'textarea':\n                    item.value = data[key] || '';\n                    break;\n\n                case 'checkbox':\n                    item.selected = data[key] ? true : false;\n                    break;\n\n                case 'radio':\n                    item.selected = (data[item.radio] || '') === item.value;\n                    break;\n\n                case 'select':\n                    item.selected = data[key] || '';\n                    break;\n            }\n        });\n    };\n\n// export values from <input> commands\n    $.contextMenu.getInputValues = function (opt, data) {\n        if (typeof data === 'undefined') {\n            data = {};\n        }\n\n        $.each(opt.inputs, function (key, item) {\n            switch (item.type) {\n                case 'text':\n                case 'textarea':\n                case 'select':\n                    data[key] = item.$input.val();\n                    break;\n\n                case 'checkbox':\n                    data[key] = item.$input.prop('checked');\n                    break;\n\n                case 'radio':\n                    if (item.$input.prop('checked')) {\n                        data[item.radio] = item.value;\n                    }\n                    break;\n            }\n        });\n\n        return data;\n    };\n\n// find <label for=\"xyz\">\n    function inputLabel(node) {\n        return (node.id && $('label[for=\"' + node.id + '\"]').val()) || node.name;\n    }\n\n// convert <menu> to items object\n    function menuChildren(items, $children, counter) {\n        if (!counter) {\n            counter = 0;\n        }\n\n        $children.each(function () {\n            var $node = $(this),\n                node = this,\n                nodeName = this.nodeName.toLowerCase(),\n                label,\n                item;\n\n            // extract <label><input>\n            if (nodeName === 'label' && $node.find('input, textarea, select').length) {\n                label = $node.text();\n                $node = $node.children().first();\n                node = $node.get(0);\n                nodeName = node.nodeName.toLowerCase();\n            }\n\n            /*\n             * <menu> accepts flow-content as children. that means <embed>, <canvas> and such are valid menu items.\n             * Not being the sadistic kind, $.contextMenu only accepts:\n             * <command>, <menuitem>, <hr>, <span>, <p> <input [text, radio, checkbox]>, <textarea>, <select> and of course <menu>.\n             * Everything else will be imported as an html node, which is not interfaced with contextMenu.\n             */\n\n            // http://www.whatwg.org/specs/web-apps/current-work/multipage/commands.html#concept-command\n            switch (nodeName) {\n                // http://www.whatwg.org/specs/web-apps/current-work/multipage/interactive-elements.html#the-menu-element\n                case 'menu':\n                    item = {name: $node.attr('label'), items: {}};\n                    counter = menuChildren(item.items, $node.children(), counter);\n                    break;\n\n                // http://www.whatwg.org/specs/web-apps/current-work/multipage/commands.html#using-the-a-element-to-define-a-command\n                case 'a':\n                // http://www.whatwg.org/specs/web-apps/current-work/multipage/commands.html#using-the-button-element-to-define-a-command\n                case 'button':\n                    item = {\n                        name: $node.text(),\n                        disabled: !!$node.attr('disabled'),\n                        callback: (function () {\n                            return function () {\n                                $node.get(0).click();\n                            };\n                        })()\n                    };\n                    break;\n\n                // http://www.whatwg.org/specs/web-apps/current-work/multipage/commands.html#using-the-command-element-to-define-a-command\n                case 'menuitem':\n                case 'command':\n                    switch ($node.attr('type')) {\n                        case undefined:\n                        case 'command':\n                        case 'menuitem':\n                            item = {\n                                name: $node.attr('label'),\n                                disabled: !!$node.attr('disabled'),\n                                icon: $node.attr('icon'),\n                                callback: (function () {\n                                    return function () {\n                                        $node.get(0).click();\n                                    };\n                                })()\n                            };\n                            break;\n\n                        case 'checkbox':\n                            item = {\n                                type: 'checkbox',\n                                disabled: !!$node.attr('disabled'),\n                                name: $node.attr('label'),\n                                selected: !!$node.attr('checked')\n                            };\n                            break;\n                        case 'radio':\n                            item = {\n                                type: 'radio',\n                                disabled: !!$node.attr('disabled'),\n                                name: $node.attr('label'),\n                                radio: $node.attr('radiogroup'),\n                                value: $node.attr('id'),\n                                selected: !!$node.attr('checked')\n                            };\n                            break;\n\n                        default:\n                            item = undefined;\n                    }\n                    break;\n\n                case 'hr':\n                    item = '-------';\n                    break;\n\n                case 'input':\n                    switch ($node.attr('type')) {\n                        case 'text':\n                            item = {\n                                type: 'text',\n                                name: label || inputLabel(node),\n                                disabled: !!$node.attr('disabled'),\n                                value: $node.val()\n                            };\n                            break;\n\n                        case 'checkbox':\n                            item = {\n                                type: 'checkbox',\n                                name: label || inputLabel(node),\n                                disabled: !!$node.attr('disabled'),\n                                selected: !!$node.attr('checked')\n                            };\n                            break;\n\n                        case 'radio':\n                            item = {\n                                type: 'radio',\n                                name: label || inputLabel(node),\n                                disabled: !!$node.attr('disabled'),\n                                radio: !!$node.attr('name'),\n                                value: $node.val(),\n                                selected: !!$node.attr('checked')\n                            };\n                            break;\n\n                        default:\n                            item = undefined;\n                            break;\n                    }\n                    break;\n\n                case 'select':\n                    item = {\n                        type: 'select',\n                        name: label || inputLabel(node),\n                        disabled: !!$node.attr('disabled'),\n                        selected: $node.val(),\n                        options: {}\n                    };\n                    $node.children().each(function () {\n                        item.options[this.value] = $(this).text();\n                    });\n                    break;\n\n                case 'textarea':\n                    item = {\n                        type: 'textarea',\n                        name: label || inputLabel(node),\n                        disabled: !!$node.attr('disabled'),\n                        value: $node.val()\n                    };\n                    break;\n\n                case 'label':\n                    break;\n\n                default:\n                    item = {type: 'html', html: $node.clone(true)};\n                    break;\n            }\n\n            if (item) {\n                counter++;\n                items['key' + counter] = item;\n            }\n        });\n\n        return counter;\n    }\n\n// convert html5 menu\n    $.contextMenu.fromMenu = function (element) {\n        var $this = $(element),\n            items = {};\n\n        menuChildren(items, $this.children());\n\n        return items;\n    };\n\n// make defaults accessible\n    $.contextMenu.defaults = defaults;\n    $.contextMenu.types = types;\n// export internal functions - undocumented, for hacking only!\n    $.contextMenu.handle = handle;\n    $.contextMenu.op = op;\n    $.contextMenu.menus = menus;\n});\n"]}
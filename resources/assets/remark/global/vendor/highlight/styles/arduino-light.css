/*

Arduino® Light Theme - <PERSON><PERSON> <<EMAIL>>

*/

.hljs {
  display: block;
  overflow-x: auto;
  padding: .5em;
  background: #fff;
}

.hljs, .hljs-subst {
  color: #434f54;
}

.hljs-keyword, .hljs-attribute, .hljs-selector-tag, .hljs-doctag, .hljs-name {
  color: #00979d;
}

.hljs-built_in, .hljs-literal, .hljs-bullet, .hljs-code, .hljs-addition {
  color: #d35400;
}

.hljs-regexp, .hljs-symbol, .hljs-variable, .hljs-template-variable, .hljs-link, .hljs-selector-attr, .hljs-selector-pseudo {
  color: #00979d;
}

.hljs-type, .hljs-string, .hljs-selector-id, .hljs-selector-class, .hljs-quote, .hljs-template-tag, .hljs-deletion {
  color: #005c5f;
}

.hljs-title, .hljs-section {
  color: #800;
  font-weight: bold;
}

.hljs-comment {
  color: rgba(149,165,166,.8);
}

.hljs-meta-keyword {
  color: #728e00;
}

.hljs-meta {
  color: #728e00;
  color: #434f54;
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: bold;
}

.hljs-function {
  color: #728e00;
}

.hljs-number {
  color: #8a7b52;
}

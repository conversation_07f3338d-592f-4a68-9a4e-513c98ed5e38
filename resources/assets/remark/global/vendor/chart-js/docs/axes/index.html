
<!DOCTYPE HTML>
<html lang="" >
    <head>
        <meta charset="UTF-8">
        <meta content="text/html; charset=utf-8" http-equiv="Content-Type">
        <title>Axes · GitBook</title>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="description" content="">
        <meta name="generator" content="GitBook 3.2.2">
        <meta name="author" content="chartjs">
        
        
    
    <link rel="stylesheet" href="../gitbook/style.css">

    
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-search-plus/search.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-highlight/website.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-fontsettings/website.css">
                
            
        

    

    
        
        <link rel="stylesheet" href="../style.css">
        
    
        
    
        
    
        
    
        
    
        
    

        
    
    
    
    <meta name="HandheldFriendly" content="true"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <link rel="apple-touch-icon-precomposed" sizes="152x152" href="../gitbook/images/apple-touch-icon-precomposed-152.png">
    <link rel="shortcut icon" href="../gitbook/images/favicon.ico" type="image/x-icon">

    
    <link rel="next" href="cartesian/" />
    
    
    <link rel="prev" href="../charts/mixed.html" />
    

    <link rel="stylesheet" href="../gitbook/gitbook-plugin-chartjs/style.css">
    <script src="../gitbook/gitbook-plugin-chartjs/Chart.bundle.js"></script>
    <script src="../gitbook/gitbook-plugin-chartjs/chartjs-plugin-deferred.js"></script>
    

    </head>
    <body>
        
<div class="book">
    <div class="book-summary">
        
            
<div id="book-search-input" role="search">
    <input type="text" placeholder="Type to search" />
</div>

            
                <nav role="navigation">
                


<ul class="summary">
    
    

    

    
        
        
    
        <li class="chapter " data-level="1.1" data-path="../">
            
                <a href="../">
            
                    
                    Chart.js
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2" data-path="../getting-started/">
            
                <a href="../getting-started/">
            
                    
                    Getting Started
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.2.1" data-path="../getting-started/installation.html">
            
                <a href="../getting-started/installation.html">
            
                    
                    Installation
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.2" data-path="../getting-started/integration.html">
            
                <a href="../getting-started/integration.html">
            
                    
                    Integration
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.3" data-path="../getting-started/usage.html">
            
                <a href="../getting-started/usage.html">
            
                    
                    Usage
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.3" data-path="../general/">
            
                <a href="../general/">
            
                    
                    General
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.3.1" data-path="../general/responsive.html">
            
                <a href="../general/responsive.html">
            
                    
                    Responsive
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2" data-path="../general/interactions/">
            
                <a href="../general/interactions/">
            
                    
                    Interactions
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.3.2.1" data-path="../general/interactions/events.html">
            
                <a href="../general/interactions/events.html">
            
                    
                    Events
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.2" data-path="../general/interactions/modes.html">
            
                <a href="../general/interactions/modes.html">
            
                    
                    Modes
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.3.3" data-path="../general/options.html">
            
                <a href="../general/options.html">
            
                    
                    Options
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.4" data-path="../general/colors.html">
            
                <a href="../general/colors.html">
            
                    
                    Colors
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.5" data-path="../general/fonts.html">
            
                <a href="../general/fonts.html">
            
                    
                    Fonts
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.4" data-path="../configuration/">
            
                <a href="../configuration/">
            
                    
                    Configuration
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.4.1" data-path="../configuration/animations.html">
            
                <a href="../configuration/animations.html">
            
                    
                    Animations
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.2" data-path="../configuration/layout.html">
            
                <a href="../configuration/layout.html">
            
                    
                    Layout
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.3" data-path="../configuration/legend.html">
            
                <a href="../configuration/legend.html">
            
                    
                    Legend
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.4" data-path="../configuration/title.html">
            
                <a href="../configuration/title.html">
            
                    
                    Title
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.5" data-path="../configuration/tooltip.html">
            
                <a href="../configuration/tooltip.html">
            
                    
                    Tooltip
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.6" data-path="../configuration/elements.html">
            
                <a href="../configuration/elements.html">
            
                    
                    Elements
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.5" data-path="../charts/">
            
                <a href="../charts/">
            
                    
                    Charts
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.5.1" data-path="../charts/line.html">
            
                <a href="../charts/line.html">
            
                    
                    Line
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.2" data-path="../charts/bar.html">
            
                <a href="../charts/bar.html">
            
                    
                    Bar
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.3" data-path="../charts/radar.html">
            
                <a href="../charts/radar.html">
            
                    
                    Radar
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.4" data-path="../charts/doughnut.html">
            
                <a href="../charts/doughnut.html">
            
                    
                    Doughnut & Pie
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.5" data-path="../charts/polar.html">
            
                <a href="../charts/polar.html">
            
                    
                    Polar Area
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.6" data-path="../charts/bubble.html">
            
                <a href="../charts/bubble.html">
            
                    
                    Bubble
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.7" data-path="../charts/scatter.html">
            
                <a href="../charts/scatter.html">
            
                    
                    Scatter
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.8" data-path="../charts/area.html">
            
                <a href="../charts/area.html">
            
                    
                    Area
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.9" data-path="../charts/mixed.html">
            
                <a href="../charts/mixed.html">
            
                    
                    Mixed
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter active" data-level="1.6" data-path="./">
            
                <a href="./">
            
                    
                    Axes
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.6.1" data-path="cartesian/">
            
                <a href="cartesian/">
            
                    
                    Cartesian
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="*******" data-path="cartesian/category.html">
            
                <a href="cartesian/category.html">
            
                    
                    Category
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="*******" data-path="cartesian/linear.html">
            
                <a href="cartesian/linear.html">
            
                    
                    Linear
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="*******" data-path="cartesian/logarithmic.html">
            
                <a href="cartesian/logarithmic.html">
            
                    
                    Logarithmic
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="*******" data-path="cartesian/time.html">
            
                <a href="cartesian/time.html">
            
                    
                    Time
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.6.2" data-path="radial/">
            
                <a href="radial/">
            
                    
                    Radial
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.6.2.1" data-path="radial/linear.html">
            
                <a href="radial/linear.html">
            
                    
                    Linear
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.6.3" data-path="labelling.html">
            
                <a href="labelling.html">
            
                    
                    Labelling
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.4" data-path="styling.html">
            
                <a href="styling.html">
            
                    
                    Styling
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.7" data-path="../developers/">
            
                <a href="../developers/">
            
                    
                    Developers
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.7.1" data-path="../developers/api.html">
            
                <a href="../developers/api.html">
            
                    
                    Chart.js API
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.2" data-path="../developers/updates.html">
            
                <a href="../developers/updates.html">
            
                    
                    Updating Charts
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.3" data-path="../developers/plugins.html">
            
                <a href="../developers/plugins.html">
            
                    
                    Plugins
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.4" data-path="../developers/charts.html">
            
                <a href="../developers/charts.html">
            
                    
                    New Charts
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.5" data-path="../developers/axes.html">
            
                <a href="../developers/axes.html">
            
                    
                    New Axes
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.6" data-path="../developers/contributing.html">
            
                <a href="../developers/contributing.html">
            
                    
                    Contributing
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.8" data-path="../notes/">
            
                <a href="../notes/">
            
                    
                    Additional Notes
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.8.1" data-path="../notes/comparison.html">
            
                <a href="../notes/comparison.html">
            
                    
                    Comparison Table
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.8.2" data-path="../notes/extensions.html">
            
                <a href="../notes/extensions.html">
            
                    
                    Popular Extensions
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.8.3" data-path="../notes/license.html">
            
                <a href="../notes/license.html">
            
                    
                    License
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    

    

    <li class="divider"></li>

    <li>
        <a href="https://www.gitbook.com" target="blank" class="gitbook-link">
            Published with GitBook
        </a>
    </li>
</ul>


                </nav>
            
        
    </div>

    <div class="book-body">
        
            <div class="body-inner">
                
                    

<div class="book-header" role="navigation">
    

    <!-- Title -->
    <h1>
        <i class="fa fa-circle-o-notch fa-spin"></i>
        <a href=".." >Axes</a>
    </h1>
</div>




                    <div class="page-wrapper" tabindex="-1" role="main">
                        <div class="page-inner">
                            
<div class="search-plus" id="book-search-results">
    <div class="search-noresults">
    
                                <section class="normal markdown-section">
                                
                                <h1 id="axes">Axes</h1>
<p>Axes are an integral part of a chart. They are used to determine how data maps to a pixel value on the chart. In a cartesian chart, there is 1 or more X axis and 1 or more Y axis to map points onto the 2 dimensional canvas. These axes are know as <a href="cartesian/#cartesian-axes">&apos;cartesian axes&apos;</a>.</p>
<p>In a radial chart, such as a radar chart or a polar area chart, there is a single axis that maps points in the angular and radial directions. These are known as <a href="radial/#radial-axes">&apos;radial axes&apos;</a>.</p>
<p>Scales in Chart.js &gt;V2.0 are significantly more powerful, but also different than those of v1.0.</p>
<ul>
<li>Multiple X &amp; Y axes are supported.</li>
<li>A built-in label auto-skip feature detects would-be overlapping ticks and labels and removes every nth label to keep things displaying normally.</li>
<li>Scale titles are supported</li>
<li>New scale types can be extended without writing an entirely new chart type</li>
</ul>
<h1 id="common-configuration">Common Configuration</h1>
<p>The following properties are common to all axes provided by Chart.js</p>
<table>
<thead>
<tr>
<th>Name</th>
<th>Type</th>
<th>Default</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>display</code></td>
<td><code>Boolean</code></td>
<td><code>true</code></td>
<td>If set to <code>false</code> the axis is hidden from view. Overrides <em>gridLines.display</em>, <em>scaleLabel.display</em>, and <em>ticks.display</em>.</td>
</tr>
<tr>
<td><code>callbacks</code></td>
<td><code>Object</code></td>
<td></td>
<td>Callback functions to hook into the axis lifecycle. <a href="#callbacks">more...</a></td>
</tr>
<tr>
<td><code>weight</code></td>
<td><code>Number</code></td>
<td><code>0</code></td>
<td>The weight used to sort the axis. Higher weights are further away from the chart area.</td>
</tr>
</tbody>
</table>
<h2 id="callbacks">Callbacks</h2>
<p>There are a number of config callbacks that can be used to change parameters in the scale at different points in the update process.</p>
<table>
<thead>
<tr>
<th>Name</th>
<th>Arguments</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>beforeUpdate</code></td>
<td><code>axis</code></td>
<td>Callback called before the update process starts.</td>
</tr>
<tr>
<td><code>beforeSetDimensions</code></td>
<td><code>axis</code></td>
<td>Callback that runs before dimensions are set. </td>
</tr>
<tr>
<td><code>afterSetDimensions</code></td>
<td><code>axis</code></td>
<td>Callback that runs after dimensions are set.</td>
</tr>
<tr>
<td><code>beforeDataLimits</code></td>
<td><code>axis</code></td>
<td>Callback that runs before data limits are determined.</td>
</tr>
<tr>
<td><code>afterDataLimits</code></td>
<td><code>axis</code></td>
<td>Callback that runs after data limits are determined.</td>
</tr>
<tr>
<td><code>beforeBuildTicks</code></td>
<td><code>axis</code></td>
<td>Callback that runs before ticks are created.</td>
</tr>
<tr>
<td><code>afterBuildTicks</code></td>
<td><code>axis</code></td>
<td>Callback that runs after ticks are created. Useful for filtering ticks.</td>
</tr>
<tr>
<td><code>beforeTickToLabelConversion</code></td>
<td><code>axis</code></td>
<td>Callback that runs before ticks are converted into strings.</td>
</tr>
<tr>
<td><code>afterTickToLabelConversion</code></td>
<td><code>axis</code></td>
<td>Callback that runs after ticks are converted into strings. </td>
</tr>
<tr>
<td><code>beforeCalculateTickRotation</code></td>
<td><code>axis</code></td>
<td>Callback that runs before tick rotation is determined.</td>
</tr>
<tr>
<td><code>afterCalculateTickRotation</code></td>
<td><code>axis</code></td>
<td>Callback that runs after tick rotation is determined.</td>
</tr>
<tr>
<td><code>beforeFit</code></td>
<td><code>axis</code></td>
<td>Callback that runs before the scale fits to the canvas. </td>
</tr>
<tr>
<td><code>afterFit</code></td>
<td><code>axis</code></td>
<td>Callback that runs after the scale fits to the canvas. </td>
</tr>
<tr>
<td><code>afterUpdate</code></td>
<td><code>axis</code></td>
<td>Callback that runs at the end of the update process.</td>
</tr>
</tbody>
</table>
<h2 id="updating-axis-defaults">Updating Axis Defaults</h2>
<p>The default configuration for a scale can be easily changed using the scale service. All you need to do is to pass in a partial configuration that will be merged with the current scale default configuration to form the new default.</p>
<p>For example, to set the minimum value of 0 for all linear scales, you would do the following. Any linear scales created after this time would now have a minimum of 0.</p>
<pre><code class="lang-javascript">Chart.scaleService.updateScaleDefaults(<span class="hljs-string">&apos;linear&apos;</span>, {
    ticks: {
        min: <span class="hljs-number">0</span>
    }
});
</code></pre>
<h2 id="creating-new-axes">Creating New Axes</h2>
<p>To create a new axis, see the <a href="../developers/axes.html">developer docs</a>.</p>

                                
                                </section>
                            
    </div>
    <div class="search-results">
        <div class="has-results">
            
            <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
            <ul class="search-results-list"></ul>
            
        </div>
        <div class="no-results">
            
            <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
            
        </div>
    </div>
</div>

                        </div>
                    </div>
                
            </div>

            
                
                <a href="../charts/mixed.html" class="navigation navigation-prev " aria-label="Previous page: Mixed">
                    <i class="fa fa-angle-left"></i>
                </a>
                
                
                <a href="cartesian/" class="navigation navigation-next " aria-label="Next page: Cartesian">
                    <i class="fa fa-angle-right"></i>
                </a>
                
            
        
    </div>

    <script>
        var gitbook = gitbook || [];
        gitbook.push(function() {
            gitbook.page.hasChanged({"page":{"title":"Axes","level":"1.6","depth":1,"next":{"title":"Cartesian","level":"1.6.1","depth":2,"path":"axes/cartesian/README.md","ref":"axes/cartesian/README.md","articles":[{"title":"Category","level":"*******","depth":3,"path":"axes/cartesian/category.md","ref":"axes/cartesian/category.md","articles":[]},{"title":"Linear","level":"*******","depth":3,"path":"axes/cartesian/linear.md","ref":"axes/cartesian/linear.md","articles":[]},{"title":"Logarithmic","level":"*******","depth":3,"path":"axes/cartesian/logarithmic.md","ref":"axes/cartesian/logarithmic.md","articles":[]},{"title":"Time","level":"*******","depth":3,"path":"axes/cartesian/time.md","ref":"axes/cartesian/time.md","articles":[]}]},"previous":{"title":"Mixed","level":"1.5.9","depth":2,"path":"charts/mixed.md","ref":"charts/mixed.md","articles":[]},"dir":"ltr"},"config":{"plugins":["-lunr","-search","search-plus","anchorjs","chartjs","ga"],"root":"./docs","styles":{"website":"style.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"pluginsConfig":{"anchorjs":{"icon":"#","placement":"left","visible":"always"},"ga":{"configuration":"auto","token":"UA-28909194-3"},"theme-default":{"styles":{"website":"style.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"showLevel":false},"search-plus":{},"chartjs":{"defaults":null},"highlight":{},"sharing":{"facebook":true,"twitter":true,"google":false,"weibo":false,"instapaper":false,"vk":false,"all":["facebook","google","twitter","weibo","instapaper"]},"fontsettings":{"theme":"white","family":"sans","size":2}},"theme":"default","author":"chartjs","pdf":{"pageNumbers":true,"fontSize":12,"fontFamily":"Arial","paperSize":"a4","chapterMark":"pagebreak","pageBreaksBefore":"/","margin":{"right":62,"left":62,"top":56,"bottom":56}},"structure":{"langs":"LANGS.md","readme":"README.md","glossary":"GLOSSARY.md","summary":"SUMMARY.md"},"variables":{},"gitbook":"3.2.2"},"file":{"path":"axes/README.md","mtime":"2017-10-28T15:03:49.266Z","type":"markdown"},"gitbook":{"version":"3.2.2","time":"2017-10-28T15:09:53.587Z"},"basePath":"..","book":{"language":""}});
        });
    </script>
</div>

        
    <script src="../gitbook/gitbook.js"></script>
    <script src="../gitbook/theme.js"></script>
    
        
        <script src="../gitbook/gitbook-plugin-search-plus/jquery.mark.min.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-search-plus/search.js"></script>
        
    
        
        <script src="https://cdnjs.cloudflare.com/ajax/libs/anchor-js/3.1.1/anchor.min.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-anchorjs/anchor-style.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-ga/plugin.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-sharing/buttons.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-fontsettings/fontsettings.js"></script>
        
    

    </body>
</html>


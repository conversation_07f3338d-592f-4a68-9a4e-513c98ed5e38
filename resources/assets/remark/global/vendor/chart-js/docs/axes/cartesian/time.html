
<!DOCTYPE HTML>
<html lang="" >
    <head>
        <meta charset="UTF-8">
        <meta content="text/html; charset=utf-8" http-equiv="Content-Type">
        <title>Time · GitBook</title>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="description" content="">
        <meta name="generator" content="GitBook 3.2.2">
        <meta name="author" content="chartjs">
        
        
    
    <link rel="stylesheet" href="../../gitbook/style.css">

    
            
                
                <link rel="stylesheet" href="../../gitbook/gitbook-plugin-search-plus/search.css">
                
            
                
                <link rel="stylesheet" href="../../gitbook/gitbook-plugin-highlight/website.css">
                
            
                
                <link rel="stylesheet" href="../../gitbook/gitbook-plugin-fontsettings/website.css">
                
            
        

    

    
        
        <link rel="stylesheet" href="../../style.css">
        
    
        
    
        
    
        
    
        
    
        
    

        
    
    
    
    <meta name="HandheldFriendly" content="true"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <link rel="apple-touch-icon-precomposed" sizes="152x152" href="../../gitbook/images/apple-touch-icon-precomposed-152.png">
    <link rel="shortcut icon" href="../../gitbook/images/favicon.ico" type="image/x-icon">

    
    <link rel="next" href="../radial/" />
    
    
    <link rel="prev" href="logarithmic.html" />
    

    <link rel="stylesheet" href="../../gitbook/gitbook-plugin-chartjs/style.css">
    <script src="../../gitbook/gitbook-plugin-chartjs/Chart.bundle.js"></script>
    <script src="../../gitbook/gitbook-plugin-chartjs/chartjs-plugin-deferred.js"></script>
    

    </head>
    <body>
        
<div class="book">
    <div class="book-summary">
        
            
<div id="book-search-input" role="search">
    <input type="text" placeholder="Type to search" />
</div>

            
                <nav role="navigation">
                


<ul class="summary">
    
    

    

    
        
        
    
        <li class="chapter " data-level="1.1" data-path="../../">
            
                <a href="../../">
            
                    
                    Chart.js
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2" data-path="../../getting-started/">
            
                <a href="../../getting-started/">
            
                    
                    Getting Started
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.2.1" data-path="../../getting-started/installation.html">
            
                <a href="../../getting-started/installation.html">
            
                    
                    Installation
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.2" data-path="../../getting-started/integration.html">
            
                <a href="../../getting-started/integration.html">
            
                    
                    Integration
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.3" data-path="../../getting-started/usage.html">
            
                <a href="../../getting-started/usage.html">
            
                    
                    Usage
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.3" data-path="../../general/">
            
                <a href="../../general/">
            
                    
                    General
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.3.1" data-path="../../general/responsive.html">
            
                <a href="../../general/responsive.html">
            
                    
                    Responsive
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2" data-path="../../general/interactions/">
            
                <a href="../../general/interactions/">
            
                    
                    Interactions
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.3.2.1" data-path="../../general/interactions/events.html">
            
                <a href="../../general/interactions/events.html">
            
                    
                    Events
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.2" data-path="../../general/interactions/modes.html">
            
                <a href="../../general/interactions/modes.html">
            
                    
                    Modes
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.3.3" data-path="../../general/options.html">
            
                <a href="../../general/options.html">
            
                    
                    Options
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.4" data-path="../../general/colors.html">
            
                <a href="../../general/colors.html">
            
                    
                    Colors
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.5" data-path="../../general/fonts.html">
            
                <a href="../../general/fonts.html">
            
                    
                    Fonts
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.4" data-path="../../configuration/">
            
                <a href="../../configuration/">
            
                    
                    Configuration
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.4.1" data-path="../../configuration/animations.html">
            
                <a href="../../configuration/animations.html">
            
                    
                    Animations
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.2" data-path="../../configuration/layout.html">
            
                <a href="../../configuration/layout.html">
            
                    
                    Layout
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.3" data-path="../../configuration/legend.html">
            
                <a href="../../configuration/legend.html">
            
                    
                    Legend
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.4" data-path="../../configuration/title.html">
            
                <a href="../../configuration/title.html">
            
                    
                    Title
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.5" data-path="../../configuration/tooltip.html">
            
                <a href="../../configuration/tooltip.html">
            
                    
                    Tooltip
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.6" data-path="../../configuration/elements.html">
            
                <a href="../../configuration/elements.html">
            
                    
                    Elements
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.5" data-path="../../charts/">
            
                <a href="../../charts/">
            
                    
                    Charts
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.5.1" data-path="../../charts/line.html">
            
                <a href="../../charts/line.html">
            
                    
                    Line
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.2" data-path="../../charts/bar.html">
            
                <a href="../../charts/bar.html">
            
                    
                    Bar
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.3" data-path="../../charts/radar.html">
            
                <a href="../../charts/radar.html">
            
                    
                    Radar
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.4" data-path="../../charts/doughnut.html">
            
                <a href="../../charts/doughnut.html">
            
                    
                    Doughnut & Pie
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.5" data-path="../../charts/polar.html">
            
                <a href="../../charts/polar.html">
            
                    
                    Polar Area
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.6" data-path="../../charts/bubble.html">
            
                <a href="../../charts/bubble.html">
            
                    
                    Bubble
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.7" data-path="../../charts/scatter.html">
            
                <a href="../../charts/scatter.html">
            
                    
                    Scatter
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.8" data-path="../../charts/area.html">
            
                <a href="../../charts/area.html">
            
                    
                    Area
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.9" data-path="../../charts/mixed.html">
            
                <a href="../../charts/mixed.html">
            
                    
                    Mixed
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.6" data-path="../">
            
                <a href="../">
            
                    
                    Axes
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.6.1" data-path="./">
            
                <a href="./">
            
                    
                    Cartesian
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.6.1.1" data-path="category.html">
            
                <a href="category.html">
            
                    
                    Category
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.1.2" data-path="linear.html">
            
                <a href="linear.html">
            
                    
                    Linear
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="*******" data-path="logarithmic.html">
            
                <a href="logarithmic.html">
            
                    
                    Logarithmic
            
                </a>
            

            
        </li>
    
        <li class="chapter active" data-level="*******" data-path="time.html">
            
                <a href="time.html">
            
                    
                    Time
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.6.2" data-path="../radial/">
            
                <a href="../radial/">
            
                    
                    Radial
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="*******" data-path="../radial/linear.html">
            
                <a href="../radial/linear.html">
            
                    
                    Linear
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.6.3" data-path="../labelling.html">
            
                <a href="../labelling.html">
            
                    
                    Labelling
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.4" data-path="../styling.html">
            
                <a href="../styling.html">
            
                    
                    Styling
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.7" data-path="../../developers/">
            
                <a href="../../developers/">
            
                    
                    Developers
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.7.1" data-path="../../developers/api.html">
            
                <a href="../../developers/api.html">
            
                    
                    Chart.js API
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.2" data-path="../../developers/updates.html">
            
                <a href="../../developers/updates.html">
            
                    
                    Updating Charts
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.3" data-path="../../developers/plugins.html">
            
                <a href="../../developers/plugins.html">
            
                    
                    Plugins
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.4" data-path="../../developers/charts.html">
            
                <a href="../../developers/charts.html">
            
                    
                    New Charts
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.5" data-path="../../developers/axes.html">
            
                <a href="../../developers/axes.html">
            
                    
                    New Axes
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.6" data-path="../../developers/contributing.html">
            
                <a href="../../developers/contributing.html">
            
                    
                    Contributing
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.8" data-path="../../notes/">
            
                <a href="../../notes/">
            
                    
                    Additional Notes
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.8.1" data-path="../../notes/comparison.html">
            
                <a href="../../notes/comparison.html">
            
                    
                    Comparison Table
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.8.2" data-path="../../notes/extensions.html">
            
                <a href="../../notes/extensions.html">
            
                    
                    Popular Extensions
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.8.3" data-path="../../notes/license.html">
            
                <a href="../../notes/license.html">
            
                    
                    License
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    

    

    <li class="divider"></li>

    <li>
        <a href="https://www.gitbook.com" target="blank" class="gitbook-link">
            Published with GitBook
        </a>
    </li>
</ul>


                </nav>
            
        
    </div>

    <div class="book-body">
        
            <div class="body-inner">
                
                    

<div class="book-header" role="navigation">
    

    <!-- Title -->
    <h1>
        <i class="fa fa-circle-o-notch fa-spin"></i>
        <a href="../.." >Time</a>
    </h1>
</div>




                    <div class="page-wrapper" tabindex="-1" role="main">
                        <div class="page-inner">
                            
<div class="search-plus" id="book-search-results">
    <div class="search-noresults">
    
                                <section class="normal markdown-section">
                                
                                <h1 id="time-cartesian-axis">Time Cartesian Axis</h1>
<p>The time scale is used to display times and dates. When building its ticks, it will automatically calculate the most comfortable unit base on the size of the scale.</p>
<h2 id="data-sets">Data Sets</h2>
<h3 id="input-data">Input Data</h3>
<p>The x-axis data points may additionally be specified via the <code>t</code> attribute when using the time scale.</p>
<pre><code>data: [{
    x: new Date(),
    y: 1
}, {
    t: new Date(),
    y: 10
}]
</code></pre><h3 id="date-formats">Date Formats</h3>
<p>When providing data for the time scale, Chart.js supports all of the formats that Moment.js accepts. See <a href="http://momentjs.com/docs/#/parsing/" target="_blank">Moment.js docs</a> for details.</p>
<h2 id="configuration-options">Configuration Options</h2>
<p>The following options are provided by the time scale. You may also set options provided by the <a href="./#tick-configuration">common tick configuration</a>.</p>
<table>
<thead>
<tr>
<th>Name</th>
<th>Type</th>
<th>Default</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>distribution</code></td>
<td><code>String</code></td>
<td><code>linear</code></td>
<td>How data is plotted. <a href="#scale-distribution">more...</a></td>
</tr>
<tr>
<td><code>bounds</code></td>
<td><code>String</code></td>
<td><code>data</code></td>
<td>Determines the scale bounds. <a href="#scale-bounds">more...</a></td>
</tr>
<tr>
<td><code>ticks.source</code></td>
<td><code>String</code></td>
<td><code>auto</code></td>
<td>How ticks are generated. <a href="#ticks-source">more...</a></td>
</tr>
<tr>
<td><code>time.displayFormats</code></td>
<td><code>Object</code></td>
<td></td>
<td>Sets how different time units are displayed. <a href="#display-formats">more...</a></td>
</tr>
<tr>
<td><code>time.isoWeekday</code></td>
<td><code>Boolean</code></td>
<td><code>false</code></td>
<td>If true and the unit is set to &apos;week&apos;, then the first day of the week will be Monday. Otherwise, it will be Sunday.</td>
</tr>
<tr>
<td><code>time.max</code></td>
<td><a href="#date-formats">Time</a></td>
<td></td>
<td>If defined, this will override the data maximum</td>
</tr>
<tr>
<td><code>time.min</code></td>
<td><a href="#date-formats">Time</a></td>
<td></td>
<td>If defined, this will override the data minimum</td>
</tr>
<tr>
<td><code>time.parser</code></td>
<td><code>String/Function</code></td>
<td></td>
<td>Custom parser for dates. <a href="#parser">more...</a></td>
</tr>
<tr>
<td><code>time.round</code></td>
<td><code>String</code></td>
<td><code>false</code></td>
<td>If defined, dates will be rounded to the start of this unit. See <a href="#time-units">Time Units</a> below for the allowed units.</td>
</tr>
<tr>
<td><code>time.tooltipFormat</code></td>
<td><code>String</code></td>
<td></td>
<td>The moment js format string to use for the tooltip.</td>
</tr>
<tr>
<td><code>time.unit</code></td>
<td><code>String</code></td>
<td><code>false</code></td>
<td>If defined, will force the unit to be a certain type. See <a href="#time-units">Time Units</a> section below for details.</td>
</tr>
<tr>
<td><code>time.stepSize</code></td>
<td><code>Number</code></td>
<td><code>1</code></td>
<td>The number of units between grid lines.</td>
</tr>
<tr>
<td><code>time.minUnit</code></td>
<td><code>String</code></td>
<td><code>&apos;millisecond&apos;</code></td>
<td>The minimum display format to be used for a time unit.</td>
</tr>
</tbody>
</table>
<h3 id="time-units">Time Units</h3>
<p>The following time measurements are supported. The names can be passed as strings to the <code>time.unit</code> config option to force a certain unit.</p>
<ul>
<li>millisecond</li>
<li>second</li>
<li>minute</li>
<li>hour</li>
<li>day</li>
<li>week</li>
<li>month</li>
<li>quarter</li>
<li>year</li>
</ul>
<p>For example, to create a chart with a time scale that always displayed units per month, the following config could be used.</p>
<pre><code class="lang-javascript"><span class="hljs-keyword">var</span> chart = <span class="hljs-keyword">new</span> Chart(ctx, {
    type: <span class="hljs-string">&apos;line&apos;</span>,
    data: data,
    options: {
        scales: {
            xAxes: [{
                time: {
                    unit: <span class="hljs-string">&apos;month&apos;</span>
                }
            }]
        }
    }
})
</code></pre>
<h3 id="display-formats">Display Formats</h3>
<p>The following display formats are used to configure how different time units are formed into strings for the axis tick marks. See <a href="http://momentjs.com/docs/#/displaying/format/" target="_blank">moment.js</a> for the allowable format strings.</p>
<table>
<thead>
<tr>
<th>Name</th>
<th>Default</th>
<th>Example</th>
</tr>
</thead>
<tbody>
<tr>
<td>millisecond</td>
<td>&apos;h:mm:ss.SSS a&apos;</td>
<td>11:20:01.123 AM</td>
</tr>
<tr>
<td>second</td>
<td>&apos;h:mm:ss a&apos;</td>
<td>11:20:01 AM</td>
</tr>
<tr>
<td>minute</td>
<td>&apos;h:mm a&apos;</td>
<td>11:20 AM</td>
</tr>
<tr>
<td>hour</td>
<td>&apos;hA&apos;</td>
<td>11AM</td>
</tr>
<tr>
<td>day</td>
<td>&apos;MMM D&apos;</td>
<td>Sep 4</td>
</tr>
<tr>
<td>week</td>
<td>&apos;ll&apos;</td>
<td>Sep 4 2015</td>
</tr>
<tr>
<td>month</td>
<td>&apos;MMM YYYY&apos;</td>
<td>Sep 2015</td>
</tr>
<tr>
<td>quarter</td>
<td>&apos;[Q]Q - YYYY&apos;</td>
<td>Q3 - 2015</td>
</tr>
<tr>
<td>year</td>
<td>&apos;YYYY&apos;</td>
<td>2015</td>
</tr>
</tbody>
</table>
<p>For example, to set the display format for the &apos;quarter&apos; unit to show the month and year, the following config would be passed to the chart constructor.</p>
<pre><code class="lang-javascript"><span class="hljs-keyword">var</span> chart = <span class="hljs-keyword">new</span> Chart(ctx, {
    type: <span class="hljs-string">&apos;line&apos;</span>,
    data: data,
    options: {
        scales: {
            xAxes: [{
                type: <span class="hljs-string">&apos;time&apos;</span>,
                time: {
                    displayFormats: {
                        quarter: <span class="hljs-string">&apos;MMM YYYY&apos;</span>
                    }
                }
            }]
        }
    }
})
</code></pre>
<h3 id="scale-distribution">Scale Distribution</h3>
<p>The <code>distribution</code> property controls the data distribution along the scale:</p>
<ul>
<li><code>&apos;linear&apos;</code>: data are spread according to their time (distances can vary)</li>
<li><code>&apos;series&apos;</code>: data are spread at the same distance from each other</li>
</ul>
<pre><code class="lang-javascript"><span class="hljs-keyword">var</span> chart = <span class="hljs-keyword">new</span> Chart(ctx, {
    type: <span class="hljs-string">&apos;line&apos;</span>,
    data: data,
    options: {
        scales: {
            xAxes: [{
                type: <span class="hljs-string">&apos;time&apos;</span>,
                distribution: <span class="hljs-string">&apos;series&apos;</span>
            }]
        }
    }
})
</code></pre>
<h3 id="scale-bounds">Scale Bounds</h3>
<p>The <code>bounds</code> property controls the scale boundary strategy (bypassed by min/max time options)</p>
<ul>
<li><code>&apos;data&apos;</code>: make sure data are fully visible, labels outside are removed</li>
<li><code>&apos;ticks&apos;</code>: make sure ticks are fully visible, data outside are truncated</li>
</ul>
<h3 id="ticks-source">Ticks Source</h3>
<p>The <code>ticks.source</code> property controls the ticks generation</p>
<ul>
<li><code>&apos;auto&apos;</code>: generates &quot;optimal&quot; ticks based on scale size and time options.</li>
<li><code>&apos;data&apos;</code>: generates ticks from data (including labels from data <code>{t|x|y}</code> objects)</li>
<li><code>&apos;labels&apos;</code>: generates ticks from user given <code>data.labels</code> values ONLY</li>
</ul>
<h3 id="parser">Parser</h3>
<p>If this property is defined as a string, it is interpreted as a custom format to be used by moment to parse the date. </p>
<p>If this is a function, it must return a moment.js object given the appropriate data value.</p>

                                
                                </section>
                            
    </div>
    <div class="search-results">
        <div class="has-results">
            
            <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
            <ul class="search-results-list"></ul>
            
        </div>
        <div class="no-results">
            
            <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
            
        </div>
    </div>
</div>

                        </div>
                    </div>
                
            </div>

            
                
                <a href="logarithmic.html" class="navigation navigation-prev " aria-label="Previous page: Logarithmic">
                    <i class="fa fa-angle-left"></i>
                </a>
                
                
                <a href="../radial/" class="navigation navigation-next " aria-label="Next page: Radial">
                    <i class="fa fa-angle-right"></i>
                </a>
                
            
        
    </div>

    <script>
        var gitbook = gitbook || [];
        gitbook.push(function() {
            gitbook.page.hasChanged({"page":{"title":"Time","level":"*******","depth":3,"next":{"title":"Radial","level":"1.6.2","depth":2,"path":"axes/radial/README.md","ref":"axes/radial/README.md","articles":[{"title":"Linear","level":"*******","depth":3,"path":"axes/radial/linear.md","ref":"axes/radial/linear.md","articles":[]}]},"previous":{"title":"Logarithmic","level":"*******","depth":3,"path":"axes/cartesian/logarithmic.md","ref":"axes/cartesian/logarithmic.md","articles":[]},"dir":"ltr"},"config":{"plugins":["-lunr","-search","search-plus","anchorjs","chartjs","ga"],"root":"./docs","styles":{"website":"style.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"pluginsConfig":{"anchorjs":{"icon":"#","placement":"left","visible":"always"},"ga":{"configuration":"auto","token":"UA-28909194-3"},"theme-default":{"styles":{"website":"style.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"showLevel":false},"search-plus":{},"chartjs":{"defaults":null},"highlight":{},"sharing":{"facebook":true,"twitter":true,"google":false,"weibo":false,"instapaper":false,"vk":false,"all":["facebook","google","twitter","weibo","instapaper"]},"fontsettings":{"theme":"white","family":"sans","size":2}},"theme":"default","author":"chartjs","pdf":{"pageNumbers":true,"fontSize":12,"fontFamily":"Arial","paperSize":"a4","chapterMark":"pagebreak","pageBreaksBefore":"/","margin":{"right":62,"left":62,"top":56,"bottom":56}},"structure":{"langs":"LANGS.md","readme":"README.md","glossary":"GLOSSARY.md","summary":"SUMMARY.md"},"variables":{},"gitbook":"3.2.2"},"file":{"path":"axes/cartesian/time.md","mtime":"2017-10-28T15:03:49.266Z","type":"markdown"},"gitbook":{"version":"3.2.2","time":"2017-10-28T15:09:53.587Z"},"basePath":"../..","book":{"language":""}});
        });
    </script>
</div>

        
    <script src="../../gitbook/gitbook.js"></script>
    <script src="../../gitbook/theme.js"></script>
    
        
        <script src="../../gitbook/gitbook-plugin-search-plus/jquery.mark.min.js"></script>
        
    
        
        <script src="../../gitbook/gitbook-plugin-search-plus/search.js"></script>
        
    
        
        <script src="https://cdnjs.cloudflare.com/ajax/libs/anchor-js/3.1.1/anchor.min.js"></script>
        
    
        
        <script src="../../gitbook/gitbook-plugin-anchorjs/anchor-style.js"></script>
        
    
        
        <script src="../../gitbook/gitbook-plugin-ga/plugin.js"></script>
        
    
        
        <script src="../../gitbook/gitbook-plugin-sharing/buttons.js"></script>
        
    
        
        <script src="../../gitbook/gitbook-plugin-fontsettings/fontsettings.js"></script>
        
    

    </body>
</html>


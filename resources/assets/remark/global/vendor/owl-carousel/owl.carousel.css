@charset "utf-8";
/*
 *  Owl Carousel - Core
 */
.owl-carousel {
  /* position relative and z-index fix webkit rendering fonts issue */
  position: relative;
  z-index: 1;
  display: none;
  width: 100%;
  -webkit-tap-highlight-color: transparent;
}

.owl-carousel .owl-stage {
  position: relative;
  -ms-touch-action: pan-Y;
}

.owl-carousel .owl-stage:after {
  display: block;
  height: 0;
  clear: both;
  line-height: 0;
  visibility: hidden;
  content: ".";
}

.owl-carousel .owl-stage-outer {
  position: relative;
  overflow: hidden;
  /* fix for flashing background */
  -webkit-transform: translate3d(0px, 0px, 0px);
}

.owl-carousel .owl-item {
  position: relative;
  float: left;
  min-height: 1px;
  -webkit-backface-visibility: hidden;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}

.owl-carousel .owl-item img {
  display: block;
  width: 100%;
  -webkit-transform-style: preserve-3d;
}

.owl-carousel .owl-nav.disabled, .owl-carousel .owl-dots.disabled {
  display: none;
}

.owl-carousel .owl-nav .owl-prev, .owl-carousel .owl-nav .owl-next, .owl-carousel .owl-dot {
  cursor: pointer;
  cursor: hand;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.owl-carousel.owl-loaded {
  display: block;
}

.owl-carousel.owl-loading {
  display: block;
  opacity: 0;
}

.owl-carousel.owl-hidden {
  opacity: 0;
}

.owl-carousel.owl-refresh .owl-item {
  display: none;
}

.owl-carousel.owl-drag .owl-item {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.owl-carousel.owl-grab {
  cursor: move;
  cursor: -webkit-grab;
  cursor: grab;
}

.owl-carousel.owl-rtl {
  direction: rtl;
}

.owl-carousel.owl-rtl .owl-item {
  float: right;
}

/* No Js */
.no-js .owl-carousel {
  display: block;
}

/*
 *  Owl Carousel - Animate Plugin
 */
.owl-carousel .animated {
  -webkit-animation-duration: 1000ms;
  animation-duration: 1000ms;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

.owl-carousel .owl-animated-in {
  z-index: 0;
}

.owl-carousel .owl-animated-out {
  z-index: 1;
}

.owl-carousel .fadeOut {
  -webkit-animation-name: fadeOut;
  animation-name: fadeOut;
}

@-webkit-keyframes fadeOut {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

@keyframes fadeOut {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

/*
 * 	Owl Carousel - Auto Height Plugin
 */
.owl-height {
  transition: height 500ms ease-in-out;
}

/*
 * 	Owl Carousel - Lazy Load Plugin
 */
.owl-carousel .owl-item .owl-lazy {
  opacity: 0;
  transition: opacity 400ms ease;
}

.owl-carousel .owl-item img.owl-lazy {
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
}

/*
 * 	Owl Carousel - Video Plugin
 */
.owl-carousel .owl-video-wrapper {
  position: relative;
  height: 100%;
  background: #000;
}

.owl-carousel .owl-video-play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 1;
  width: 80px;
  height: 80px;
  margin-top: -40px;
  margin-left: -40px;
  cursor: pointer;
  background: url("owl.video.play.png") no-repeat;
  transition: -webkit-transform 100ms ease;
  transition: transform 100ms ease;
  transition: transform 100ms ease, -webkit-transform 100ms ease;
  -webkit-backface-visibility: hidden;
}

.owl-carousel .owl-video-play-icon:hover {
  -webkit-transform: scale(1.3, 1.3);
  transform: scale(1.3, 1.3);
}

.owl-carousel .owl-video-playing .owl-video-tn, .owl-carousel .owl-video-playing .owl-video-play-icon {
  display: none;
}

.owl-carousel .owl-video-tn {
  height: 100%;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: contain;
  opacity: 0;
  transition: opacity 400ms ease;
}

.owl-carousel .owl-video-frame {
  position: relative;
  z-index: 1;
  width: 100%;
  height: 100%;
}

/*
 *  Default theme - Owl Carousel CSS File
 */
.owl-carousel.owl-carousel-navOut .owl-nav [class*='owl-'] {
  margin-top: -15px;
  background-color: #a3afb7;
}

.owl-carousel.owl-carousel-navOut .owl-nav [class*='owl-']:before, .owl-carousel.owl-carousel-navOut .owl-nav [class*='owl-']:after {
  font-size: 15px;
}

.owl-carousel .owl-nav [class*='owl-'] {
  position: absolute;
  top: 50%;
  display: inline-block;
  width: 30px;
  height: 30px;
  font-family: "Web Icons";
  font-size: 0;
  font-style: normal;
  font-weight: normal;
  line-height: 30px;
  color: #fff;
  text-align: center;
  border-radius: 50px;
  opacity: .6;
  -webkit-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  text-rendering: auto;
  speak: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.owl-carousel .owl-nav [class*='owl-']:before {
  font-size: 20px;
}

.owl-carousel .owl-nav [class*='owl-']:hover {
  opacity: .8;
}

.owl-carousel .owl-nav .owl-prev {
  left: 10px;
}

.owl-carousel .owl-nav .owl-prev:before {
  content: "";
}

.owl-carousel .owl-nav .owl-next {
  right: 10px;
}

.owl-carousel .owl-nav .owl-next:before {
  content: "";
}

.owl-carousel .owl-dots {
  padding: 0;
  margin-top: 15px;
  line-height: 1;
  text-align: center;
  list-style: none;
}

.owl-carousel .owl-dots .owl-dot {
  position: relative;
  display: inline-block;
  width: 10px;
  height: 10px;
  margin: 0 10px;
  line-height: 10px;
  vertical-align: middle;
  list-style: none;
}

.owl-carousel .owl-dots .owl-dot > span {
  display: inline-block;
  width: 100%;
  height: 100%;
  cursor: pointer;
  background-color: #000;
  border-radius: 50%;
  opacity: .2;
}

.owl-carousel .owl-dots .owl-dot > span:focus {
  outline: none;
}

.owl-carousel .owl-dots .owl-dot:hover > span, .owl-carousel .owl-dots .owl-dot:focus > span, .owl-carousel .owl-dots .owl-dot.active > span {
  opacity: .5;
}

.owl-carousel .owl-dots-stroke .owl-dot > span {
  border: 2px solid transparent;
  border: 2px solid transparent;
  transition: border .3s ease 0s, background-color .3s ease 0s;
}

.owl-carousel .owl-dots-stroke .owl-dot.active > span {
  background-color: transparent;
  background-color: transparent;
  border-color: #000;
  opacity: .3;
  -webkit-transform: scale(1.3);
  transform: scale(1.3);
}

.owl-carousel .owl-dots-fillin .owl-dot > span {
  background-color: transparent;
  box-shadow: 0 0 0 2px #000 inset;
  transition: box-shadow .3s ease 0s;
}

.owl-carousel .owl-dots-fillin .owl-dot:hover > span, .owl-carousel .owl-dots-fillin .owl-dot:focus > span {
  box-shadow: 0 0 0 2px #000 inset;
}

.owl-carousel .owl-dots-fillin .owl-dot.active > span {
  box-shadow: 0 0 0 8px #000 inset;
}

.owl-carousel .owl-dots-fall .owl-dot:after {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  visibility: hidden;
  content: "";
  background-color: #000;
  border-radius: 50%;
  opacity: 0;
  transition: opacity .3s ease 0s, visibility 0s ease .3s, -webkit-transform .3s ease 0s;
  transition: transform .3s ease 0s, opacity .3s ease 0s, visibility 0s ease .3s;
  transition: transform .3s ease 0s, opacity .3s ease 0s, visibility 0s ease .3s, -webkit-transform .3s ease 0s;
  -webkit-transform: translate(0%, -200%);
  transform: translate(0%, -200%);
}

.owl-carousel .owl-dots-fall .owl-dot > span {
  transition: opacity .3s ease 0s, background-color .3s ease 0s, -webkit-transform .3s ease 0s;
  transition: transform .3s ease 0s, opacity .3s ease 0s, background-color .3s ease 0s;
  transition: transform .3s ease 0s, opacity .3s ease 0s, background-color .3s ease 0s, -webkit-transform .3s ease 0s;
}

.owl-carousel .owl-dots-fall .owl-dot.active:after {
  visibility: visible;
  opacity: .5;
  transition: opacity .3s ease 0s, -webkit-transform .3s ease 0s;
  transition: transform .3s ease 0s, opacity .3s ease 0s;
  transition: transform .3s ease 0s, opacity .3s ease 0s, -webkit-transform .3s ease 0s;
  -webkit-transform: translate(0%, 0%);
  transform: translate(0%, 0%);
}

.owl-carousel .owl-dots-fall .owl-dot.active > span {
  opacity: 0;
  -webkit-transform: translate(0, 200%);
  transform: translate(0, 200%);
}

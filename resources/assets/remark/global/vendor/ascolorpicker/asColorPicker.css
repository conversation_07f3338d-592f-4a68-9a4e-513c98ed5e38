@charset "utf-8";

.colorInputUi-wrap {
  position: relative;
  display: inline-block;
}

.colorInputUi_hideInput {
  display: none;
}

.colorInputUi_hideInput .colorInputUi-clear {
  display: none;
}

.colorInputUi-dropdown {
  position: absolute;
  z-index: 9999;
  display: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.colorInputUi-dropdown, .colorInputUi-dropdown * {
  box-sizing: content-box;
}

.colorInputUi-dropdown * {
  padding: 0;
  margin: 0;
}

.colorInputUi_open {
  display: block;
}

.colorInputUi-mask {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9998;
  width: 100%;
  height: 100%;
}

.colorInputUi-trigger {
  position: relative;
  display: inline-block;
  width: 2.43rem;
  height: 2.43rem;
  cursor: pointer;
  background-image: url("transparent.png");
}

.colorInputUi-trigger span {
  display: inline-block;
  width: 100%;
  height: 100%;
}

.colorInputUi-wrap .colorInputUi-trigger {
  position: absolute;
  top: 1px;
  right: 1px;
  border-radius: 0 .215rem .215rem 0;
}

.colorInputUi-input, .colorInputUi-trigger {
  vertical-align: middle;
}

.colorInputUi-clear {
  position: absolute;
  top: 50%;
  right: 2.788rem;
  display: none;
  font-family: "Web Icons";
  font-style: normal;
  font-weight: normal;
  color: #ccd5db;
  transition: color .1s ease-in;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  text-rendering: auto;
  speak: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.colorInputUi-clear, .colorInputUi-clear:hover, .colorInputUi-clear:active {
  text-decoration: none;
}

.colorInputUi-clear:after {
  content: "";
}

.colorInputUi-wrap:hover .colorInputUi-clear {
  display: inline-block;
}

.colorInputUi-preview {
  float: left;
  list-style: none;
}

.colorInputUi-preview li {
  display: inline-block;
  vertical-align: top;
  background-image: url("transparent.png");
}

.colorInputUi-preview li span {
  display: block;
  height: 100%;
}

.colorInputUi-preview-previous {
  cursor: pointer;
}

.colorInputUi-palettes ul {
  display: block;
}

.colorInputUi-palettes ul:before, .colorInputUi-palettes ul:after {
  display: table;
  content: "";
}

.colorInputUi-palettes ul:after {
  clear: both;
}

.colorInputUi-palettes li {
  display: block;
  float: left;
  overflow: hidden;
  text-indent: 100%;
  white-space: nowrap;
  cursor: pointer;
  background-image: url("transparent.png");
}

.colorInputUi-palettes li span {
  display: block;
  height: 100%;
}

.colorInputUi-saturation {
  position: relative;
  display: inline-block;
  width: 175px;
  height: 175px;
  clear: both;
  background-image: url("saturation.png");
}

.colorInputUi-saturation i {
  position: absolute;
}

.colorInputUi-hue, .colorInputUi-alpha {
  position: relative;
  display: inline-block;
  width: 20px;
  height: 175px;
  cursor: pointer;
}

.colorInputUi-hue i, .colorInputUi-alpha i {
  position: absolute;
  cursor: row-resize;
}

.colorInputUi-hue {
  background-image: url("hue.png");
}

.colorInputUi-alpha {
  background-image: url("alpha.png");
}

.colorInputUi-buttons a, .colorInputUi-gradient-control a {
  text-decoration: none;
  cursor: pointer;
}

.colorInputUi-gradient {
  display: none;
}

.colorInputUi-gradient_enable {
  display: block;
}

.colorInputUi-gradient-preview {
  float: left;
  height: 20px;
}

.colorInputUi-gradient-markers {
  position: relative;
  width: 100%;
  outline: none;
}

.colorInputUi-gradient-marker {
  position: absolute;
  outline: none;
}

.colorInputUi-gradient-wheel {
  position: relative;
  float: left;
  width: 20px;
  height: 20px;
  border: 1px solid #e4eaec;
  border-radius: 100%;
}

.colorInputUi-gradient-wheel i {
  position: absolute;
  width: 5px;
  height: 5px;
  border-radius: 100%;
}

.colorInputUi-gradient-angle {
  float: left;
}

.colorInputUi-dropdown {
  min-width: 205px;
  max-width: 235px;
  padding: 10px;
  margin: 5px 0;
  background: #fff;
  border: 1px solid #e4eaec;
  box-shadow: 0 3px 12px rgba(0, 0, 0, .05);
}

[data-mode="palettes"] .colorInputUi-dropdown {
  min-width: auto;
  max-width: auto;
}

.colorInputUi-dropdown input {
  color: #76838f;
  background-color: #fff;
  background-image: none;
  border: 1px solid #e4eaec;
  border-radius: .215rem;
  transition: box-shadow .25s linear, border .25s linear, color .25s linear, background-color .25s linear;
  -webkit-appearance: none;
}

.colorInputUi-dropdown input.focus, .colorInputUi-dropdown input:focus {
  border-color: #3e8ef7;
  box-shadow: none;
}

.colorInputUi-trigger {
  border: 4px solid #f3f7f9;
  border-left-width: 5px;
}

.colorInputUi-trigger:before {
  position: absolute;
  top: o;
  left: -1px;
  width: 1px;
  height: 100%;
  pointer-events: none;
  content: "";
  background: #e4eaec;
}

.colorInputUi-saturation {
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, .05);
}

.colorInputUi-saturation i {
  width: 5px;
  height: 5px;
  margin-top: -2px;
  margin-left: -2px;
  border: 2px solid #fff;
  border-radius: 100%;
}

.colorInputUi-hue, .colorInputUi-alpha {
  margin-left: 10px;
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, .05);
}

.colorInputUi-hue i, .colorInputUi-alpha i {
  left: -2px;
  width: 20px;
  height: 2px;
  margin-top: -2px;
  border: 2px solid #fff;
}

.colorInputUi-preview {
  position: relative;
  height: 33px;
  margin-right: 10px;
  margin-bottom: 10px;
}

.colorInputUi-preview:after {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  content: "";
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, .05);
}

.colorInputUi-preview li {
  width: 48px;
  height: 33px;
}

.colorInputUi-hex {
  width: 100px;
  border-color: rgba(0, 0, 0, .05);
}

.colorInputUi-palettes li {
  width: 21px;
  height: 15px;
  margin-right: 6px;
  margin-bottom: 3px;
}

.colorInputUi-palettes li span {
  box-sizing: border-box;
  border: 1px solid rgba(0, 0, 0, .05);
}

.colorInputUi-palettes li:nth-child(5n) {
  margin-right: 0;
}

[data-mode="palettes"] .colorInputUi-palettes li:nth-child(5n) {
  margin-right: 6px;
}

.colorInputUi-buttons, .colorInputUi-gradient-control {
  float: right;
}

.colorInputUi-buttons a, .colorInputUi-gradient-control a {
  margin-left: 5px;
}

.colorInputUi-gradient {
  padding-top: 20px;
  margin-top: 10px;
  border-top: 1px solid rgba(0, 0, 0, .05);
}

.colorInputUi-gradient-preview {
  position: relative;
  width: 160px;
  border: 1px solid rgba(0, 0, 0, .05);
}

.colorInputUi-gradient-preview:after {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
  content: "";
  background-image: url("transparent.png");
}

.colorInputUi-gradient-markers {
  top: -16px;
  display: block;
  width: 160px;
  height: 16px;
  padding: 0;
  margin: 0;
  list-style: none;
}

.colorInputUi-gradient-marker {
  width: 10px;
  height: 10px;
  margin-left: -6px;
  background: #fff;
  border: 1px solid #e4eaec;
}

.colorInputUi-gradient-marker span {
  display: block;
  width: 100%;
  height: 100%;
}

.colorInputUi-gradient-marker i {
  position: absolute;
  bottom: -3px;
  left: 2px;
  width: 4px;
  height: 4px;
  background: #fff;
  border: 1px solid transparent;
  border-right-color: rgba(0, 0, 0, .05);
  border-bottom-color: rgba(0, 0, 0, .05);
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}

.colorInputUi-gradient-marker_active {
  z-index: 1;
  border: 2px solid #3e8ef7;
}

.colorInputUi-gradient-marker_active i {
  left: 1px;
  border: 2px solid transparent;
  border-right-color: #3e8ef7;
  border-bottom-color: #3e8ef7;
}

.colorInputUi-gradient-wheel {
  margin-left: 10px;
}

.colorInputUi-gradient-wheel i {
  background-color: #a8bbc2;
}

.colorInputUi-gradient-angle {
  width: 28px;
  margin-left: 10px;
}

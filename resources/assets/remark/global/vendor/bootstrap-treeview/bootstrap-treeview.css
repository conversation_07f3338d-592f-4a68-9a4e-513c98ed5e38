.treeview .list-group-item {
  cursor: pointer;
}

.treeview .list-group-item:not(.node-selected):hover, .treeview .list-group-item:not(.node-selected):focus {
  color: #3e8ef7;
  background-color: #f3f7f9;
}

.treeview .list-group-item:not(.node-selected):hover > .node-icon, .treeview .list-group-item:not(.node-selected):focus > .node-icon {
  color: #3e8ef7;
}

.treeview span.indent {
  margin-right: 10px;
  margin-left: 10px;
}

.treeview span.icon {
  width: 12px;
  margin-right: 8px;
}

.treeview .node-disabled {
  color: silver;
  cursor: not-allowed;
}

.treeview .badge {
  -ms-flex-order: 1;
  order: 1;
  padding: 3px 6px;
  margin-left: auto;
  font-size: 12px;
  font-weight: 400;
  line-height: 1;
  color: #76838f;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  background-color: #e4eaec;
  border-radius: 10px;
}

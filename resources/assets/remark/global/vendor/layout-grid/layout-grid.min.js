var LTGrid=function(a){"use strict";var b=function(){function a(a){this.rects=a||[]}return a.prototype.getIntersectingRects=function(a){return this.rects.filter(function(b){return a!==b&&a.intersect(b)})},a.prototype.compact=function(){var a=this.rects.slice(0),b=this;return a.sort(function(a,b){return a.y-b.y}).forEach(function(a){do a.y-=1;while(a.y>=0&&0===b.getIntersectingRects(a).length);a.y+=1}),this},a.prototype.height=function(){var a=this.rects.map(function(a){return a.bottom()});return a.length?Math.max.apply(null,a):0},a.prototype.updateNoOverlap=function(a,b){var c=this;return this.update(a,b),this.getIntersectingRects(a).forEach(function(b){c.updateNoOverlap(b,{x:b.x,y:a.bottom()})}),this},a.prototype.update=function(a,b){return a.x="x"in b?b.x:a.x,a.y="y"in b?b.y:a.y,a.w="w"in b?b.w:a.w,a.h="h"in b?b.h:a.h,this},a}(),c=(function(a){var d="ltData",f={NAME:d};return f.getRects=function(b){return a.map(b,function(a){return new e(a.x,a.y,a.w,a.h)})},a.fn[d]=function(a,d){var e=this[c.NAME]().data(c.DATA_KEY);return void 0!==d?(e.grid(a,new b(f.getRects(d))).update(),this):e.grid(a).rects},f}(jQuery),function(a){function c(b,c){this.$element=a(b),this._options=this._getOptions(c||{}),this.$mask=void 0,this.$ghost=void 0}var g="ltGrid",h="lt.grid",i="."+h,j={UPDATE:"update",START:"dragstart"+i+" touchstart"+i,OVER:"dragover"+i+" touchmove"+i,END:"dragend"+i+" touchcancel"+i,DROP:"drop"+i+" touchend"+i,LEAVE:"dragleave."+i},k={GRID:'[data-arrange="lt-grid"]',WIDGET:'[data-arrange="lt-grid"] .lt'},l={resize:!0,overlap:!1,compact:!0,params:{lg:{gap:1,maxWidth:Number.MAX_VALUE,cols:4,aspect:2/3},md:{gap:2,maxWidth:1200,cols:3,aspect:2/3},sm:{gap:3,maxWidth:992,cols:2,aspect:2/3},xs:{gap:4,maxWidth:768,cols:1,aspect:2/3}}};return c.Default=l,c.NAME=g,c.DATA_KEY=h,c.EVENT_KEY=i,c.prototype.options=function(){return this._options},c.prototype.option=function(a,b){this._options[a]=b},c.prototype.size=function(){var a,b=this._windowWidth();for(var c in this._options.params)b<this._options.params[c].maxWidth&&(a=c);return a},c.prototype.compact=function(){var a=this.size(),b=this.grid(a);this.grid(a,b.compact())},c.prototype.resize=function(){var a=this.size(),b=new e(0,0,0,this.grid(a).height()),c=b.setCss(this.$element.attr("class"),a);this.$element.attr("class",c)},c.prototype.end=function(){this._removeMask(),this._removeGhost(),this.update()},c.prototype.update=function(){this._options.compact&&this.compact(),this._options.resize&&this.resize(),this.$element.trigger(j.UPDATE)},c.prototype.grid=function(c,e){var f=this.$element.children("[draggable]");return void 0!==e?(e.rects.forEach(function(a,b){f.eq(b)[d.NAME](c,a)}),this):new b(a.map(f.toArray(),function(b){return a(b)[d.NAME](c)}))},c.prototype.reposition=function(a,b){var c=this.size(),e=a[d.NAME](c),f=this.grid(c);this._options.overlap?f.update(e,b):f.updateNoOverlap(e,b),this.grid(c,f),this.update()},c.prototype._moveGhost=function(a,b,c){var e=this.size(),f=this._getGhost(a),g=f[d.NAME](e),h=this._options.params[e].gap,i=this._options.params[e].cols;g.x=Math.floor(b/(this._itemWidth(e)+h)),g.y=Math.floor(c/(this._itemHeight(e)+h)),g.x=Math.min(Math.max(0,g.x),i-g.w),f[d.NAME](e,g)},c.prototype._getGhost=function(b){return void 0===this.$ghost&&(this.$ghost=a('<div class="'+b.attr("class")+' lt-ghost"></div>'),this.$element.append(this.$ghost)),this.$ghost},c.prototype._removeGhost=function(){this.$ghost&&(this.$ghost.remove(),this.$ghost=void 0)},c.prototype._moveToGhost=function(a){var b=this.size(),c=a.parent(),e=this._getGhost(a),f=e[d.NAME](b);this.$element.append(a),this.reposition(a,{x:f.x,y:f.y}),c.add(this.$element)[g]("update")},c.prototype._getMask=function(){return void 0===this.$mask&&(this.$mask=a('<div class="lt-mask" data-lt-grid="mask"></div>'),this.$element.append(this.$mask)),this.$mask},c.prototype._removeMask=function(){void 0!==this.$mask&&(this.$mask.remove(),this.$mask=void 0)},c.prototype._itemWidth=function(a){var b=this._options.params[a].cols,c=this._options.params[a].gap;return(this.$element.width()-(b-1)*c)/b},c.prototype._itemHeight=function(a){var b=this._options.params[a].aspect;return this._itemWidth(a)*b},c.prototype._windowWidth=function(){return a(window).width()},c.prototype._getOptions=function(b){return a.extend(!0,{},l,b)},c._jQueryInterface=function(b,d,e,f){return this.each(function(){var g=a(this),i=g.data(h),j=a.extend(!0,{},l,g.data(),"object"==typeof b&&b);i||(i=new c(this,j),g.data(h,i)),"string"==typeof b&&i[b](d,e,f)})},a(document).on(j.START,k.WIDGET,function(a){f.set(a.originalEvent,this)}).on(j.OVER,k.GRID,function(b){var c=b.originalEvent,d=a(f.get(c));if(d.length){var e=c.touches?c.touches[0]:c,i=a(this),j=e.pageX-i.offset().left,k=e.pageY-i.offset().top,l=i[g]().data(h);b.preventDefault(),l._getMask(),l._moveGhost(d,j,k)}}).on(j.END,k.GRID,function(){a(this)[g]("end")}).on(j.LEAVE,k.GRID,function(b){b.preventDefault(),"mask"===a(b.target).data("lt-grid")&&a(this)[g]("end")}).on(j.DROP,k.GRID,function(b){var c=a(f.get(b.originalEvent));if(c.length){var d=a(this),e=d[g]().data(h);b.preventDefault(),e._moveToGhost(c),e.end()}}),a.fn[g]=c._jQueryInterface,a.fn[g].LTGrid=c,c}(jQuery)),d=(function(a){function b(b,d){this.$element=a(b),this.$target=a(d.target)[c.NAME](),this.ltGrid=this.$target.data(c.DATA_KEY)}var d="ltGridOnly",e="lt.grid-only",f="."+e,g={CLICK:"click"+f},h={xs:"lt-only-xs",sm:"lt-only-sm",md:"lt-only-md",lg:"lt-only-lg"},i={TOGGLE:'[data-toggle="lt-grid-only"]'};return b.NAME=d,b.DATA_KEY=e,b.EVENT_KEY=f,b.prototype.originalParams=function(){return void 0===this.ltGrid.options().originalParams&&(this.ltGrid.options().originalParams=this.ltGrid.options().params),this.ltGrid.options().originalParams},b.prototype.set=function(b){var c=this.originalParams(),d=a.map(h,function(a){return a});if(this.$target.removeClass(d.join(" ")),b){var e={};e[b]=c[b],e[b].maxWidth=Number.MAX_VALUE,this.$target.addClass(h[b]),this.ltGrid.option("params",e)}else this.ltGrid.option("params",c)},b._jQueryInterface=function(c,d){return this.each(function(){var f=a(this),g=f.data(e),h=a.extend(!0,{},f.data(),"object"==typeof c&&c);g||(g=new b(this,h),f.data(e,g)),"string"==typeof c&&g[c](d)})},a(document).on(g.CLICK,i.TOGGLE,function(c){var d=a(c.target);d[b.NAME]("set",d.data("only"))}),a.fn[d]=b._jQueryInterface,a.fn[d].LTGridOnly=b,b}(jQuery),function(a){var b="ltRect",c="lt.rect",d={NAME:b,DATA_KEY:c};return a.fn[b]=function(a,b){return void 0===b?(void 0===this.data(c+a)&&this.data(c+a,(new e).loadCss(this.attr("class"),a)),this.data(c+a)):(this.data(c+a,b),this.attr("class",b.setCss(this.attr("class"),a)),this)},d}(jQuery)),e=(function(a){var b="ltSize",d={NAME:b};return a.fn[b]=function(){return this[c.NAME]().data(c.DATA_KEY).size()},d}(jQuery),function(){function a(a,b,c,d){this.x=a||0,this.y=b||0,this.w=c||1,this.h=d||1}var b=["x","y","w","h"];return a.prototype.bottom=function(){return this.y+this.h},a.prototype.right=function(){return this.x+this.w},a.prototype.intersect=function(a){return this.x<a.right()&&this.right()>a.x&&this.y<a.bottom()&&this.bottom()>a.y},a.prototype.setCss=function(a,c){var d=this;return b.forEach(function(b){a=a.replace(new RegExp("lt-"+c+"-"+b+"-(\\d+)"),"lt-"+c+"-"+b+"-"+d[b])}),a},a.prototype.loadCss=function(a,c){var d=this;return b.forEach(function(b){var e=a.match(new RegExp("lt-"+c+"-"+b+"-(\\d+)"));e&&(d[b]=parseInt(e[1],10))}),this},a}()),f=function(){var a={};return a.getRandomNumber=function(){return Math.round((new Date).getTime()+100*Math.random())},a.getId=function(a){return a.id||(a.id="lt-"+this.getRandomNumber()),a.id},a.clear=function(){this.item=null},a.set=function(a,b){this.item=JSON.stringify({LTWidget:this.getId(b)}),a.dataTransfer&&a.dataTransfer.setData("text",this.item)},a.get=function(a){var b=a.dataTransfer&&a.dataTransfer.getData("text")||this.item;if(b){var c=JSON.parse(b);return document.getElementById(c.LTWidget)}},a}();return c.Rect=e,c.Grid=b,c}(jQuery);
//# sourceMappingURL=layout-grid.min.js.map
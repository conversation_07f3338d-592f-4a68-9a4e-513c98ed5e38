/* Galician localization for 'UI date picker' jQuery extension. */
/* Translated by <PERSON> <<EMAIL>>. */
( function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}( function( datepicker ) {

datepicker.regional.gl = {
	closeText: "Pechar",
	prevText: "&#x3C;Ant",
	nextText: "Seg&#x3E;",
	currentText: "Hoxe",
	monthNames: [ "Xaneiro","<PERSON>re<PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>",
	"<PERSON><PERSON>","Agosto","Setembro","Outubro","Novembro","Decembro" ],
	monthNamesShort: [ "<PERSON>an","Feb","<PERSON>","<PERSON>b<PERSON>","<PERSON>","<PERSON><PERSON>",
	"<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>" ],
	dayNames: [ "<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>érc<PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>bad<PERSON>" ],
	dayNamesShort: [ "Dom","Lun","Mar","Mér","Xov","Ven","Sáb" ],
	dayNamesMin: [ "Do","Lu","Ma","Mé","Xo","Ve","Sá" ],
	weekHeader: "Sm",
	dateFormat: "dd/mm/yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.gl );

return datepicker.regional.gl;

} ) );

/* Faroese initialisation for the jQuery UI date picker plugin */
/* Written by <PERSON><PERSON><PERSON>, <EMAIL> */
( function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}( function( datepicker ) {

datepicker.regional.fo = {
	closeText: "Lat aftur",
	prevText: "&#x3C;Fyrra",
	nextText: "Næsta&#x3E;",
	currentText: "Í dag",
	monthNames: [ "<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON>","<PERSON><PERSON><PERSON>","<PERSON>","<PERSON><PERSON>",
	"<PERSON><PERSON>","August","September","Oktober","November","Desember" ],
	monthNamesShort: [ "Jan","Feb","Mar","Apr","<PERSON>","<PERSON>",
	"Jul","Aug","<PERSON>","<PERSON><PERSON>","Nov","<PERSON>" ],
	dayNames: [
		"<PERSON><PERSON><PERSON><PERSON><PERSON>",
		"<PERSON><PERSON><PERSON><PERSON><PERSON>",
		"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
		"<PERSON><PERSON><PERSON><PERSON><PERSON>",
		"<PERSON><PERSON><PERSON><PERSON><PERSON>",
		"Fr<PERSON>ggjadagu<PERSON>",
		"<PERSON>yardagur"
	],
	dayNamesShort: [ "Sun","Mán","Týs","Mik","Hós","Frí","Ley" ],
	dayNamesMin: [ "Su","Má","Tý","Mi","Hó","Fr","Le" ],
	weekHeader: "Vk",
	dateFormat: "dd-mm-yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.fo );

return datepicker.regional.fo;

} ) );

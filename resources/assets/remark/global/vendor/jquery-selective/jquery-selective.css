.addMember .avatar {
  display: inline-block;
  width: 30px;
}

.addMember-items {
  display: inline-block;
  padding-left: 0;
  margin-bottom: 0;
  vertical-align: middle;
  list-style: none;
}

.addMember-item {
  position: relative;
  display: inline-block;
  margin-right: 10px;
}

.addMember-item:hover .addMember-remove {
  display: block;
}

.addMember-remove {
  position: absolute;
  top: -2px;
  right: -2px;
  display: none;
  font-size: 10px;
  line-height: 1;
  color: #ff666b;
  cursor: pointer;
  background-color: #fff;
  border-radius: 50%;
}

.addMember-trigger {
  position: relative;
  display: inline-block;
  vertical-align: middle;
}

.addMember-trigger-button {
  width: 30px;
  height: 30px;
  line-height: 28px;
  color: #fff;
  text-align: center;
  cursor: pointer;
  background-color: #dcdfe3;
  border-radius: 50%;
}

.addMember-trigger-button:hover {
  background-color: #e6e9ed;
}

.addMember-trigger-dropdown {
  position: absolute;
  top: 0;
  left: 45px;
  z-index: 1200;
  display: none;
  padding: 1px;
  background-color: #fff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, .2);
}

.addMember-trigger-dropdown:before {
  position: absolute;
  top: 6px;
  left: -19px;
  display: block;
  width: 0;
  height: 0;
  content: "";
  border-color: transparent #fff transparent transparent;
  border-style: solid;
  border-width: 10px;
}

.addMember-trigger.addMember-active .addMember-trigger-dropdown {
  display: block;
}

.addMember-list {
  max-height: 260px;
  padding-left: 0;
  margin-bottom: 0;
  overflow-y: auto;
  list-style: none;
}

.addMember-list-item {
  padding: 8px 20px;
  margin-bottom: 1px;
  white-space: nowrap;
  cursor: pointer;
}

.addMember-list-item > .avatar {
  margin-right: 10px;
}

.addMember-list-item.addMember-selected, .addMember-list-item:hover {
  background-color: #f3f7f9;
}

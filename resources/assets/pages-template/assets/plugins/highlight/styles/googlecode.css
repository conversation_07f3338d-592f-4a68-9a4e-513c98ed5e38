/*

Google Code style (c) <PERSON><PERSON> <<EMAIL>>

*/

.hljs {
  display: block;
  overflow-x: auto;
  padding: 0.5em;
  background: white;
  color: black;
  -webkit-text-size-adjust: none;
}

.hljs-comment,
.hljs-template_comment,
.hljs-javadoc {
  color: #800;
}

.hljs-keyword,
.method,
.hljs-list .hljs-keyword,
.nginx .hljs-title,
.hljs-tag .hljs-title,
.setting .hljs-value,
.hljs-winutils,
.tex .hljs-command,
.http .hljs-title,
.hljs-request,
.hljs-status {
  color: #008;
}

.hljs-envvar,
.tex .hljs-special {
  color: #660;
}

.hljs-string,
.hljs-tag .hljs-value,
.hljs-cdata,
.hljs-filter .hljs-argument,
.hljs-attr_selector,
.apache .hljs-cbracket,
.hljs-date,
.hljs-regexp,
.coffeescript .hljs-attribute {
  color: #080;
}

.hljs-sub .hljs-identifier,
.hljs-pi,
.hljs-tag,
.hljs-tag .hljs-keyword,
.hljs-decorator,
.ini .hljs-title,
.hljs-shebang,
.hljs-prompt,
.hljs-hexcolor,
.hljs-rules .hljs-value,
.hljs-literal,
.hljs-symbol,
.ruby .hljs-symbol .hljs-string,
.hljs-number,
.css .hljs-function,
.clojure .hljs-attribute {
  color: #066;
}

.hljs-class .hljs-title,
.smalltalk .hljs-class,
.hljs-javadoctag,
.hljs-yardoctag,
.hljs-phpdoc,
.hljs-dartdoc,
.hljs-type,
.hljs-typename,
.hljs-tag .hljs-attribute,
.hljs-doctype,
.hljs-class .hljs-id,
.hljs-built_in,
.setting,
.hljs-params,
.hljs-variable {
  color: #606;
}

.css .hljs-tag,
.hljs-rules .hljs-property,
.hljs-pseudo,
.hljs-subst {
  color: #000;
}

.css .hljs-class,
.css .hljs-id {
  color: #9b703f;
}

.hljs-value .hljs-important {
  color: #ff7700;
  font-weight: bold;
}

.hljs-rules .hljs-keyword {
  color: #c5af75;
}

.hljs-annotation,
.apache .hljs-sqbracket,
.nginx .hljs-built_in {
  color: #9b859d;
}

.hljs-preprocessor,
.hljs-preprocessor *,
.hljs-pragma {
  color: #444;
}

.tex .hljs-formula {
  background-color: #eee;
  font-style: italic;
}

.diff .hljs-header,
.hljs-chunk {
  color: #808080;
  font-weight: bold;
}

.diff .hljs-change {
  background-color: #bccff9;
}

.hljs-addition {
  background-color: #baeeba;
}

.hljs-deletion {
  background-color: #ffc8bd;
}

.hljs-comment .hljs-yardoctag {
  font-weight: bold;
}

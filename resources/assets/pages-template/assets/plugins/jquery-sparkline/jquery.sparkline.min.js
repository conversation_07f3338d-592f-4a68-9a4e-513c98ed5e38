!function(e,t,l){!function(e){"function"==typeof define&&define.amd?define(["jquery"],e):jQuery&&!jQuery.fn.sparkline&&e(jQuery)}(function(n){"use strict";var i,a,o,r,s,c,u,d,h,f,p,m,g,v,y,b,C,T,E,w,D,_,S,I,x,k,O,R,M,N,A,K,L={},P=0;i=function(){return{common:{type:"line",lineColor:"#00f",fillColor:"#cdf",defaultPixelsPerValue:3,width:"auto",height:"auto",composite:!1,tagValuesAttribute:"values",tagOptionsPrefix:"spark",enableTagOptions:!1,enableHighlight:!0,highlightLighten:1.4,tooltipSkipNull:!0,tooltipPrefix:"",tooltipSuffix:"",disableHiddenCheck:!1,numberFormatter:!1,numberDigitGroupCount:3,numberDigitGroupSep:",",numberDecimalMark:".",disableTooltips:!1,disableInteraction:!1},line:{spotColor:"#f80",highlightSpotColor:"#5f5",highlightLineColor:"#f22",spotRadius:1.5,minSpotColor:"#f80",maxSpotColor:"#f80",lineWidth:1,normalRangeMin:l,normalRangeMax:l,normalRangeColor:"#ccc",drawNormalOnTop:!1,chartRangeMin:l,chartRangeMax:l,chartRangeMinX:l,chartRangeMaxX:l,tooltipFormat:new o('<span style="color: {{color}}">&#9679;</span> {{prefix}}{{y}}{{suffix}}')},bar:{barColor:"#3366cc",negBarColor:"#f44",stackedBarColor:["#3366cc","#dc3912","#ff9900","#109618","#66aa00","#dd4477","#0099c6","#990099"],zeroColor:l,nullColor:l,zeroAxis:!0,barWidth:4,barSpacing:1,chartRangeMax:l,chartRangeMin:l,chartRangeClip:!1,colorMap:l,tooltipFormat:new o('<span style="color: {{color}}">&#9679;</span> {{prefix}}{{value}}{{suffix}}')},tristate:{barWidth:4,barSpacing:1,posBarColor:"#6f6",negBarColor:"#f44",zeroBarColor:"#999",colorMap:{},tooltipFormat:new o('<span style="color: {{color}}">&#9679;</span> {{value:map}}'),tooltipValueLookups:{map:{"-1":"Loss",0:"Draw",1:"Win"}}},discrete:{lineHeight:"auto",thresholdColor:l,thresholdValue:0,chartRangeMax:l,chartRangeMin:l,chartRangeClip:!1,tooltipFormat:new o("{{prefix}}{{value}}{{suffix}}")},bullet:{targetColor:"#f33",targetWidth:3,performanceColor:"#33f",rangeColors:["#d3dafe","#a8b6ff","#7f94ff"],base:l,tooltipFormat:new o("{{fieldkey:fields}} - {{value}}"),tooltipValueLookups:{fields:{r:"Range",p:"Performance",t:"Target"}}},pie:{offset:0,sliceColors:["#3366cc","#dc3912","#ff9900","#109618","#66aa00","#dd4477","#0099c6","#990099"],borderWidth:0,borderColor:"#000",tooltipFormat:new o('<span style="color: {{color}}">&#9679;</span> {{value}} ({{percent.1}}%)')},box:{raw:!1,boxLineColor:"#000",boxFillColor:"#cdf",whiskerColor:"#000",outlierLineColor:"#333",outlierFillColor:"#fff",medianColor:"#f00",showOutliers:!0,outlierIQR:1.5,spotRadius:1.5,target:l,targetColor:"#4a2",chartRangeMax:l,chartRangeMin:l,tooltipFormat:new o("{{field:fields}}: {{value}}"),tooltipFormatFieldlistKey:"field",tooltipValueLookups:{fields:{lq:"Lower Quartile",med:"Median",uq:"Upper Quartile",lo:"Left Outlier",ro:"Right Outlier",lw:"Left Whisker",rw:"Right Whisker"}}}}},k='.jqstooltip { position: absolute;left: 0px;top: 0px;visibility: hidden;background: rgb(0, 0, 0) transparent;background-color: rgba(0,0,0,0.6);filter:progid:DXImageTransform.Microsoft.gradient(startColorstr=#99000000, endColorstr=#99000000);-ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorstr=#99000000, endColorstr=#99000000)";color: white;font: 10px arial, san serif;text-align: left;white-space: nowrap;padding: 5px;border: 1px solid white;z-index: 10000;}.jqsfield { color: white;font: 10px arial, san serif;text-align: left;}',a=function(){var e,t;return e=function(){this.init.apply(this,arguments)},arguments.length>1?(arguments[0]?(e.prototype=n.extend(new arguments[0],arguments[arguments.length-1]),e._super=arguments[0].prototype):e.prototype=arguments[arguments.length-1],arguments.length>2&&(t=Array.prototype.slice.call(arguments,1,-1),t.unshift(e.prototype),n.extend.apply(n,t))):e.prototype=arguments[0],e.prototype.cls=e,e},n.SPFormatClass=o=a({fre:/\{\{([\w.]+?)(:(.+?))?\}\}/g,precre:/(\w+)\.(\d+)/,init:function(e,t){this.format=e,this.fclass=t},render:function(e,t,n){var i,a,o,r,s,c=this,u=e;return this.format.replace(this.fre,function(){var e;return a=arguments[1],o=arguments[3],i=c.precre.exec(a),i?(s=i[2],a=i[1]):s=!1,r=u[a],r===l?"":o&&t&&t[o]?(e=t[o],e.get?t[o].get(r)||r:t[o][r]||r):(h(r)&&(r=n.get("numberFormatter")?n.get("numberFormatter")(r):v(r,s,n.get("numberDigitGroupCount"),n.get("numberDigitGroupSep"),n.get("numberDecimalMark"))),r)})}}),n.spformat=function(e,t){return new o(e,t)},r=function(e,t,l){return t>e?t:e>l?l:e},s=function(e,l){var n;return 2===l?(n=t.floor(e.length/2),e.length%2?e[n]:(e[n-1]+e[n])/2):e.length%2?(n=(e.length*l+l)/4,n%1?(e[t.floor(n)]+e[t.floor(n)-1])/2:e[n-1]):(n=(e.length*l+2)/4,n%1?(e[t.floor(n)]+e[t.floor(n)-1])/2:e[n-1])},c=function(e){var t;switch(e){case"undefined":e=l;break;case"null":e=null;break;case"true":e=!0;break;case"false":e=!1;break;default:t=parseFloat(e),e==t&&(e=t)}return e},u=function(e){var t,l=[];for(t=e.length;t--;)l[t]=c(e[t]);return l},d=function(e,t){var l,n,i=[];for(l=0,n=e.length;n>l;l++)e[l]!==t&&i.push(e[l]);return i},h=function(e){return!isNaN(parseFloat(e))&&isFinite(e)},v=function(e,t,l,i,a){var o,r;for(e=(t===!1?parseFloat(e).toString():e.toFixed(t)).split(""),o=(o=n.inArray(".",e))<0?e.length:o,o<e.length&&(e[o]=a),r=o-l;r>0;r-=l)e.splice(r,0,i);return e.join("")},f=function(e,t,l){var n;for(n=t.length;n--;)if((!l||null!==t[n])&&t[n]!==e)return!1;return!0},p=function(e){var t,l=0;for(t=e.length;t--;)l+="number"==typeof e[t]?e[t]:0;return l},g=function(e){return n.isArray(e)?e:[e]},m=function(t){var l;e.createStyleSheet?e.createStyleSheet().cssText=t:(l=e.createElement("style"),l.type="text/css",e.getElementsByTagName("head")[0].appendChild(l),l["string"==typeof e.body.style.WebkitAppearance?"innerText":"innerHTML"]=t)},n.fn.simpledraw=function(t,i,a,o){var r,s;if(a&&(r=this.data("_jqs_vcanvas")))return r;if(n.fn.sparkline.canvas===!1)return!1;if(n.fn.sparkline.canvas===l){var c=e.createElement("canvas");if(c.getContext&&c.getContext("2d"))n.fn.sparkline.canvas=function(e,t,l,n){return new N(e,t,l,n)};else{if(!e.namespaces||e.namespaces.v)return n.fn.sparkline.canvas=!1,!1;e.namespaces.add("v","urn:schemas-microsoft-com:vml","#default#VML"),n.fn.sparkline.canvas=function(e,t,l){return new A(e,t,l)}}}return t===l&&(t=n(this).innerWidth()),i===l&&(i=n(this).innerHeight()),r=n.fn.sparkline.canvas(t,i,this,o),s=n(this).data("_jqs_mhandler"),s&&s.registerCanvas(r),r},n.fn.cleardraw=function(){var e=this.data("_jqs_vcanvas");e&&e.reset()},n.RangeMapClass=y=a({init:function(e){var t,l,n=[];for(t in e)e.hasOwnProperty(t)&&"string"==typeof t&&t.indexOf(":")>-1&&(l=t.split(":"),l[0]=0===l[0].length?-1/0:parseFloat(l[0]),l[1]=0===l[1].length?1/0:parseFloat(l[1]),l[2]=e[t],n.push(l));this.map=e,this.rangelist=n||!1},get:function(e){var t,n,i,a=this.rangelist;if((i=this.map[e])!==l)return i;if(a)for(t=a.length;t--;)if(n=a[t],n[0]<=e&&n[1]>=e)return n[2];return l}}),n.range_map=function(e){return new y(e)},b=a({init:function(e,t){var l=n(e);this.$el=l,this.options=t,this.currentPageX=0,this.currentPageY=0,this.el=e,this.splist=[],this.tooltip=null,this.over=!1,this.displayTooltips=!t.get("disableTooltips"),this.highlightEnabled=!t.get("disableHighlight")},registerSparkline:function(e){this.splist.push(e),this.over&&this.updateDisplay()},registerCanvas:function(e){var t=n(e.canvas);this.canvas=e,this.$canvas=t,t.mouseenter(n.proxy(this.mouseenter,this)),t.mouseleave(n.proxy(this.mouseleave,this)),t.click(n.proxy(this.mouseclick,this))},reset:function(e){this.splist=[],this.tooltip&&e&&(this.tooltip.remove(),this.tooltip=l)},mouseclick:function(e){var t=n.Event("sparklineClick");t.originalEvent=e,t.sparklines=this.splist,this.$el.trigger(t)},mouseenter:function(t){n(e.body).unbind("mousemove.jqs"),n(e.body).bind("mousemove.jqs",n.proxy(this.mousemove,this)),this.over=!0,this.currentPageX=t.pageX,this.currentPageY=t.pageY,this.currentEl=t.target,!this.tooltip&&this.displayTooltips&&(this.tooltip=new C(this.options),this.tooltip.updatePosition(t.pageX,t.pageY)),this.updateDisplay()},mouseleave:function(){n(e.body).unbind("mousemove.jqs");var t,l,i=this.splist,a=i.length,o=!1;for(this.over=!1,this.currentEl=null,this.tooltip&&(this.tooltip.remove(),this.tooltip=null),l=0;a>l;l++)t=i[l],t.clearRegionHighlight()&&(o=!0);o&&this.canvas.render()},mousemove:function(e){this.currentPageX=e.pageX,this.currentPageY=e.pageY,this.currentEl=e.target,this.tooltip&&this.tooltip.updatePosition(e.pageX,e.pageY),this.updateDisplay()},updateDisplay:function(){var e,t,l,i,a,o=this.splist,r=o.length,s=!1,c=this.$canvas.offset(),u=this.currentPageX-c.left,d=this.currentPageY-c.top;if(this.over){for(l=0;r>l;l++)t=o[l],i=t.setRegionHighlight(this.currentEl,u,d),i&&(s=!0);if(s){if(a=n.Event("sparklineRegionChange"),a.sparklines=this.splist,this.$el.trigger(a),this.tooltip){for(e="",l=0;r>l;l++)t=o[l],e+=t.getCurrentRegionTooltip();this.tooltip.setContent(e)}this.disableHighlight||this.canvas.render()}null===i&&this.mouseleave()}}}),C=a({sizeStyle:"position: static !important;display: block !important;visibility: hidden !important;float: left !important;padding: 5px 5px 15px 5px;min-height: 30px;min-width: 30px;",init:function(t){var l,i=t.get("tooltipClassname","jqstooltip"),a=this.sizeStyle;this.container=t.get("tooltipContainer")||e.body,this.tooltipOffsetX=t.get("tooltipOffsetX",10),this.tooltipOffsetY=t.get("tooltipOffsetY",12),n("#jqssizetip").remove(),n("#jqstooltip").remove(),this.sizetip=n("<div/>",{id:"jqssizetip",style:a,"class":i}),this.tooltip=n("<div/>",{id:"jqstooltip","class":i}).appendTo(this.container),l=this.tooltip.offset(),this.offsetLeft=l.left,this.offsetTop=l.top,this.hidden=!0,n(window).unbind("resize.jqs scroll.jqs"),n(window).bind("resize.jqs scroll.jqs",n.proxy(this.updateWindowDims,this)),this.updateWindowDims()},updateWindowDims:function(){this.scrollTop=n(window).scrollTop(),this.scrollLeft=n(window).scrollLeft(),this.scrollRight=this.scrollLeft+n(window).width(),this.updatePosition()},getSize:function(e){this.sizetip.html(e).appendTo(this.container),this.width=this.sizetip.width()+12,this.height=this.sizetip.height()+12,this.sizetip.remove()},setContent:function(e){return e?(this.getSize(e),this.tooltip.html(e).css({width:this.width,height:this.height,visibility:"visible"}),this.hidden&&(this.hidden=!1,this.updatePosition()),void 0):(this.tooltip.css("visibility","hidden"),this.hidden=!0,void 0)},updatePosition:function(e,t){if(e===l){if(this.mousex===l)return;e=this.mousex-this.offsetLeft,t=this.mousey-this.offsetTop}else this.mousex=e-=this.offsetLeft,this.mousey=t-=this.offsetTop;this.height&&this.width&&!this.hidden&&(t-=this.height+this.tooltipOffsetY,e+=this.tooltipOffsetX,t<this.scrollTop&&(t=this.scrollTop),e<this.scrollLeft?e=this.scrollLeft:e+this.width>this.scrollRight&&(e=this.scrollRight-this.width),this.tooltip.css({left:e,top:t}))},remove:function(){this.tooltip.remove(),this.sizetip.remove(),this.sizetip=this.tooltip=l,n(window).unbind("resize.jqs scroll.jqs")}}),O=function(){m(k)},n(O),K=[],n.fn.sparkline=function(t,i){return this.each(function(){var a,o,r=new n.fn.sparkline.options(this,i),s=n(this);if(a=function(){var i,a,o,c,u,d,h;return"html"===t||t===l?(h=this.getAttribute(r.get("tagValuesAttribute")),(h===l||null===h)&&(h=s.html()),i=h.replace(/(^\s*<!--)|(-->\s*$)|\s+/g,"").split(",")):i=t,a="auto"===r.get("width")?i.length*r.get("defaultPixelsPerValue"):r.get("width"),"auto"===r.get("height")?r.get("composite")&&n.data(this,"_jqs_vcanvas")||(c=e.createElement("span"),c.innerHTML="a",s.html(c),o=n(c).innerHeight()||n(c).height(),n(c).remove(),c=null):o=r.get("height"),r.get("disableInteraction")?u=!1:(u=n.data(this,"_jqs_mhandler"),u?r.get("composite")||u.reset():(u=new b(this,r),n.data(this,"_jqs_mhandler",u))),r.get("composite")&&!n.data(this,"_jqs_vcanvas")?(n.data(this,"_jqs_errnotify")||(alert("Attempted to attach a composite sparkline to an element with no existing sparkline"),n.data(this,"_jqs_errnotify",!0)),void 0):(d=new(n.fn.sparkline[r.get("type")])(this,i,r,a,o),d.render(),u&&u.registerSparkline(d),void 0)},n(this).html()&&!r.get("disableHiddenCheck")&&n(this).is(":hidden")||!n(this).parents("body").length){if(!r.get("composite")&&n.data(this,"_jqs_pending"))for(o=K.length;o;o--)K[o-1][0]==this&&K.splice(o-1,1);K.push([this,a]),n.data(this,"_jqs_pending",!0)}else a.call(this)})},n.fn.sparkline.defaults=i(),n.sparkline_display_visible=function(){var e,t,l,i=[];for(t=0,l=K.length;l>t;t++)e=K[t][0],n(e).is(":visible")&&!n(e).parents().is(":hidden")?(K[t][1].call(e),n.data(K[t][0],"_jqs_pending",!1),i.push(t)):!n(e).closest("html").length&&!n.data(e,"_jqs_pending")&&(n.data(K[t][0],"_jqs_pending",!1),i.push(t));for(t=i.length;t;t--)K.splice(i[t-1],1)},n.fn.sparkline.options=a({init:function(e,t){var l,i,a,o;this.userOptions=t=t||{},this.tag=e,this.tagValCache={},i=n.fn.sparkline.defaults,a=i.common,this.tagOptionsPrefix=t.enableTagOptions&&(t.tagOptionsPrefix||a.tagOptionsPrefix),o=this.getTagSetting("type"),l=o===L?i[t.type||a.type]:i[o],this.mergedOptions=n.extend({},a,l,t)},getTagSetting:function(e){var t,n,i,a,o=this.tagOptionsPrefix;if(o===!1||o===l)return L;if(this.tagValCache.hasOwnProperty(e))t=this.tagValCache.key;else{if(t=this.tag.getAttribute(o+e),t===l||null===t)t=L;else if("["===t.substr(0,1))for(t=t.substr(1,t.length-2).split(","),n=t.length;n--;)t[n]=c(t[n].replace(/(^\s*)|(\s*$)/g,""));else if("{"===t.substr(0,1))for(i=t.substr(1,t.length-2).split(","),t={},n=i.length;n--;)a=i[n].split(":",2),t[a[0].replace(/(^\s*)|(\s*$)/g,"")]=c(a[1].replace(/(^\s*)|(\s*$)/g,""));else t=c(t);this.tagValCache.key=t}return t},get:function(e,t){var n,i=this.getTagSetting(e);return i!==L?i:(n=this.mergedOptions[e])===l?t:n}}),n.fn.sparkline._base=a({disabled:!1,init:function(e,t,i,a,o){this.el=e,this.$el=n(e),this.values=t,this.options=i,this.width=a,this.height=o,this.currentRegion=l},initTarget:function(){var e=!this.options.get("disableInteraction");(this.target=this.$el.simpledraw(this.width,this.height,this.options.get("composite"),e))?(this.canvasWidth=this.target.pixelWidth,this.canvasHeight=this.target.pixelHeight):this.disabled=!0},render:function(){return this.disabled?(this.el.innerHTML="",!1):!0},getRegion:function(){},setRegionHighlight:function(e,t,n){var i,a=this.currentRegion,o=!this.options.get("disableHighlight");return t>this.canvasWidth||n>this.canvasHeight||0>t||0>n?null:(i=this.getRegion(e,t,n),a!==i?(a!==l&&o&&this.removeHighlight(),this.currentRegion=i,i!==l&&o&&this.renderHighlight(),!0):!1)},clearRegionHighlight:function(){return this.currentRegion!==l?(this.removeHighlight(),this.currentRegion=l,!0):!1},renderHighlight:function(){this.changeHighlight(!0)},removeHighlight:function(){this.changeHighlight(!1)},changeHighlight:function(){},getCurrentRegionTooltip:function(){var e,t,i,a,r,s,c,u,d,h,f,p,m,g,v=this.options,y="",b=[];if(this.currentRegion===l)return"";if(e=this.getCurrentRegionFields(),f=v.get("tooltipFormatter"))return f(this,v,e);if(v.get("tooltipChartTitle")&&(y+='<div class="jqs jqstitle">'+v.get("tooltipChartTitle")+"</div>\n"),t=this.options.get("tooltipFormat"),!t)return"";if(n.isArray(t)||(t=[t]),n.isArray(e)||(e=[e]),c=this.options.get("tooltipFormatFieldlist"),u=this.options.get("tooltipFormatFieldlistKey"),c&&u){for(d=[],s=e.length;s--;)h=e[s][u],-1!=(g=n.inArray(h,c))&&(d[g]=e[s]);e=d}for(i=t.length,m=e.length,s=0;i>s;s++)for(p=t[s],"string"==typeof p&&(p=new o(p)),a=p.fclass||"jqsfield",g=0;m>g;g++)e[g].isNull&&v.get("tooltipSkipNull")||(n.extend(e[g],{prefix:v.get("tooltipPrefix"),suffix:v.get("tooltipSuffix")}),r=p.render(e[g],v.get("tooltipValueLookups"),v),b.push('<div class="'+a+'">'+r+"</div>"));return b.length?y+b.join("\n"):""},getCurrentRegionFields:function(){},calcHighlightColor:function(e,l){var n,i,a,o,s=l.get("highlightColor"),c=l.get("highlightLighten");if(s)return s;if(c&&(n=/^#([0-9a-f])([0-9a-f])([0-9a-f])$/i.exec(e)||/^#([0-9a-f]{2})([0-9a-f]{2})([0-9a-f]{2})$/i.exec(e))){for(a=[],i=4===e.length?16:1,o=0;3>o;o++)a[o]=r(t.round(parseInt(n[o+1],16)*i*c),0,255);return"rgb("+a.join(",")+")"}return e}}),T={changeHighlight:function(e){var t,l=this.currentRegion,i=this.target,a=this.regionShapes[l];a&&(t=this.renderRegion(l,e),n.isArray(t)||n.isArray(a)?(i.replaceWithShapes(a,t),this.regionShapes[l]=n.map(t,function(e){return e.id})):(i.replaceWithShape(a,t),this.regionShapes[l]=t.id))},render:function(){var e,t,l,i,a=this.values,o=this.target,r=this.regionShapes;if(this.cls._super.render.call(this)){for(l=a.length;l--;)if(e=this.renderRegion(l))if(n.isArray(e)){for(t=[],i=e.length;i--;)e[i].append(),t.push(e[i].id);r[l]=t}else e.append(),r[l]=e.id;else r[l]=null;o.render()}}},n.fn.sparkline.line=E=a(n.fn.sparkline._base,{type:"line",init:function(e,t,l,n,i){E._super.init.call(this,e,t,l,n,i),this.vertices=[],this.regionMap=[],this.xvalues=[],this.yvalues=[],this.yminmax=[],this.hightlightSpotId=null,this.lastShapeId=null,this.initTarget()},getRegion:function(e,t){var n,i=this.regionMap;for(n=i.length;n--;)if(null!==i[n]&&t>=i[n][0]&&t<=i[n][1])return i[n][2];return l},getCurrentRegionFields:function(){var e=this.currentRegion;return{isNull:null===this.yvalues[e],x:this.xvalues[e],y:this.yvalues[e],color:this.options.get("lineColor"),fillColor:this.options.get("fillColor"),offset:e}},renderHighlight:function(){var e,t,n=this.currentRegion,i=this.target,a=this.vertices[n],o=this.options,r=o.get("spotRadius"),s=o.get("highlightSpotColor"),c=o.get("highlightLineColor");a&&(r&&s&&(e=i.drawCircle(a[0],a[1],r,l,s),this.highlightSpotId=e.id,i.insertAfterShape(this.lastShapeId,e)),c&&(t=i.drawLine(a[0],this.canvasTop,a[0],this.canvasTop+this.canvasHeight,c),this.highlightLineId=t.id,i.insertAfterShape(this.lastShapeId,t)))},removeHighlight:function(){var e=this.target;this.highlightSpotId&&(e.removeShapeId(this.highlightSpotId),this.highlightSpotId=null),this.highlightLineId&&(e.removeShapeId(this.highlightLineId),this.highlightLineId=null)},scanValues:function(){var e,l,n,i,a,o=this.values,r=o.length,s=this.xvalues,c=this.yvalues,u=this.yminmax;for(e=0;r>e;e++)l=o[e],n="string"==typeof o[e],i="object"==typeof o[e]&&o[e]instanceof Array,a=n&&o[e].split(":"),n&&2===a.length?(s.push(Number(a[0])),c.push(Number(a[1])),u.push(Number(a[1]))):i?(s.push(l[0]),c.push(l[1]),u.push(l[1])):(s.push(e),null===o[e]||"null"===o[e]?c.push(null):(c.push(Number(l)),u.push(Number(l))));this.options.get("xvalues")&&(s=this.options.get("xvalues")),this.maxy=this.maxyorg=t.max.apply(t,u),this.miny=this.minyorg=t.min.apply(t,u),this.maxx=t.max.apply(t,s),this.minx=t.min.apply(t,s),this.xvalues=s,this.yvalues=c,this.yminmax=u},processRangeOptions:function(){var e=this.options,t=e.get("normalRangeMin"),n=e.get("normalRangeMax");t!==l&&(t<this.miny&&(this.miny=t),n>this.maxy&&(this.maxy=n)),e.get("chartRangeMin")!==l&&(e.get("chartRangeClip")||e.get("chartRangeMin")<this.miny)&&(this.miny=e.get("chartRangeMin")),e.get("chartRangeMax")!==l&&(e.get("chartRangeClip")||e.get("chartRangeMax")>this.maxy)&&(this.maxy=e.get("chartRangeMax")),e.get("chartRangeMinX")!==l&&(e.get("chartRangeClipX")||e.get("chartRangeMinX")<this.minx)&&(this.minx=e.get("chartRangeMinX")),e.get("chartRangeMaxX")!==l&&(e.get("chartRangeClipX")||e.get("chartRangeMaxX")>this.maxx)&&(this.maxx=e.get("chartRangeMaxX"))},drawNormalRange:function(e,n,i,a,o){var r=this.options.get("normalRangeMin"),s=this.options.get("normalRangeMax"),c=n+t.round(i-i*((s-this.miny)/o)),u=t.round(i*(s-r)/o);this.target.drawRect(e,c,a,u,l,this.options.get("normalRangeColor")).append()},render:function(){var e,i,a,o,r,s,c,u,d,h,f,p,m,g,v,b,C,T,w,D,_,S,I,x,k,O=this.options,R=this.target,M=this.canvasWidth,N=this.canvasHeight,A=this.vertices,K=O.get("spotRadius"),L=this.regionMap;if(E._super.render.call(this)&&(this.scanValues(),this.processRangeOptions(),I=this.xvalues,x=this.yvalues,this.yminmax.length&&!(this.yvalues.length<2))){for(o=r=0,e=0===this.maxx-this.minx?1:this.maxx-this.minx,i=0===this.maxy-this.miny?1:this.maxy-this.miny,a=this.yvalues.length-1,K&&(4*K>M||4*K>N)&&(K=0),K&&(_=O.get("highlightSpotColor")&&!O.get("disableInteraction"),(_||O.get("minSpotColor")||O.get("spotColor")&&x[a]===this.miny)&&(N-=t.ceil(K)),(_||O.get("maxSpotColor")||O.get("spotColor")&&x[a]===this.maxy)&&(N-=t.ceil(K),o+=t.ceil(K)),(_||(O.get("minSpotColor")||O.get("maxSpotColor"))&&(x[0]===this.miny||x[0]===this.maxy))&&(r+=t.ceil(K),M-=t.ceil(K)),(_||O.get("spotColor")||O.get("minSpotColor")||O.get("maxSpotColor")&&(x[a]===this.miny||x[a]===this.maxy))&&(M-=t.ceil(K))),N--,O.get("normalRangeMin")!==l&&!O.get("drawNormalOnTop")&&this.drawNormalRange(r,o,N,M,i),c=[],u=[c],g=v=null,b=x.length,k=0;b>k;k++)d=I[k],f=I[k+1],h=x[k],p=r+t.round((d-this.minx)*(M/e)),m=b-1>k?r+t.round((f-this.minx)*(M/e)):M,v=p+(m-p)/2,L[k]=[g||0,v,k],g=v,null===h?k&&(null!==x[k-1]&&(c=[],u.push(c)),A.push(null)):(h<this.miny&&(h=this.miny),h>this.maxy&&(h=this.maxy),c.length||c.push([p,o+N]),s=[p,o+t.round(N-N*((h-this.miny)/i))],c.push(s),A.push(s));for(C=[],T=[],w=u.length,k=0;w>k;k++)c=u[k],c.length&&(O.get("fillColor")&&(c.push([c[c.length-1][0],o+N]),T.push(c.slice(0)),c.pop()),c.length>2&&(c[0]=[c[0][0],c[1][1]]),C.push(c));for(w=T.length,k=0;w>k;k++)R.drawShape(T[k],O.get("fillColor"),O.get("fillColor")).append();for(O.get("normalRangeMin")!==l&&O.get("drawNormalOnTop")&&this.drawNormalRange(r,o,N,M,i),w=C.length,k=0;w>k;k++)R.drawShape(C[k],O.get("lineColor"),l,O.get("lineWidth")).append();if(K&&O.get("valueSpots"))for(D=O.get("valueSpots"),D.get===l&&(D=new y(D)),k=0;b>k;k++)S=D.get(x[k]),S&&R.drawCircle(r+t.round((I[k]-this.minx)*(M/e)),o+t.round(N-N*((x[k]-this.miny)/i)),K,l,S).append();K&&O.get("spotColor")&&null!==x[a]&&R.drawCircle(r+t.round((I[I.length-1]-this.minx)*(M/e)),o+t.round(N-N*((x[a]-this.miny)/i)),K,l,O.get("spotColor")).append(),this.maxy!==this.minyorg&&(K&&O.get("minSpotColor")&&(d=I[n.inArray(this.minyorg,x)],R.drawCircle(r+t.round((d-this.minx)*(M/e)),o+t.round(N-N*((this.minyorg-this.miny)/i)),K,l,O.get("minSpotColor")).append()),K&&O.get("maxSpotColor")&&(d=I[n.inArray(this.maxyorg,x)],R.drawCircle(r+t.round((d-this.minx)*(M/e)),o+t.round(N-N*((this.maxyorg-this.miny)/i)),K,l,O.get("maxSpotColor")).append())),this.lastShapeId=R.getLastShapeId(),this.canvasTop=o,R.render()}}}),n.fn.sparkline.bar=w=a(n.fn.sparkline._base,T,{type:"bar",init:function(e,i,a,o,s){var h,f,p,m,g,v,b,C,T,E,D,_,S,I,x,k,O,R,M,N,A,K,L=parseInt(a.get("barWidth"),10),P=parseInt(a.get("barSpacing"),10),F=a.get("chartRangeMin"),B=a.get("chartRangeMax"),$=a.get("chartRangeClip"),H=1/0,Z=-1/0;for(w._super.init.call(this,e,i,a,o,s),v=0,b=i.length;b>v;v++)N=i[v],h="string"==typeof N&&N.indexOf(":")>-1,(h||n.isArray(N))&&(x=!0,h&&(N=i[v]=u(N.split(":"))),N=d(N,null),f=t.min.apply(t,N),p=t.max.apply(t,N),H>f&&(H=f),p>Z&&(Z=p));this.stacked=x,this.regionShapes={},this.barWidth=L,this.barSpacing=P,this.totalBarWidth=L+P,this.width=o=i.length*L+(i.length-1)*P,this.initTarget(),$&&(S=F===l?-1/0:F,I=B===l?1/0:B),g=[],m=x?[]:g;var z=[],U=[];for(v=0,b=i.length;b>v;v++)if(x)for(k=i[v],i[v]=M=[],z[v]=0,m[v]=U[v]=0,O=0,R=k.length;R>O;O++)N=M[O]=$?r(k[O],S,I):k[O],null!==N&&(N>0&&(z[v]+=N),0>H&&Z>0?0>N?U[v]+=t.abs(N):m[v]+=N:m[v]+=t.abs(N-(0>N?Z:H)),g.push(N));else N=$?r(i[v],S,I):i[v],N=i[v]=c(N),null!==N&&g.push(N);this.max=_=t.max.apply(t,g),this.min=D=t.min.apply(t,g),this.stackMax=Z=x?t.max.apply(t,z):_,this.stackMin=H=x?t.min.apply(t,g):D,a.get("chartRangeMin")!==l&&(a.get("chartRangeClip")||a.get("chartRangeMin")<D)&&(D=a.get("chartRangeMin")),a.get("chartRangeMax")!==l&&(a.get("chartRangeClip")||a.get("chartRangeMax")>_)&&(_=a.get("chartRangeMax")),this.zeroAxis=T=a.get("zeroAxis",!0),E=0>=D&&_>=0&&T?0:0==T?D:D>0?D:_,this.xaxisOffset=E,C=x?t.max.apply(t,m)+t.max.apply(t,U):_-D,this.canvasHeightEf=T&&0>D?this.canvasHeight-2:this.canvasHeight-1,E>D?(K=x&&_>=0?Z:_,A=(K-E)/C*this.canvasHeight,A!==t.ceil(A)&&(this.canvasHeightEf-=2,A=t.ceil(A))):A=this.canvasHeight,this.yoffset=A,n.isArray(a.get("colorMap"))?(this.colorMapByIndex=a.get("colorMap"),this.colorMapByValue=null):(this.colorMapByIndex=null,this.colorMapByValue=a.get("colorMap"),this.colorMapByValue&&this.colorMapByValue.get===l&&(this.colorMapByValue=new y(this.colorMapByValue))),this.range=C},getRegion:function(e,n){var i=t.floor(n/this.totalBarWidth);return 0>i||i>=this.values.length?l:i},getCurrentRegionFields:function(){var e,t,l=this.currentRegion,n=g(this.values[l]),i=[];for(t=n.length;t--;)e=n[t],i.push({isNull:null===e,value:e,color:this.calcColor(t,e,l),offset:l});return i},calcColor:function(e,t,i){var a,o,r=this.colorMapByIndex,s=this.colorMapByValue,c=this.options;return a=this.stacked?c.get("stackedBarColor"):0>t?c.get("negBarColor"):c.get("barColor"),0===t&&c.get("zeroColor")!==l&&(a=c.get("zeroColor")),s&&(o=s.get(t))?a=o:r&&r.length>i&&(a=r[i]),n.isArray(a)?a[e%a.length]:a},renderRegion:function(e,i){var a,o,r,s,c,u,d,h,p,m,g=this.values[e],v=this.options,y=this.xaxisOffset,b=[],C=this.range,T=this.stacked,E=this.target,w=e*this.totalBarWidth,D=this.canvasHeightEf,_=this.yoffset;if(g=n.isArray(g)?g:[g],d=g.length,h=g[0],s=f(null,g),m=f(y,g,!0),s)return v.get("nullColor")?(r=i?v.get("nullColor"):this.calcHighlightColor(v.get("nullColor"),v),a=_>0?_-1:_,E.drawRect(w,a,this.barWidth-1,0,r,r)):l;for(c=_,u=0;d>u;u++){if(h=g[u],T&&h===y){if(!m||p)continue;p=!0}o=C>0?t.floor(D*(t.abs(h-y)/C))+1:1,y>h||h===y&&0===_?(a=c,c+=o):(a=_-o,_-=o),r=this.calcColor(u,h,e),i&&(r=this.calcHighlightColor(r,v)),b.push(E.drawRect(w,a,this.barWidth-1,o-1,r,r))}return 1===b.length?b[0]:b}}),n.fn.sparkline.tristate=D=a(n.fn.sparkline._base,T,{type:"tristate",init:function(e,t,i,a,o){var r=parseInt(i.get("barWidth"),10),s=parseInt(i.get("barSpacing"),10);D._super.init.call(this,e,t,i,a,o),this.regionShapes={},this.barWidth=r,this.barSpacing=s,this.totalBarWidth=r+s,this.values=n.map(t,Number),this.width=a=t.length*r+(t.length-1)*s,n.isArray(i.get("colorMap"))?(this.colorMapByIndex=i.get("colorMap"),this.colorMapByValue=null):(this.colorMapByIndex=null,this.colorMapByValue=i.get("colorMap"),this.colorMapByValue&&this.colorMapByValue.get===l&&(this.colorMapByValue=new y(this.colorMapByValue))),this.initTarget()},getRegion:function(e,l){return t.floor(l/this.totalBarWidth)},getCurrentRegionFields:function(){var e=this.currentRegion;return{isNull:this.values[e]===l,value:this.values[e],color:this.calcColor(this.values[e],e),offset:e}},calcColor:function(e,t){var l,n,i=this.values,a=this.options,o=this.colorMapByIndex,r=this.colorMapByValue;return l=r&&(n=r.get(e))?n:o&&o.length>t?o[t]:i[t]<0?a.get("negBarColor"):i[t]>0?a.get("posBarColor"):a.get("zeroBarColor")},renderRegion:function(e,l){var n,i,a,o,r,s,c=this.values,u=this.options,d=this.target;return n=d.pixelHeight,a=t.round(n/2),o=e*this.totalBarWidth,c[e]<0?(r=a,i=a-1):c[e]>0?(r=0,i=a-1):(r=a-1,i=2),s=this.calcColor(c[e],e),null!==s?(l&&(s=this.calcHighlightColor(s,u)),d.drawRect(o,r,this.barWidth-1,i-1,s,s)):void 0}}),n.fn.sparkline.discrete=_=a(n.fn.sparkline._base,T,{type:"discrete",init:function(e,i,a,o,r){_._super.init.call(this,e,i,a,o,r),this.regionShapes={},this.values=i=n.map(i,Number),this.min=t.min.apply(t,i),this.max=t.max.apply(t,i),this.range=this.max-this.min,this.width=o="auto"===a.get("width")?2*i.length:this.width,this.interval=t.floor(o/i.length),this.itemWidth=o/i.length,a.get("chartRangeMin")!==l&&(a.get("chartRangeClip")||a.get("chartRangeMin")<this.min)&&(this.min=a.get("chartRangeMin")),a.get("chartRangeMax")!==l&&(a.get("chartRangeClip")||a.get("chartRangeMax")>this.max)&&(this.max=a.get("chartRangeMax")),this.initTarget(),this.target&&(this.lineHeight="auto"===a.get("lineHeight")?t.round(.3*this.canvasHeight):a.get("lineHeight"))},getRegion:function(e,l){return t.floor(l/this.itemWidth)},getCurrentRegionFields:function(){var e=this.currentRegion;return{isNull:this.values[e]===l,value:this.values[e],offset:e}},renderRegion:function(e,l){var n,i,a,o,s=this.values,c=this.options,u=this.min,d=this.max,h=this.range,f=this.interval,p=this.target,m=this.canvasHeight,g=this.lineHeight,v=m-g;return i=r(s[e],u,d),o=e*f,n=t.round(v-v*((i-u)/h)),a=c.get("thresholdColor")&&i<c.get("thresholdValue")?c.get("thresholdColor"):c.get("lineColor"),l&&(a=this.calcHighlightColor(a,c)),p.drawLine(o,n,o,n+g,a)}}),n.fn.sparkline.bullet=S=a(n.fn.sparkline._base,{type:"bullet",init:function(e,n,i,a,o){var r,s,c;S._super.init.call(this,e,n,i,a,o),this.values=n=u(n),c=n.slice(),c[0]=null===c[0]?c[2]:c[0],c[1]=null===n[1]?c[2]:c[1],r=t.min.apply(t,n),s=t.max.apply(t,n),r=i.get("base")===l?0>r?r:0:i.get("base"),this.min=r,this.max=s,this.range=s-r,this.shapes={},this.valueShapes={},this.regiondata={},this.width=a="auto"===i.get("width")?"4.0em":a,this.target=this.$el.simpledraw(a,o,i.get("composite")),n.length||(this.disabled=!0),this.initTarget()},getRegion:function(e,t,n){var i=this.target.getShapeAt(e,t,n);return i!==l&&this.shapes[i]!==l?this.shapes[i]:l},getCurrentRegionFields:function(){var e=this.currentRegion;return{fieldkey:e.substr(0,1),value:this.values[e.substr(1)],region:e}},changeHighlight:function(e){var t,l=this.currentRegion,n=this.valueShapes[l];switch(delete this.shapes[n],l.substr(0,1)){case"r":t=this.renderRange(l.substr(1),e);break;case"p":t=this.renderPerformance(e);break;case"t":t=this.renderTarget(e)}this.valueShapes[l]=t.id,this.shapes[t.id]=l,this.target.replaceWithShape(n,t)},renderRange:function(e,l){var n=this.values[e],i=t.round(this.canvasWidth*((n-this.min)/this.range)),a=this.options.get("rangeColors")[e-2];return l&&(a=this.calcHighlightColor(a,this.options)),this.target.drawRect(0,0,i-1,this.canvasHeight-1,a,a)},renderPerformance:function(e){var l=this.values[1],n=t.round(this.canvasWidth*((l-this.min)/this.range)),i=this.options.get("performanceColor");return e&&(i=this.calcHighlightColor(i,this.options)),this.target.drawRect(0,t.round(.3*this.canvasHeight),n-1,t.round(.4*this.canvasHeight)-1,i,i)},renderTarget:function(e){var l=this.values[0],n=t.round(this.canvasWidth*((l-this.min)/this.range)-this.options.get("targetWidth")/2),i=t.round(.1*this.canvasHeight),a=this.canvasHeight-2*i,o=this.options.get("targetColor");return e&&(o=this.calcHighlightColor(o,this.options)),this.target.drawRect(n,i,this.options.get("targetWidth")-1,a-1,o,o)},render:function(){var e,t,l=this.values.length,n=this.target;if(S._super.render.call(this)){for(e=2;l>e;e++)t=this.renderRange(e).append(),this.shapes[t.id]="r"+e,this.valueShapes["r"+e]=t.id;null!==this.values[1]&&(t=this.renderPerformance().append(),this.shapes[t.id]="p1",this.valueShapes.p1=t.id),null!==this.values[0]&&(t=this.renderTarget().append(),this.shapes[t.id]="t0",this.valueShapes.t0=t.id),n.render()}}}),n.fn.sparkline.pie=I=a(n.fn.sparkline._base,{type:"pie",init:function(e,l,i,a,o){var r,s=0;if(I._super.init.call(this,e,l,i,a,o),this.shapes={},this.valueShapes={},this.values=l=n.map(l,Number),"auto"===i.get("width")&&(this.width=this.height),l.length>0)for(r=l.length;r--;)s+=l[r];this.total=s,this.initTarget(),this.radius=t.floor(t.min(this.canvasWidth,this.canvasHeight)/2)},getRegion:function(e,t,n){var i=this.target.getShapeAt(e,t,n);return i!==l&&this.shapes[i]!==l?this.shapes[i]:l},getCurrentRegionFields:function(){var e=this.currentRegion;return{isNull:this.values[e]===l,value:this.values[e],percent:100*(this.values[e]/this.total),color:this.options.get("sliceColors")[e%this.options.get("sliceColors").length],offset:e}},changeHighlight:function(e){var t=this.currentRegion,l=this.renderSlice(t,e),n=this.valueShapes[t];delete this.shapes[n],this.target.replaceWithShape(n,l),this.valueShapes[t]=l.id,this.shapes[l.id]=t},renderSlice:function(e,n){var i,a,o,r,s,c=this.target,u=this.options,d=this.radius,h=u.get("borderWidth"),f=u.get("offset"),p=2*t.PI,m=this.values,g=this.total,v=f?2*t.PI*(f/360):0;for(r=m.length,o=0;r>o;o++){if(i=v,a=v,g>0&&(a=v+p*(m[o]/g)),e===o)return s=u.get("sliceColors")[o%u.get("sliceColors").length],n&&(s=this.calcHighlightColor(s,u)),c.drawPieSlice(d,d,d-h,i,a,l,s);v=a}},render:function(){var e,n,i=this.target,a=this.values,o=this.options,r=this.radius,s=o.get("borderWidth");
if(I._super.render.call(this)){for(s&&i.drawCircle(r,r,t.floor(r-s/2),o.get("borderColor"),l,s).append(),n=a.length;n--;)a[n]&&(e=this.renderSlice(n).append(),this.valueShapes[n]=e.id,this.shapes[e.id]=n);i.render()}}}),n.fn.sparkline.box=x=a(n.fn.sparkline._base,{type:"box",init:function(e,t,l,i,a){x._super.init.call(this,e,t,l,i,a),this.values=n.map(t,Number),this.width="auto"===l.get("width")?"4.0em":i,this.initTarget(),this.values.length||(this.disabled=1)},getRegion:function(){return 1},getCurrentRegionFields:function(){var e=[{field:"lq",value:this.quartiles[0]},{field:"med",value:this.quartiles[1]},{field:"uq",value:this.quartiles[2]}];return this.loutlier!==l&&e.push({field:"lo",value:this.loutlier}),this.routlier!==l&&e.push({field:"ro",value:this.routlier}),this.lwhisker!==l&&e.push({field:"lw",value:this.lwhisker}),this.rwhisker!==l&&e.push({field:"rw",value:this.rwhisker}),e},render:function(){var e,n,i,a,o,r,c,u,d,h,f,p=this.target,m=this.values,g=m.length,v=this.options,y=this.canvasWidth,b=this.canvasHeight,C=v.get("chartRangeMin")===l?t.min.apply(t,m):v.get("chartRangeMin"),T=v.get("chartRangeMax")===l?t.max.apply(t,m):v.get("chartRangeMax"),E=0;if(x._super.render.call(this)){if(v.get("raw"))v.get("showOutliers")&&m.length>5?(n=m[0],e=m[1],a=m[2],o=m[3],r=m[4],c=m[5],u=m[6]):(e=m[0],a=m[1],o=m[2],r=m[3],c=m[4]);else if(m.sort(function(e,t){return e-t}),a=s(m,1),o=s(m,2),r=s(m,3),i=r-a,v.get("showOutliers")){for(e=c=l,d=0;g>d;d++)e===l&&m[d]>a-i*v.get("outlierIQR")&&(e=m[d]),m[d]<r+i*v.get("outlierIQR")&&(c=m[d]);n=m[0],u=m[g-1]}else e=m[0],c=m[g-1];this.quartiles=[a,o,r],this.lwhisker=e,this.rwhisker=c,this.loutlier=n,this.routlier=u,f=y/(T-C+1),v.get("showOutliers")&&(E=t.ceil(v.get("spotRadius")),y-=2*t.ceil(v.get("spotRadius")),f=y/(T-C+1),e>n&&p.drawCircle((n-C)*f+E,b/2,v.get("spotRadius"),v.get("outlierLineColor"),v.get("outlierFillColor")).append(),u>c&&p.drawCircle((u-C)*f+E,b/2,v.get("spotRadius"),v.get("outlierLineColor"),v.get("outlierFillColor")).append()),p.drawRect(t.round((a-C)*f+E),t.round(.1*b),t.round((r-a)*f),t.round(.8*b),v.get("boxLineColor"),v.get("boxFillColor")).append(),p.drawLine(t.round((e-C)*f+E),t.round(b/2),t.round((a-C)*f+E),t.round(b/2),v.get("lineColor")).append(),p.drawLine(t.round((e-C)*f+E),t.round(b/4),t.round((e-C)*f+E),t.round(b-b/4),v.get("whiskerColor")).append(),p.drawLine(t.round((c-C)*f+E),t.round(b/2),t.round((r-C)*f+E),t.round(b/2),v.get("lineColor")).append(),p.drawLine(t.round((c-C)*f+E),t.round(b/4),t.round((c-C)*f+E),t.round(b-b/4),v.get("whiskerColor")).append(),p.drawLine(t.round((o-C)*f+E),t.round(.1*b),t.round((o-C)*f+E),t.round(.9*b),v.get("medianColor")).append(),v.get("target")&&(h=t.ceil(v.get("spotRadius")),p.drawLine(t.round((v.get("target")-C)*f+E),t.round(b/2-h),t.round((v.get("target")-C)*f+E),t.round(b/2+h),v.get("targetColor")).append(),p.drawLine(t.round((v.get("target")-C)*f+E-h),t.round(b/2),t.round((v.get("target")-C)*f+E+h),t.round(b/2),v.get("targetColor")).append()),p.render()}}}),R=a({init:function(e,t,l,n){this.target=e,this.id=t,this.type=l,this.args=n},append:function(){return this.target.appendShape(this),this}}),M=a({_pxregex:/(\d+)(px)?\s*$/i,init:function(e,t,l){e&&(this.width=e,this.height=t,this.target=l,this.lastShapeId=null,l[0]&&(l=l[0]),n.data(l,"_jqs_vcanvas",this))},drawLine:function(e,t,l,n,i,a){return this.drawShape([[e,t],[l,n]],i,a)},drawShape:function(e,t,l,n){return this._genShape("Shape",[e,t,l,n])},drawCircle:function(e,t,l,n,i,a){return this._genShape("Circle",[e,t,l,n,i,a])},drawPieSlice:function(e,t,l,n,i,a,o){return this._genShape("PieSlice",[e,t,l,n,i,a,o])},drawRect:function(e,t,l,n,i,a){return this._genShape("Rect",[e,t,l,n,i,a])},getElement:function(){return this.canvas},getLastShapeId:function(){return this.lastShapeId},reset:function(){alert("reset not implemented")},_insert:function(e,t){n(t).html(e)},_calculatePixelDims:function(e,t,l){var i;i=this._pxregex.exec(t),this.pixelHeight=i?i[1]:n(l).height(),i=this._pxregex.exec(e),this.pixelWidth=i?i[1]:n(l).width()},_genShape:function(e,t){var l=P++;return t.unshift(l),new R(this,l,e,t)},appendShape:function(){alert("appendShape not implemented")},replaceWithShape:function(){alert("replaceWithShape not implemented")},insertAfterShape:function(){alert("insertAfterShape not implemented")},removeShapeId:function(){alert("removeShapeId not implemented")},getShapeAt:function(){alert("getShapeAt not implemented")},render:function(){alert("render not implemented")}}),N=a(M,{init:function(t,i,a,o){N._super.init.call(this,t,i,a),this.canvas=e.createElement("canvas"),a[0]&&(a=a[0]),n.data(a,"_jqs_vcanvas",this),n(this.canvas).css({display:"inline-block",width:t,height:i,verticalAlign:"top"}),this._insert(this.canvas,a),this._calculatePixelDims(t,i,this.canvas),this.canvas.width=this.pixelWidth,this.canvas.height=this.pixelHeight,this.interact=o,this.shapes={},this.shapeseq=[],this.currentTargetShapeId=l,n(this.canvas).css({width:this.pixelWidth,height:this.pixelHeight})},_getContext:function(e,t,n){var i=this.canvas.getContext("2d");return e!==l&&(i.strokeStyle=e),i.lineWidth=n===l?1:n,t!==l&&(i.fillStyle=t),i},reset:function(){var e=this._getContext();e.clearRect(0,0,this.pixelWidth,this.pixelHeight),this.shapes={},this.shapeseq=[],this.currentTargetShapeId=l},_drawShape:function(e,t,n,i,a){var o,r,s=this._getContext(n,i,a);for(s.beginPath(),s.moveTo(t[0][0]+.5,t[0][1]+.5),o=1,r=t.length;r>o;o++)s.lineTo(t[o][0]+.5,t[o][1]+.5);n!==l&&s.stroke(),i!==l&&s.fill(),this.targetX!==l&&this.targetY!==l&&s.isPointInPath(this.targetX,this.targetY)&&(this.currentTargetShapeId=e)},_drawCircle:function(e,n,i,a,o,r,s){var c=this._getContext(o,r,s);c.beginPath(),c.arc(n,i,a,0,2*t.PI,!1),this.targetX!==l&&this.targetY!==l&&c.isPointInPath(this.targetX,this.targetY)&&(this.currentTargetShapeId=e),o!==l&&c.stroke(),r!==l&&c.fill()},_drawPieSlice:function(e,t,n,i,a,o,r,s){var c=this._getContext(r,s);c.beginPath(),c.moveTo(t,n),c.arc(t,n,i,a,o,!1),c.lineTo(t,n),c.closePath(),r!==l&&c.stroke(),s&&c.fill(),this.targetX!==l&&this.targetY!==l&&c.isPointInPath(this.targetX,this.targetY)&&(this.currentTargetShapeId=e)},_drawRect:function(e,t,l,n,i,a,o){return this._drawShape(e,[[t,l],[t+n,l],[t+n,l+i],[t,l+i],[t,l]],a,o)},appendShape:function(e){return this.shapes[e.id]=e,this.shapeseq.push(e.id),this.lastShapeId=e.id,e.id},replaceWithShape:function(e,t){var l,n=this.shapeseq;for(this.shapes[t.id]=t,l=n.length;l--;)n[l]==e&&(n[l]=t.id);delete this.shapes[e]},replaceWithShapes:function(e,t){var l,n,i,a=this.shapeseq,o={};for(n=e.length;n--;)o[e[n]]=!0;for(n=a.length;n--;)l=a[n],o[l]&&(a.splice(n,1),delete this.shapes[l],i=n);for(n=t.length;n--;)a.splice(i,0,t[n].id),this.shapes[t[n].id]=t[n]},insertAfterShape:function(e,t){var l,n=this.shapeseq;for(l=n.length;l--;)if(n[l]===e)return n.splice(l+1,0,t.id),this.shapes[t.id]=t,void 0},removeShapeId:function(e){var t,l=this.shapeseq;for(t=l.length;t--;)if(l[t]===e){l.splice(t,1);break}delete this.shapes[e]},getShapeAt:function(e,t,l){return this.targetX=t,this.targetY=l,this.render(),this.currentTargetShapeId},render:function(){var e,t,l,n=this.shapeseq,i=this.shapes,a=n.length,o=this._getContext();for(o.clearRect(0,0,this.pixelWidth,this.pixelHeight),l=0;a>l;l++)e=n[l],t=i[e],this["_draw"+t.type].apply(this,t.args);this.interact||(this.shapes={},this.shapeseq=[])}}),A=a(M,{init:function(t,l,i){var a;A._super.init.call(this,t,l,i),i[0]&&(i=i[0]),n.data(i,"_jqs_vcanvas",this),this.canvas=e.createElement("span"),n(this.canvas).css({display:"inline-block",position:"relative",overflow:"hidden",width:t,height:l,margin:"0px",padding:"0px",verticalAlign:"top"}),this._insert(this.canvas,i),this._calculatePixelDims(t,l,this.canvas),this.canvas.width=this.pixelWidth,this.canvas.height=this.pixelHeight,a='<v:group coordorigin="0 0" coordsize="'+this.pixelWidth+" "+this.pixelHeight+'"'+' style="position:absolute;top:0;left:0;width:'+this.pixelWidth+"px;height="+this.pixelHeight+'px;"></v:group>',this.canvas.insertAdjacentHTML("beforeEnd",a),this.group=n(this.canvas).children()[0],this.rendered=!1,this.prerender=""},_drawShape:function(e,t,n,i,a){var o,r,s,c,u,d,h,f=[];for(h=0,d=t.length;d>h;h++)f[h]=""+t[h][0]+","+t[h][1];return o=f.splice(0,1),a=a===l?1:a,r=n===l?' stroked="false" ':' strokeWeight="'+a+'px" strokeColor="'+n+'" ',s=i===l?' filled="false"':' fillColor="'+i+'" filled="true" ',c=f[0]===f[f.length-1]?"x ":"",u='<v:shape coordorigin="0 0" coordsize="'+this.pixelWidth+" "+this.pixelHeight+'" '+' id="jqsshape'+e+'" '+r+s+' style="position:absolute;left:0px;top:0px;height:'+this.pixelHeight+"px;width:"+this.pixelWidth+'px;padding:0px;margin:0px;" '+' path="m '+o+" l "+f.join(", ")+" "+c+'e">'+" </v:shape>"},_drawCircle:function(e,t,n,i,a,o,r){var s,c,u;return t-=i,n-=i,s=a===l?' stroked="false" ':' strokeWeight="'+r+'px" strokeColor="'+a+'" ',c=o===l?' filled="false"':' fillColor="'+o+'" filled="true" ',u='<v:oval  id="jqsshape'+e+'" '+s+c+' style="position:absolute;top:'+n+"px; left:"+t+"px; width:"+2*i+"px; height:"+2*i+'px"></v:oval>'},_drawPieSlice:function(e,n,i,a,o,r,s,c){var u,d,h,f,p,m,g,v;if(o===r)return"";if(r-o===2*t.PI&&(o=0,r=2*t.PI),d=n+t.round(t.cos(o)*a),h=i+t.round(t.sin(o)*a),f=n+t.round(t.cos(r)*a),p=i+t.round(t.sin(r)*a),d===f&&h===p){if(r-o<t.PI)return"";d=f=n+a,h=p=i}return d===f&&h===p&&r-o<t.PI?"":(u=[n-a,i-a,n+a,i+a,d,h,f,p],m=s===l?' stroked="false" ':' strokeWeight="1px" strokeColor="'+s+'" ',g=c===l?' filled="false"':' fillColor="'+c+'" filled="true" ',v='<v:shape coordorigin="0 0" coordsize="'+this.pixelWidth+" "+this.pixelHeight+'" '+' id="jqsshape'+e+'" '+m+g+' style="position:absolute;left:0px;top:0px;height:'+this.pixelHeight+"px;width:"+this.pixelWidth+'px;padding:0px;margin:0px;" '+' path="m '+n+","+i+" wa "+u.join(", ")+' x e">'+" </v:shape>")},_drawRect:function(e,t,l,n,i,a,o){return this._drawShape(e,[[t,l],[t,l+i],[t+n,l+i],[t+n,l],[t,l]],a,o)},reset:function(){this.group.innerHTML=""},appendShape:function(e){var t=this["_draw"+e.type].apply(this,e.args);return this.rendered?this.group.insertAdjacentHTML("beforeEnd",t):this.prerender+=t,this.lastShapeId=e.id,e.id},replaceWithShape:function(e,t){var l=n("#jqsshape"+e),i=this["_draw"+t.type].apply(this,t.args);l[0].outerHTML=i},replaceWithShapes:function(e,t){var l,i=n("#jqsshape"+e[0]),a="",o=t.length;for(l=0;o>l;l++)a+=this["_draw"+t[l].type].apply(this,t[l].args);for(i[0].outerHTML=a,l=1;l<e.length;l++)n("#jqsshape"+e[l]).remove()},insertAfterShape:function(e,t){var l=n("#jqsshape"+e),i=this["_draw"+t.type].apply(this,t.args);l[0].insertAdjacentHTML("afterEnd",i)},removeShapeId:function(e){var t=n("#jqsshape"+e);this.group.removeChild(t[0])},getShapeAt:function(e){var t=e.id.substr(8);return t},render:function(){this.rendered||(this.group.innerHTML=this.prerender,this.rendered=!0)}})})}(document,Math);
@extends('layouts.login')

@section('page-title', 'Login')

@section('content')
<div class="page-content vertical-align-middle animation-slide-top animation-duration-1">
    <div class="panel">
        <div class="panel-heading">
            <div class="brand">
                @if (Environment::getLogoUrl())
                    <img class="img-fluid" src="{{ Environment::getLogoUrl() }}" alt="logo">
                @else
                    <h2 class="brand-text font-size-18">{{ Environment::getLogoTitle() }}</h2>
                @endif
            </div>
        </div>

        @include('spark::shared.errors')

        <div class="panel-body">
            <form role="form" method="POST" action="{{ url('/login') }}">

                {{ csrf_field() }}

                <div class="form-group form-material floating" data-plugin="formMaterial">
                    <input type="email" class="form-control" name="email" value="{{ old('email') }}" autofocus>
                    <label class="floating-label">Email</label>
                </div>
                <div class="form-group form-material floating" data-plugin="formMaterial">
                    <input type="password" class="form-control" name="password">
                    <label class="floating-label">Password</label>
                </div>
                <div class="form-group clearfix">
                    <div class="checkbox-custom checkbox-inline checkbox-primary checkbox-lg float-left">
                        <input type="checkbox" id="inputCheckbox" name="remember">
                        <label for="inputCheckbox">Remember me</label>
                    </div>
                    <a class="float-right" href="{{ url('/password/reset') }}">Forgot password?</a>
                </div>
                <button type="submit" class="btn btn-primary btn-block btn-lg mt-40">Sign in</button>
            </form>
            <p>Still no account? Please go to <a href="{{ url('/register') }}">Sign up</a></p>
        </div>
    </div>

    <footer class="page-copyright page-copyright-inverse">
        {{--<p>{{ Environment::getLogoTitle() }} © {{ date('Y') }}. All RIGHTS RESERVED.</p>--}}
        <p>Copyright © {{ date('Y') }} {{ Environment::getLogoTitle() }}. All Rights Reserved</p>
    </footer>
</div>
@endsection

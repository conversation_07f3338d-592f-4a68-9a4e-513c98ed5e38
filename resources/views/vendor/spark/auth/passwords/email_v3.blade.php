@extends('layouts.login')

@section('page-title', 'Reset Password')

<!-- Main Content -->
@section('content')
    <div class="page-content vertical-align-middle animation-slide-top animation-duration-1">
        <div class="panel">
            <div class="panel-heading">
                <div class="brand">
                    <h4>Reset Password</h4>
                </div>
            </div>

            @if (session('status'))
                <div class="alert alert-success text-left">
                    {{ session('status') }}
                </div>
            @endif

            <div class="panel-body">
                <form role="form" method="POST" action="{{ url('/password/email') }}">

                    {!! csrf_field() !!}

                    <div class="form-group{{ $errors->has('email') ? ' has-error' : '' }} form-material floating" data-plugin="formMaterial">
                        <input type="email" class="form-control" name="email" value="{{ old('email') }}" autofocus>
                        <label class="floating-label">Email Address</label>
                        @if ($errors->has('email'))
                            <span class="help-block error text-left">
                                {{ $errors->first('email') }}
                            </span>
                        @endif
                    </div>
                    <button type="submit" class="btn btn-primary btn-block btn-lg mt-40">
                        <i class="fa fa-btn fa-envelope"></i>Send Password Reset Link
                    </button>
                </form>
            </div>
        </div>

        <footer class="page-copyright page-copyright-inverse">
            {{--<p>{{ Environment::getLogoTitle() }}© {{ date('Y') }}. All RIGHTS RESERVED.</p>--}}
            <p>Copyright © {{ date('Y') }} {{ Environment::getLogoTitle() }}. All Rights Reserved</p>
        </footer>
    </div>
@endsection

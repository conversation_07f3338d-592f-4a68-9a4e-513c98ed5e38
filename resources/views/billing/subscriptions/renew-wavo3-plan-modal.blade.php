<!-- Modal -->
<wavo3-renew
	:user="user"
	:team="team"
	:card-summary="cardSummary"
  :plan="dashboardPlan"
	from="{{request()->from}}"
	app_env="{{ config('app.env') }}"
	inline-template
>
	<div>
		<div class="modal fade" id="renewWavo3PlanModal" tabindex="-1" role="dialog" aria-labelledby="renewWavo3PlanModal" aria-hidden="true">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
            <h4 class="modal-title w-full" id="exampleModalLabel">
							<span class="pull-right" v-if="plan.interval == 'monthly'">
								$@{{ plan.price }} Monthly
							</span>
              <span class="pull-right" v-else>
                $@{{ plan.price }} Yearly
              </span>
              @{{ plan.name }} Plan Details
            </h4>

					</div>
					<div class="modal-body">
						<div class="px-40">
							<div class="row">
                <div class="col-md-12 mb-20">
                  <p>
                    <strong>Renew early to access more Contacts</strong>
                  </p>
                  <ul class="list-unstyled">
                    <li v-for="feature in plan.features">
                      <i class="icon wb-check"></i>
                      <span class="pl-10">@{{ feature }}</span>
                    </li>
                  </ul>
                </div>


								<div class="col-md-12 text-center mb-20" v-if="hasBillingDetails">
									<button type="button" class="btn btn-default" data-dismiss="modal" aria-label="Close">
										<i class="icon wb-close hidden-lg"></i>
										<span class="hidden-md-down">Close</span>
									</button>
									<button class="btn btn-primary"
										@click="activateAgencyPlan"
										v-if="!isActivating"
									>
										<i class="fa fa-cogs"></i>
										Renew Subscription
									</button>
									<button class="btn btn-primary"
										disabled
										v-if="isActivating"
									>
										<i class="fa fa-check" v-if="isActivated"></i>
										<i class="fa fa-spinner fa-spin" v-else></i>
										Renewing Subscription
									</button>
								</div>

								<div class="col-md-12" v-else>
									<div class="alert-icon alert alert-warning">
										<i class="icon wb-alert-circle"></i>
										<strong>No payment details found</strong>
										<p>We require your payment details to activate the Agency Plan.</p>
										<p>
											<button class="btn btn-primary" @click="openPaymentFromActivate">
												<i class="fa fa-payment"></i>
												Add Payment Details
											</button>
										</p>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</wavo3-renew>

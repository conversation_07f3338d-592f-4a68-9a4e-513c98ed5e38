<div class="row mb-30 pt-20"> 
	@if(Auth::user()->isStaff() || Auth::user()->isAdmin())
		<div class="col-md-4 col-lg-6 mb-10">
	@else
		<div class="col-md-6 col-lg-8 mb-10">
	@endif
		<input type="text" class="form-control" 
			v-model="filters.keywords" 
			placeholder="Search"
			@keyup="filterByKeywords()"
		>
	</div>

	@if(Auth::user()->isStaff() || Auth::user()->isAdmin())
		<div class="col-md-2 mb-10">
			<select class="form-control cap text-capitalize"
				v-model="filters.agency_id" @change="getProjects(1)"
				data-plugin="selectpicker" data-style="btn-outline btn-default"
			>
				<option value="0">All Agencies</option>
				<option v-for="(agency, index) in agencies" :value="agency.hashid">
					@{{agency.name}}
				</option>
			</select>
		</div>
	@endcan
	<div class="col-md-3 col-lg-2 mb-10">
		<select v-model="filters.status" class="form-control" data-plugin="selectpicker"
				data-style="btn-outline btn-default" @change="getProjects(1)"
		>
			<option value="ALL">All Status</option>
			<option value="NOT_IS_ARCHIVED">All Except Archived</option>
			<option value="DRAFT">Draft</option>
			<option value="RUNNING">Running</option>
			<option value="STOPPED">Stopped</option>
			<option value="COMPLETED">Complete</option>
			<option value="ARCHIVED">Archived</option>
		</select>
	</div>

	<div class="col-md-3 col-lg-2 mb-10">
		<select v-model="filters.sorting" class="form-control" data-plugin="selectpicker"
				data-style="btn-outline btn-default" @change="getProjects(1)"
		>
			<option value="STATUS">Sort By Status</option>
			<option value="AGENCY_MESSAGE">Sort By Last Agency Message</option>
			<option value="STAFF_MESSAGE">Sort By Last Staff Message</option>
			<option value="ALPHA">Sort Alphabetically</option>
			<option value="NEWEST">Sort By Newest</option>
			<option value="OLDEST">Sort By Oldest</option>
		</select>
	</div>
</div>
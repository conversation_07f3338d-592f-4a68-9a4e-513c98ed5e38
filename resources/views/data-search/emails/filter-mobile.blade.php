<div class="row py-10">
    <div class="col-sm-8">
        <select class="form-control"  
            data-placeholder="All searches"
            data-allow-clear="true"
            @change="searchFilterSelected"
            v-model="searchFilterId"
            id="searchFilterSelect"
        >
            <option value="">All searches</option>
            <option v-for="search in searches" :value="search.id">
                @{{search.name}}
            </option>
        </select>
    </div>
    <div class="col-sm-4">
        <button class="btn btn-block btn-default" @click="showAllFilters = true" v-if="!showAllFilters">
            Show More Filters
        </button>
        <button class="btn btn-block btn-default" @click="showAllFilters = false" v-else>
            Show Less Filters
        </button>
    </div>
</div>
<div class="row" v-if="showAllFilters">
    
    <div class="col-lg-3 col-md-6 col-sm-6 col-12 mb-10">
        <div class="card card-shadow mb-20 border">
            <div class="card-block p-10">
                <div class="tabbed-inbox-filter-btn" v-if="filters?.categories">
                    <h5 class="clearfix m-0 mb-10">
                        <span class="d-inline-block pt-5">
                            @{{filters?.categories?.name}}
                        </span>
                    </h5>
                    
                    <div class="filter-option-wrapper tabbed-inbox-scrollable">
                        <div v-for="(level1, lvl1Index) in filters?.categories?.options">
                            <template>
                                <label type="button" class="btn btn-block btn-outline clearfix py-3 mb-1"
                                    :class="level1.checked ? 'btn-primary' : 'btn-default'"
                                    :for="'category_level_1_'+lvl1Index"
                                >
                                    <div class="checkbox-custom checkbox-primary my-3">
                                        <input type="checkbox" 
                                            :id="'category_level_1_'+lvl1Index" 
                                            v-model="level1.checked" 
                                            :checked="level1.checked"
                                            @change="categoryFilterToggle"
                                        >
                                        <label :for="'category_level_1_'+lvl1Index">
                                            @{{level1.name}}
                                        </label>
                                    </div>
                                </label>

                                <div v-if="level1.checked">
                                    <div class="py-0 pl-20"
                                        v-for="(level2, lvl2Index) in level1?.children"
                                    >
                                        <label type="button" class="btn btn-block btn-outline clearfix py-3 mb-1"
                                            :class="level2.checked ? 'btn-primary' : 'btn-default'"
                                            :for="'category_level_1_'+lvl1Index+'_2'+lvl2Index"
                                        >
                                            <div class="checkbox-custom checkbox-primary my-3">
                                                <input type="checkbox" 
                                                    :id="'category_level_1_'+lvl1Index+'_2'+lvl2Index" 
                                                    v-model="level2.checked" 
                                                    :checked="level2.checked"
                                                    @change="emailFilterToggle"
                                                >
                                                <label :for="'category_level_1_'+lvl1Index+'_2'+lvl2Index">
                                                    @{{level2.name}}
                                                </label>
                                            </div>
                                        </label>

                                        <div v-if="level2.checked">
                                            <div class="py-0 pl-20"
                                                v-for="(level3, lvl3Index) in level2?.children"
                                            >
                                                <label type="button" class="btn btn-block btn-outline clearfix py-3 mb-1"
                                                    :class="level3.checked ? 'btn-primary' : 'btn-default'"
                                                    :for="'category_level_1' + lvl1Index+ '_2' + lvl2Index + '_3' + lvl3Index"
                                                >
                                                    <div class="checkbox-custom checkbox-primary my-3">
                                                        <input type="checkbox" 
                                                            :id="'category_level_1' + lvl1Index+ '_2' + lvl2Index + '_3' + lvl3Index" 
                                                            v-model="level3.checked" 
                                                            :checked="level3.checked"
                                                            @change="emailFilterToggle"
                                                        >
                                                        <label :for="'category_level_1' + lvl1Index+ '_2' + lvl2Index + '_3' + lvl3Index">
                                                            @{{level3.name}}
                                                        </label>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 col-sm-6 col-12 mb-10"
        v-for="(filterType, filterIndex) in filterTypes"
        v-if="filters[filterType] && filters[filterType]?.options?.length"
    >
        <div class="card card-shadow mb-20 border">
            <div class="card-block p-10">
                <div class="tabbed-inbox-filter-btn">
                    <h5 class="clearfix m-0 mb-10">
                        <span class="d-inline-block pt-5">
                            @{{filters[filterType]?.name}}
                        </span>
                    </h5>
                    
                    <div class="filter-option-wrapper tabbed-inbox-scrollable">
                        <div v-for="(filter, filterIndex) in filters[filterType]?.options">
                            <template>
                                <label type="button" class="btn btn-block btn-outline clearfix py-3 mb-1"
                                    :class="filter.checked ? 'btn-primary' : 'btn-default'"
                                    :for="filterType+'_'+filterIndex"
                                >
                                    <div class="checkbox-custom checkbox-primary my-3">
                                        <input type="checkbox" 
                                            :id="filterType+'_'+filterIndex" 
                                            v-model="filter.checked" 
                                            :checked="filter.checked"
                                            @change="emailFilterToggle"
                                        >
                                        <label :for="filterType+'_'+filterIndex">
                                            @{{filter.name ?? filter.code}}
                                        </label>
                                    </div>
                                </label>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-12">
        <p class="text-center">
            <button class="btn btn-default" @click="showAllFilters = false">
                Show Less Filters
            </button>
        </p>
    </div>
</div>


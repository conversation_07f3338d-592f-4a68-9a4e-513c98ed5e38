{{--@extends('layouts.mailbox')--}}
@extends('layouts.app')

@section('page-title', 'Campaign: ' . $campaign->name)

@section('page-styles')
  <style>
    .nav-newemail {
        cursor: pointer;
    }
    .delete-template-email-btn {
        position: absolute;
        top: -16px;
        right: 30px;
    }
    .table-csvupload {
        width: 100%;
        table-layout: fixed;
    }
    .table-csvupload th, .table-csvupload td {
        width: 200px;
    }
    .prospect-table-csv {
        overflow-x: scroll;
    }
    .form-control-prospect {
        border-color: rgba(0,0,0,0);
        height: 23px !important;
        padding-top: 0px;
        padding-bottom: 0px;
        background-color: rgba(255,255,255,0.5) !important;
    }
    .form-control-prospect:hover {
        border-color: default;
    }
    .form-control-prospect.loading-input {
        background-repeat: no-repeat;
        background-position: 100% 50%;
        background-size: 20px;
        background-image: url({{ asset('/img/progress/progress-circle-complete.svg') }});
    }
    .form-control-prospect.loading-error{
        border-color: #ff4c52;
    }
    .form-control-prospect.loading-warning{
        border-color: #ffc107;
    }
    .head-icon-desc,
    .head-icon-asc {
        display: none;
    }

    .sort.sort-asc .head-icon-asc {
        display: inline-block;
    }
    .sort.sort-desc .head-icon-desc {
        display: inline-block;
    }
    .mb2 {
        margin-bottom: 2.143rem;
    }
    .stickybar .nav.nav-tabs {
        position: fixed;
        top: 110px;
        width: 100%;
        z-index: 1100;
        background: #fff;
    }
    .stickybar .tab-content {
        padding-top: 45px;
    }
    .emailtemplate-tabbar .nav-link .nav-link-loading,
    .emailtemplate-tabbar .nav-link .nav-link-loaded,
    .emailtemplate-tabbar .nav-link .nav-link-loaded-err{
        display: none;
    }

    .emailtemplate-tabbar .nav-link.nav-loading .nav-link-loading{
        display: inline-block;
    }
    .emailtemplate-tabbar .nav-link.nav-loaded .nav-link-loaded{
        display: inline-block;
    }
    .emailtemplate-tabbar .nav-link.nav-loaded-err .nav-link-loaded-err{
        display: inline-block;
    }
    .form-control:disabled {
        /*background-color: #868e96;*/
    }

    .campaignEmailsTab .nav-link {
        width: 120px;
        text-align: center;
        line-height: 25px;
    }
    .campaignEmailsTab .nav-link.nav-loading .nav-link-text,
    .campaignEmailsTab .nav-link.nav-loaded .nav-link-text,
    .campaignEmailsTab .nav-link.nav-loaded-err .nav-link-text {
        display: none;
    }

    .btn-blue {
        color: #fff;
        background-color: #007bff;
    }
    .btn-yellow {
        color: #111;
        background-color: #ffc107;
    }

    .slideout-panel .slideout-wrapper .slideout {
        transition: transform .35s ease-out !important;
        transform: translate3d(0%, 0px, 0px);
        box-shadow: -10px 0 20px 0 rgba(66, 66, 66, .2);
    }

    .tag {
      font-size: 85%;
    }

    .card-new {
      border: 1px solid #e0e0e0 !important;
      background-color: #fff;
    }

    .thread-new {
      color: #37474f;
    }

    @media (max-width: 1220px) {
        .time .delivery-list-item .checkbox-custom {

        }
        .time label.schedule {
            width: 35px;
            font-size: 12px;
            padding-right: 5px;
            padding-left: 5px;
            line-height: 22px;
        }
        .time .form-control {
            width: 25px;
            font-size: 12px;
            line-height: 12px;
            height: 22px !important;
        }
    }

    .thread-message-box {
      border-radius: 3px;
      box-shadow: 0 0 1px rgba(0,0,0,0.2);
    }
    .thread-message-head {
      border-bottom: 1px solid rgba(0,0,0,0.1);
      font-weight: bold;
    }
    .thread-message-body {
      font-size: 14px !important;
      font-family: "Roboto", sans-serif !important;
    }
    .thread-message-body .wv_ee_tr_op,
    .thread-message-body .ee-block-1 {
        display: none !important;
    }
    .mailContent {
      min-height: 600px;
    }
    .yahoo_quoted, .gmail_extra,
    .gmail_quote, .unsrb, .quote {
      display: none;
    }

    .display-quoted .yahoo_quoted, .display-quoted .gmail_extra,
    .display-quoted .gmail_quote, .display-quoted .unsrb, .display-quoted .quote {
        display: block !important;
    }

    .page-aside .list-group-item.active  {
        background-color: #f3f7f9;
        font-weight: bold;
    }

    .active-interest label {
      color: #eb6709;
      font-weight: bold !important;
    }

    .page-aside .list-group-item {
      cursor: pointer;
    }
    #replyEditorWrap {
      position: relative;
    }
    #replyEditorBox {
      z-index: 10;
    }
    #replyEditorBtn {
      position: absolute;
      top: 3px;
      right: 3px;
      z-index: 10000;
    }
    .thread-interest-wrap {
      width: 110px;
      text-align: center;
    }
    .thread-interest-item {
      height: 22px;
      overflow: hidden;
    }
    .thread-interest-icon {
      width: 22px;
      height: 22px;
      border: 1px solid rgba(0,0,0,0.3);
      display: inline-block;
      margin: 0 1px;
      border-radius: 5px;
      overflow: hidden;
      opacity: 0.3;
      vertical-align: top;
    }
    .thread-interest-icon i {
      font-size: 12px;
      line-height: 12px;
    }
    .time {
      font-size: 12px;
      line-height: 12px;
      text-align: center;
      margin-bottom: 5px;
    }
    .thread-interest-icon:hover {
      opacity: 1;
    }
    .thread-interest-icon.active {
      opacity: 1 !important;
      border: 1px solid rgba(0,0,0,0.3);
      box-shadow: 0 0 5px rgba(0,0,0,0.2)
    }
    .resched-datepicker {
      border: none;
      padding: 4px;
      font-size: 12px;
      line-height: 12px;
      width: 90px;
      text-align: center;
    }
    .font13 {
      font-size: 13px;
      line-height: 13px;
    }

    .radio-interest {
      background-color: transparent;
      border: 1px solid #e4eaec;
      padding: 5px 10px;
      font-size: 12px;
      line-height: 18px;
      border-radius: 2px;
    }
    .radio-interest.radio-custom input[type="radio"] {
      margin-left: 0px;
    }
    .radio-interest.radio-custom label {
      padding-left: 25px;
    }
    .radio-interest.radio-custom label::before {
      margin-left: 0px;
    }
    .radio-interest.radio-custom label::after {
      margin-left: 0px;
    }
    .radio-interest.active-interest {
      border-color: #526069;
    }
    .bg-grey-150 {
      background-color: #f4f4f4 !important;
    }
    .btn-default.btn-outline:hover .badge,
    .btn-default.btn-outline:focus .badge,
    .btn-default.btn-outline:active .badge {
        color: #fff !important;
    }
    .list-campaign-thread.list-group-item.active
    .list-campaign-thread.list-group-item.active .btn-default{
        color: #fff !important;
    }
    .list-campaign-thread {
        cursor: pointer;
    }

    .loader-wrapper {
        position: relative;
    }
    .loader-box {
        position: absolute;
        height: 100%;
        width: 100%;
        top: 0px;
        left: 0px;
        background: rgba(255,255,255,0.5);
        display: none;
    }
    .loader-wrapper.loader-wrapper-loading .loader-box {
        display: block;
    }

    .filter-group-btn-md,
    .filter-group-header,
    .filter-group-btn-md {
        display: none;
    }

    .tabbed-inbox-wrap {
        position: relative;
        padding-left: 250px;
        overflow: hidden;
    }
    .tabbed-inbox-filters {
        position: absolute;
        top: 0;
        left: 0;
        width: 250px;
    }
    .tabbed-inbox-threads {
        border-left: 1px solid #e4eaec;
        min-height: 600px;
        margin-left: 5px;
    }

    .tabbed-inbox-filters h4 {
        color: #526069;
        font-size: 14px;
        padding: 0 12px;
        margin: 5px 0;
    }


    .tabbed-inbox-filter-btn {
        border-top: 1px solid #e4eaec;
    }
    .tabbed-inbox-filter-btn:first-child {
        border-top: none;
    }
    .tabbed-inbox-filter-btn .btn {
        text-align: left;
        /*background: #fff;*/
        /*border-color: #fff;*/
        margin: 0px;
        padding: 5px 12px;
        /*color: #76838f;*/
        border: none;
    }
    .tabbed-inbox-filter-btn .btn.btn-primary, .tabbed-inbox-filter-btn .btn:hover {
        background-color: #f3f7f9;
    }

    .tabbed-inbox-filter-btn .btn:focus {
      background-color: #f3f7f9;
      color: inherit;
    }

    .tabbed-inbox-filter-btn .btn.btn-active {
        color: #eb6709;
        background-color: #e4eaec;
        font-weight: bold;
    }

    .tabbed-inbox-scrollable {
        overflow-y: scroll;
    }
    .tabbed-inbox-scrollable::-webkit-scrollbar-track
    {
        /*-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);*/
        border-radius: 10px;
        background-color: #F5F5F5;
    }

    .tabbed-inbox-scrollable::-webkit-scrollbar
    {
        width: 4px;
        background-color: #F5F5F5;
    }

    .tabbed-inbox-scrollable::-webkit-scrollbar-thumb
    {
        border-radius: 10px;
        -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.2);
        background-color: #c2ccd2;
    }
    .tabbed-inbox-scrollable .scrollable-container,
    .tabbed-inbox-scrollable .scrollable-content {
        /*width: 100% !important;*/
    }

    .editor-toggle-box {
        position: relative;
    }
    .editor-toggle-box .vue-html5-editor {

    }
    .editor-toggle-box .editor-toggle-btn {
        position: absolute;
        z-index: 1010;
        top: 3px;
        right: 3px;
    }
    .editor-toggle-box .editor-toggle-textarea {
        min-height: 300px;
    }
    .editor-toggle-toolbar {
        height: 37px;
        line-height: 37px;
        padding: 0px 20px;
        margin: 0px;
        border: 1px solid #ddd;
        border-radius: 5px;
    }

    .loader-wrapper {
        position: relative;
    }
    .loader-box {
        position: absolute;
        height: 100%;
        width: 100%;
        top: 0px;
        left: 0px;
        background: rgba(255,255,255,0.5);
        display: none;
    }
    .loader-wrapper.loader-wrapper-loading .loader-box {
        display: block;
    }

    .input-outline-primary {
        color: #eb6709;
        background-color: transparent;
        border-color: #eb6709;
    }

    .cursor-default {
        cursor: default !important;
    }
    .checkbox-custom.checkbox-bordered label::before {
        border-color: #eb6709 !important;
    }

    .header-delete-mode-filters label span {
        display: none;
    }
    .header-delete-mode-filters .w-180,
    .header-delete-mode-filters .w-120 {
        width: 100px !important;
    }

    .header-delete-mode {
        opacity: 0.5;
        display: none;
    }
    .header-delete-mode:hover {
        opacity: 1;
    }

    .campaign-header-tab {
        display: inline-block;
        float: right;
    }

    .prospect-header-addbtn {
        display: inline-block;
        float: right;
    }
    .prospect-header-filterbtn {
        display: inline-block;
        float: left;
    }
    .prospect-header-filterbtn .w-120 {
        width: 130px !important;
        margin-top: 20px !important;
        margin-bottom: 20px !important;
    }
    .prospect-header-filterbtn .w-180 {
        margin-top: 20px !important;
        margin-bottom: 20px !important;
    }
    .secondary-addbtn {
        display: none;
    }
    .secondary-addbtn span {
        display: none;
    }
    .prospect-header-selectedbtn {
        display: block;
        margin-bottom: 27px;
        margin-top: 35px
    }
    #exportFormAll {

    }

    .emailaccnt-label-hourly {
        text-align: center;
    }
    .emailaccnt-label-interval {
        text-align: right;
    }
    .emailDetailsWrap {

    }
    .emailDetailsWrap {

    }
    .email-details-closed {
        display: inline-block;
    }
    .email-details-opened {
        display: none;
    }
    .emailDetailsBox {
        display: none;
    }
    .email-sender-box {
        opacity: 1;
        display: block;
    }
    .toggleEmailDetails {
        position: relative;
        z-index: 3;
    }
    .emailDetailsWrap.emailDetailsWrap-open {

    }
    .emailDetailsWrap.emailDetailsWrap-open .email-details-closed {
        display: none;
    }
    .emailDetailsWrap.emailDetailsWrap-open .email-details-opened {
        display: inline-block;
    }
    .emailDetailsWrap.emailDetailsWrap-open  .emailDetailsBox {
        /*display: block;*/
    }
    .emailDetailsWrap.emailDetailsWrap-open  .email-sender-box {
        opacity: 0;
    }

    .trumbowyg-modal.trumbowyg-fixed-top {
        margin-top: -100px !important
    }

    .prospecttable-subform {
        min-height: 44px;
        border-bottom: 1px solid rgba(0,0,0,0.1);
        border-left: 1px solid rgba(0,0,0,0.1);
        padding: 8px;
        height: 34px;
        background: #f3f7f9;
    }
    .prospecttable-subform .form-control-prospect {
        height: 28px !important;
        border-color: #e4eaec !important;
        background-color: #fff !important;
    }
    .w-175 {
        width: 175px !important;
    }
    .w-175.prospecttable-item select {
        background-position: 137px 50% !important;
    }

    .card-group-campaign.card-group .card {
        flex: auto !important;
    }
    .counter-number.campaign-client-name {
        max-width: 350px;
        display: block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .linkedin-thumbnails {
        width: 24px;
        height: 24px;
        margin-right: 2px;
    }
    .linkedin-thumbnails-initials {
        background: #E8E5DE;
        line-height: 25px;
        text-align: center;
        font-size: 11px;
        display: inline-block;
        text-transform: uppercase;
    }
    .thread .linkedin-thumbnails {
        position: absolute;
        top: 20px;
        left: 14px;
        width: 32px;
        height: 32px;
        padding:0;
    }
    .thread .linkedin-thumbnails-initials {
        position: absolute;
        top: 20px;
        left: 14px;
        width: 32px;
        height: 32px;
        padding:0;
        line-height: 32px;
        font-size: 14px;
    }
    .prospecttable-item-firstname {
        padding-left: 35px;
        position: relative;
    }
    .prospecttable-item-firstname .linkedin-thumbnails {
        position: absolute;
        top: 5px;
        left: 10px;
    }
    .prospecttable-item-lastname {
        padding-right: 40px;
        position: relative;
    }
    .prospecttable-item-lastname a {
        position: absolute;
        top: 5px;
        right: 10px;
    }
    .prospect-free-open {
        color: #B09B61;
        position: absolute;
        right: 26px;
        top: 5px;
    }
    .import-free-open {
        color: #B09B61;
        float: right;
    }

    @media (max-width: 1250px) {
        .header-delete-mode-filters .w-180,
        .header-delete-mode-filters .w-120 {
            width: 90px !important;
        }
    }

    @media (max-width: 1220px) {
        .prospect-header-addbtn .btn span {
            display: none;
        }

        .header-delete-mode-filters .w-180,
        .header-delete-mode-filters .w-120 {
            width: 90px !important;
        }
    }

    @media (max-width: 1050px) {
        .stage-header .stage-head-div.stage-head-div-1 span {
            display: none;
        }
        .stage-head-div-1 {
            display: block;
            padding-top: 10px;
        }

        .step-template-btnbox {
            padding-top: 15px;
        }
        .w-120 {
            /*width: 90px !important;*/
        }
        .filter-item-perpage {
            display: none !important;
        }
    }

    @media (max-width: 992px) {
        .prospect-importform-footer .col-lg-6 {
            text-align: center !important;
        }

        .template-schedule-panel {
            padding: 10px !important;
        }

        .prospect-header-filterbtn .w-120 {
            width: 120px !important;
        }

        .prospect-header-selectedbtn span {

        }

        #exportFormAll {

        }

        .header-delete-mode-filters .w-180,
        .header-delete-mode-filters .w-120 {
            width: 90px !important;
        }
        .hidden-filters-md {
            display: none !important;
        }
    }

    @media (max-width: 900px) {
        .prospect-header-selectedbtn {

        }
        .prospect-header-addbtn {
            display: none;
            float: none;
        }
        .prospect-header-filterbtn {
            display: block;
            float: none;
        }

        .secondary-addbtn {
            display: block;
            width: 8% !important;
            margin-left: 1%;
            margin-right: 1%;
            float: left !important;
            margin-top: 2px !important;
            margin-bottom: 2px !important;
        }

        .prospect-header-filterbtn .w-180 {
            display: block;
            width: 98% !important;
            margin-left: 1%;
            margin-right: 1%;
            margin-top: 2px !important;
            margin-bottom: 2px !important;
        }
        .prospect-header-filterbtn .w-120 {
            width: 18% !important;
            margin-left: 1%;
            margin-right: 1%;
            display: block !important;
            float: left !important;
            margin-bottom: 2px !important;
            margin-top: 2px !important;
        }
        .prospect-header-filterbtn label {
            display: none !important;
        }
        .prospect-header-selectedbtn {
            margin-bottom: 23px;
            margin-top: 15px
        }
        .swal-modal .swal-text,
        .swal-modal .swal-footer {
            text-align: center !important;
        }
        .hidden-filters-md {
            display: none !important;
        }
    }
    @media (max-width: 850px){
        #exportFormAll {

        }
    }

    @media (max-width: 780px) {
        .filter-group-header,
        .filter-group-btn-md {
            display: block;
        }
        .filter-group-btn-md .form-control {

        }

        .tabbed-inbox-wrap { padding-left: 0px; }
        .tabbed-inbox-filters { display: none;}
        .tabbed-inbox-threads { border-left: none; }

        #exportFormAll {

        }
    }

    @media (max-width: 767px) {
        .campaign-header-tab {
            float: none;
            display: block;
            position: absolute;
            bottom: 0px;
            width: 95%;
        }
        .page-title {
            /*margin-bottom: 60px;*/
        }
        .page-header {
            padding-bottom: 10px;
        }
        #exportFormAll {

        }
        .prospect-header-selectedbtn .btn .hidden-lg-down,
        .prospect-header-selectedbtn .btn .hidden-sm-down {
            display: inline-block !important;
        }
    }
    @media (max-width: 712px) {
        #exportFormIds,
        #exportFormAll {
            /*margin-bottom: 5px;*/
        }
    }
    @media (max-width: 700px){
        .prospect-header-filterbtn .w-120 {
            width: 48% !important;
        }

        .secondary-addbtn {
            width: 48% !important;
        }

        .secondary-addbtn span {
            display: inline-block;
        }
        .prospect-importform-footer .col-lg-6 {
            text-align: left !important;
        }
        .importform-display-meta {
            display: none;
        }
        .importform-btns {
            display: block;
            text-align: center;
        }

        .col-selected {
            /*flex: 0 0 50% !important;
            max-width: 50% !important;*/
        }
    }
    @media (max-width: 600px) {
        .prospect-list-header {
            margin-bottom: 20px;
        }
        .prospect-list-header .w-180 {
            width: 100% !important;
            float: left;
            margin: 0px 0px 15px !important;
        }
        .prospect-list-header .w-120 {
            width: 50% !important;
            float: left;
            margin: 0px !important;
        }

        .stage-head-div-2 {
            display: block;
            padding-top: 10px;
        }
        .stage-head-div-2 span {
            display: none;
        }
    }
    @media (max-width: 500px) {
        .campaign-header-tab {
            width: 94%;
        }
    }
    @media (max-width: 400px) {
        .campaign-header-tab {
            width: 92%;
        }

        .prospect-header-filterbtn .w-120 {
            width: 98% !important;
        }

        .secondary-addbtn {
            width: 98% !important;
        }
    }
    .stage-add-btn {
        position: absolute;
        top: 10px;
        right: 45px;
        /*right: 245px;*/
    }
    /*.stage-delete-btn {
        position: absolute;
        right: 15px;
        top: -74px;
    }*/

    .display-more-threads.btn-outline-primary:disabled {
        background-color: transparent !important;
        color: {{ Environment::getPrimaryColorCode() }} !important;
    }
    .email-contact-label {
        display: block;
        white-space: nowrap;
    }
    .chatgpt-icon {
        /* display: inline-block; */
        position: relative;
        border-radius: 3px;
        line-height: 16px;
        height: 16px;
    }
    .chatgpt-icon img {
        height: 12px;
    }
    .tox .tox-label {
        margin-bottom: 5px !important;
        padding-top: 5px !important;
    }
    #tourImgPreload {
        visibility: hidden;
        width: 1px;
        height: 1px;
        overflow: hidden;
    }
  </style>
@endsection

@section('page-scripts')
    <script src="//rawcdn.githack.com/RickStrahl/jquery-resizable/master/dist/jquery-resizable.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/lodash@4.17.10/lodash.min.js" ></script>
    <script src="https://cdn.jsdelivr.net/npm/async@2.6.1/dist/async.min.js" ></script>
    <script src="/js/papaparse.min.js"></script>
@endsection

@section('content-layout')
<campaign-view
    :show-avatars="{{ json_encode($hasLiAvatars) }}"
    :email-histories="{{ json_encode($campaign->emailHistories()) }}"
    :linkedin-account-histories="{{ json_encode($campaign->linkedinAccountHistories()) }}"
    :prospects-count="0"
    :default-merge-fields="{{ json_encode($defaultfields) }}"
    :custom-merge-fields="{{ json_encode($snippetfields) }}"
    :merge-fields="{{ json_encode($allfields) }}"
    :campaign="{{ json_encode($campaign) }}"
    progress="{{ $campaign->getCompletedPercentage() }}"
    :stats="{{ json_encode($campaign->stats) }}"
    :tags="{{ json_encode($tags) }}"
    agency-plan="{{ Auth::user()->agency->current_billing_plan }}"
    :has-linkedin-outreach="{{Auth::user()->agency->has_linkedin_outreach}}"
    :agency="{{Auth::user()->agency}}"
    inline-template
>
    <div class="page">

        <div class="page-header page-header-bordered">
            <h1 class="page-title">
                Campaign Details
            </h1>
            <p class="page-description m-0 text-capitalize">
              <span v-pre>{{$campaign->name}}</span>
            </p>
            <div class="page-header-actions">
                <div class="spark-settings-tabs">
                    <ul role="tablist" class="nav nav-fill spark-settings-stacked-tabs nav-pills nav-round">
                        <li role="presentation" class="nav-item">
                            <a href="#campaign_details" aria-controls="campaign_details"
                                role="tab" data-toggle="tab"
                                class="nav-link active border border-primary ml-3 tour-box tour-box-campaignnav tour-box-campaignnav-details"
                                aria-selected="true"
                                id="campaign_details_btn"
                            >
                                <i class="hidden-lg icon wb-list"></i>
                                <span class="hidden-md-down text-uppercase">Details</span>
                            </a>
                        </li>
                        <li role="presentation" class="nav-item">
                            <a href="#campaign_emails" aria-controls="campaign_emails"
                                role="tab" data-toggle="tab"
                                class="nav-link border border-primary ml-3 tour-box tour-box-campaignnav tour-box-campaignnav-sequences"
                                aria-selected="false"
                                id="campaign_emails_btn"
                            >
                                <i class="hidden-lg icon wb-envelope"></i>
                                <span class="hidden-md-down text-uppercase">SEQUENCE {{-- (@{{intEmailCount}}) --}}</span>
                            </a>
                        </li>
                        @if($campaign->agency->has_chat_gpt && !empty($campaign->agency->ai_fields))
                            <li role="presentation" class="nav-item ">
                                <a href="#campaign_chatgpt"
                                    aria-controls="campaign_chatgpt"
                                    role="tab"
                                    data-toggle="tab"
                                    class="nav-link border border-primary ml-3 tour-box tour-box-campaignnav tour-box-campaignnav-chatgpt"
                                    aria-selected="false"
                                    id="campaign_chatgpt_btn"
                                >
                                    <span class="chatgpt-icon bg-primary-600 px-1 py-0 hidden-lg">
                                        <img src="/img/chatgpt.svg" alt="">
                                    </span>
                                    <span class="hidden-md-down text-uppercase">CHATGPT</span>
                                </a>
                            </li>
                        @endif
                        <li role="presentation" class="nav-item ">
                            <a href="#campaign_contacts" 
                                aria-controls="campaign_contacts"
                                role="tab" data-toggle="tab"
                                class="nav-link border border-primary ml-3 tour-box tour-box-campaignnav tour-box-campaignnav-contacts"
                                aria-selected="false"
                                id="campaign_contacts_btn"
                            >
                                <i class="hidden-lg icon wb-users"></i>
                                <span class="hidden-md-down text-uppercase">Contacts (@{{intProspectsCount}})</span>
                            </a>
                        </li>
                        <li role="presentation" class="nav-item ">
                            <a href="#campaign_inbox"
                                aria-controls="campaign_inbox"
                                role="tab" data-toggle="tab"
                                class="nav-link border border-primary ml-3 tour-box tour-box-campaignnav tour-box-campaignnav-inbox"
                                aria-selected="false"
                                id="campaign_inbox_btn"
                            >
                                <i class="hidden-lg icon wb-chat-text"></i>
                                <span class="hidden-md-down text-uppercase">Inbox</span>
                            </a>
                        </li>
                        <li role="presentation" class="nav-item ">
                            <a href="#" @click.prevent="displayCampaignTour"
                                class="nav-link border border-primary ml-3"
                            >
                                <i class="hidden-lg icon wb-info-circle"></i>
                                <span class="hidden-md-down text-uppercase">HELP</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        @if (session('status'))
            <div class="page-content container-fluid pb-0">
                <div class="mb-0 alert-dismissible alert-icon alert alert-{{session('status') == 'success' ? 'success' : 'danger'}}">
                    @if (session('status') == 'success')
                        <i class="icon wb-check-circle"></i>
                    @else
                        <i class="icon wb-alert-circle"></i>
                    @endif

                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    {{ session('msg') }}
                </div>
            </div>
        @endif

        @if($campaign->cleaned_at)
            <div class="page-content container-fluid pb-0">
                <div class="alert-icon alert alert-warning">
                    <i class="icon wb-alert-circle"></i>
                    This campaign has been ARCHIVED for more than 60 days.
                </div>
            </div>
        @endif

        @if($campaign->status == 'RUNNING' && !$campaign->max_emails_per_day)
            <div class="page-content container-fluid pb-0">
                <div class="alert-icon alert alert-danger">
                    <i class="icon wb-alert-circle"></i>
                    This campaign has 0 new contacts added to the sequence per day.
                </div>
            </div>
        @endif

        <div class="page-content container-fluid pt-0">
            <div class="nav-tabs-horizontal" data-plugin="tabs">
                <div class="tab-content pt-15">
                    <div class="tab-pane active" id="campaign_details" role="tabpanel">
                        @include('campaignsv2.details')
                    </div>
                    <div class="tab-pane" id="campaign_emails" role="tabpanel">
                        @if($campaign->status == 'RUNNING')
                            <div class="alert-icon alert alert-warning">
                                <i class="icon wb-alert-circle"></i>
                                Any changes will not be saved while the campaign is running. <strong>Pause</strong> the campaign to edit.
                            </div>
                        @endif

                        <stage-list
                            :campaign="objCampaign"
                            :campaign-status="campaignStatus"
                            :campaign-stages="campaignStages"
                            :delay-types="{{ json_encode(App\CampaignStage::DELAY_TYPES) }}"
                            :email-accounts-count="intEmailAccountCount"
                            :email-accounts="emailAccounts"
                            :linkedin-accounts-count="intLinkedinAccountCount"
                            signature="{{ $signature }}"
                            :snippets="snippetFields"
                            :fields="defaultFields"
                            color="{{ Environment::getPrimaryColorCode() }}"
                            :agency="{{Auth::user()->agency}}"
                            :importfiles="{{$importfiles}}"
                        ></stage-list>
                    </div>

                    @if($campaign->agency->has_chat_gpt && !empty($campaign->agency->ai_fields))
                        <div class="tab-pane" id="campaign_chatgpt" role="tabpanel">
                            <chatgpt-prompt-list
                                :campaign="objCampaign"
                                :chatgpt-prompts="chatgptPrompts"
                                :snippets="snippetFields"
                                :fields="defaultFields"
                                color="{{ Environment::getPrimaryColorCode() }}"
                                :agency="{{Auth::user()->agency}}"
                                :plan="{{ json_encode($plan) }}"
                            ></chatgpt-prompt-list>
                        </div>
                    @endif

                    <div class="tab-pane" id="campaign_contacts" role="tabpanel">
                        @include('campaignsv2.prospects.index')
                    </div>
                    <div class="tab-pane" id="campaign_inbox" role="tabpanel">
                        @include('campaignsv2.inbox.inbox-list')
                    </div>
                </div>
            </div>

            <slideout-panel id="template-slideout"></slideout-panel>

        </div>


        <div id="tourImgPreload"></div>
    </div>
</campaign-view>
@endsection

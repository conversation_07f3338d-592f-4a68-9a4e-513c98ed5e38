@if($account->cancelled_at)
    <div class="alert alert-icon alert-danger bg-red-100 mt-10">
        <i class="icon wb-alert-circle"></i>
        <p class="mb-0"><strong>Email Account Cancelled</strong></p>
        <p class="mb-20">
            This email account is Cancelled, do you want to re-activate?
        </p>
        <p>
            <a href="{{ route('email-accounts.reauth', $account) }}" class="btn btn-danger" >
                <i class="icon wb-reload" aria-hidden="true"></i> Re-Activate
            </a>
        </p>
    </div>
@endif

@if (session('status'))
    <div class="mb-20 alert alert-dismissible alert-{{session('status') == 'success' ? 'success' : 'danger'}}">
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
        {{ session('msg') }}
    </div>
@endif

@if($account->error == 'account.invalid')
    <div class="mb-20 alert alert-danger alert-icon">
        <i class="icon wb-alert-circle"></i>
        <p class="mb-0"><strong>Invalid credentials</strong></p>
        <p class="mb-20">Account's credentials are out of date, please reauthenticate.</p>
        <p>
            <a href="{{ route('email-accounts.reauth', $account) }} " class="btn btn-danger">
                <i class="icon wb-lock"></i> Re-authenticate
            </a>
        </p>

    </div>
@endif

@if($account->error == 'account.connecting')
    <div class="mb-20 alert alert-warning alert-icon">
        <i class="icon fa fa-circle-o-notch fa-spin"></i>
        <p class="mb-0"><strong>Checking SMTP status</strong></p>
        <p class="mb-0">Checking the account's SMTP status before it can send messages.</p>
    </div>
@endif

@if($account->error == 'account.failed')
    <div class="mb-20 alert alert-danger alert-icon">
        <i class="icon fa fa-ban"></i>
        <p class="mb-0"><strong>SMTP Failed</strong></p>
        <p class="mb-10">Account cannot send messages through SMTP.</p>
        @if($account->error_smtp_status)
            <p class="mb-0">Please fix the following SMTP issue then reauthenticate:</p>
            <p class="mb-20">{{$account->error_smtp_status}}</p>
        @endif
        <p>
            <a href="{{ route('email-accounts.reauth', $account) }} " class="btn btn-danger">
                <i class="icon wb-lock"></i> Re-authenticate
            </a>
        </p>
    </div>
@endif

@if($account->error == 'account.reauth')
    <div class="mb-20 alert alert-danger alert-icon">
        <i class="icon wb-alert-circle"></i>
        <p class="mb-0"><strong>Re-authentication incomplete.</strong></p>
        <p class="mb-20">Account's credentials are out of date, please reauthenticate.</p>
        <p>
            <a href="{{ route('email-accounts.reauth', $account) }} " class="btn btn-danger">
                <i class="icon wb-lock"></i> Re-authenticate
            </a>
        </p>
    </div>
@endif

@if($account->error == 'account.stopped')
    <div class="mb-20 alert alert-danger alert-icon">
        <i class="icon wb-alert-circle"></i>
        <p class="mb-0"><strong>Email Account Stopped</strong></p>
        @if($account->error_smtp_status)
            <p class="mb-0">Please fix the following SMTP issue then reauthenticate:</p>
            <p class="mb-20">{{$account->error_smtp_status}}</p>
        @else
            <p class="mb-20">Please reauthenticate to re-activate email account.</p>
        @endif
        <p>
            <a href="{{ route('email-accounts.reauth', $account)}}" class="btn btn-danger">
                <i class="icon wb-lock"></i> Re-authenticate
            </a>
        </p>
    </div>
@endif

@if($account->error == 'sync.timeout')
    <div class="mb-20 alert alert-danger alert-icon">
        <i class="icon wb-alert-circle"></i>
        <p class="mb-0"><strong>Email Account Stopped</strong></p>
        <p class="mb-20">
            Your account has been stopped due to consecutive failed sync attempts.
        </p>
            <a href="{{ route('email-accounts.reauth', $account)}}" class="btn btn-danger">
                <i class="icon wb-lock"></i> Re-authenticate
            </a>
        </p>
    </div>
@endif

@if ($account->error == 'sending.failed')
    <div class="mb-20 alert alert-danger alert-icon">
        <i class="icon wb-alert-circle"></i>
        <p class="mb-0"><strong>Too many send attempts failed</strong></p>
        @if ($account->emailEngineAccount->sync_state != 'running')
            <p class="mb-20">Please reauthenticate to re-activate email account.</p>
            <p>
                <a href="{{ route('email-accounts.reauth', $account) }}" class="btn btn-danger">
                    <i class="icon wb-lock"></i> Re-authenticate
                </a>
            </p>
        @else
            <p class="mb-20">
                Your account has been stopped due to consecutive failed send attempts.
            </p>
            <p>
                @if ($account->restart_count < 3)
                    <a href="{{ route('email-accounts.restart', $account) }}" class="btn btn-danger">
                        <i class="icon wb-play"></i> Restart
                    </a>
                @else
                    <a href="{{ route('email-accounts.reauth', $account) }}" class="btn btn-danger">
                        <i class="icon wb-lock"></i> Re-authenticate
                    </a>
                @endif
            </p>
        @endif
    </div>
@endif